# Documentation Viewer

A modern documentation site built with Next.js and shadcn/ui components that allows users to view and navigate files with proper formatting. The site supports three file types with different rendering approaches:

- **YAML files**: Display in raw format with syntax highlighting
- **Python files**: Display in raw format with syntax highlighting
- **Markdown files**: Render as formatted HTML

## Features

### 🗂️ Sidebar Navigation Component
- Collapsible hierarchical tree view of all files in the `content/` directory
- File icons and indicators for different file types (YAML, Python, Markdown)
- Expand/collapse directories functionality
- Highlight currently selected file
- Responsive design (collapsible on mobile devices)

### 📄 Main Content Display Area
- **YAML and Python files**: Raw content with syntax highlighting, line numbers, proper indentation, and color coding
- **Markdown files**: Parse and render as formatted HTML with GitHub-flavored markdown support
- File header showing current file path, type, size, and last modified date
- Copy-to-clipboard functionality for code files

### 🔍 Search Functionality
- Search bar to find files by name within the content directory
- Show search results with file paths and direct navigation
- Real-time search with fuzzy matching

### 📱 Responsive Design
- Clean, modern interface with good typography and spacing
- Consistent color scheme using shadcn/ui components
- Smooth transitions and hover effects
- Loading states and error handling
- Mobile-first responsive design

## Getting Started

### Prerequisites

- **Node.js** (version 18 or higher)
- **Bun** package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd yaml-viewer-v2
   ```

2. **Install dependencies**
   ```bash
   bun install
   ```

3. **Run the development server**
   ```bash
   bun run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## Project Structure

```
yaml-viewer-v2/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   │   └── files/         # File serving endpoints
│   ├── globals.css        # Global styles with Prism.js themes
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Main application page
├── components/            # React components
│   ├── ui/               # shadcn/ui components
│   ├── file-tree.tsx     # Hierarchical file navigation
│   ├── file-viewer.tsx   # Main content display
│   ├── file-search.tsx   # Search functionality
│   ├── markdown-renderer.tsx  # Markdown to HTML renderer
│   └── syntax-highlighter.tsx # Code syntax highlighting
├── content/              # Documentation files
│   ├── getting-started/  # Introduction and setup guides
│   ├── examples/         # Sample YAML, Python files
│   └── api/              # API documentation
├── lib/                  # Utility functions
│   ├── file-utils.ts     # File system operations
│   └── utils.ts          # General utilities
└── public/               # Static assets
```

## Adding Content

To add new documentation files:

1. **Create files in the `content/` directory**
   ```bash
   content/
   ├── your-section/
   │   ├── guide.md
   │   ├── config.yaml
   │   └── script.py
   ```

2. **Supported file formats**
   - **Markdown**: `.md`, `.markdown`
   - **YAML**: `.yaml`, `.yml`
   - **Python**: `.py`

3. **Organize in subdirectories**
   - Files are automatically organized in a hierarchical tree
   - Directory names become section headers
   - Files are sorted alphabetically within directories

4. **Markdown front matter support**
   ```markdown
   ---
   title: "Your Page Title"
   description: "Page description"
   author: "Your Name"
   ---

   # Your Content Here
   ```

## Technical Implementation

### Dependencies

- **Next.js 15**: React framework with App Router
- **shadcn/ui**: Modern UI component library
- **Tailwind CSS**: Utility-first CSS framework
- **Prism.js**: Syntax highlighting for code blocks
- **react-markdown**: Markdown parsing and rendering
- **gray-matter**: Front matter parsing for Markdown files

### Key Components

- **FileTree**: Hierarchical navigation with collapsible directories
- **FileViewer**: Main content area with file type detection
- **SyntaxHighlighter**: Code highlighting with copy functionality
- **MarkdownRenderer**: HTML rendering with custom styling
- **FileSearch**: Real-time file search with fuzzy matching

### API Endpoints

- `GET /api/files/structure` - Returns the file tree structure
- `GET /api/files/[...path]` - Returns file content and metadata

## Development

### Type Checking
```bash
tsc --noEmit
```

### Building for Production
```bash
bun run build
```

### Starting Production Server
```bash
bun run start
```

## Customization

### Styling
- Modify `app/globals.css` for global styles
- Customize Prism.js themes for syntax highlighting
- Update shadcn/ui component variants in `components/ui/`

### File Types
- Add new file type support in `lib/file-utils.ts`
- Update syntax highlighting in `components/syntax-highlighter.tsx`
- Add file icons in `components/file-tree.tsx`

### Search
- Enhance search functionality in `components/file-search.tsx`
- Add content-based search capabilities
- Implement search result highlighting

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.
