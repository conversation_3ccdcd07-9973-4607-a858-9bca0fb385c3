import { NextResponse } from 'next/server';
import { getFileContent } from '@/lib/server-file-utils';

export async function GET(
  _request: Request,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const { path } = await params;
    const filePath = path.join('/');

    console.log(`API: Loading file: ${filePath}`);

    if (!filePath) {
      console.log('API: No file path provided');
      return NextResponse.json(
        { error: 'File path is required' },
        { status: 400 }
      );
    }

    const fileContent = getFileContent(filePath);

    if (!fileContent) {
      console.log(`API: File not found: ${filePath}`);
      return NextResponse.json(
        { error: 'File not found or unsupported file type' },
        { status: 404 }
      );
    }

    console.log(`API: Successfully loaded file: ${filePath}`);
    return NextResponse.json(fileContent, {
      headers: {
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
      },
    });
  } catch (error) {
    console.error('Error loading file:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
