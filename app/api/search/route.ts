import { NextRequest, NextResponse } from 'next/server';
import { searchFiles } from '@/lib/server-file-utils';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const query = searchParams.get('q');
    
    if (!query || query.trim().length === 0) {
      return NextResponse.json(
        { error: 'Search query is required' },
        { status: 400 }
      );
    }

    const results = searchFiles(query.trim());
    
    return NextResponse.json({
      query: query.trim(),
      results,
      total: results.length
    });
  } catch (error) {
    console.error('Error searching files:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
