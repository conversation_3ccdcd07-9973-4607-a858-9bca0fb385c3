// Server-only file utilities - DO NOT IMPORT IN CLIENT COMPONENTS
import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { FileNode, FileContent, getFileTypeFromExtension } from './types';

export function getContentDirectory(): string {
  return path.join(process.cwd(), 'content');
}

export function buildFileTree(dirPath: string, basePath: string = ''): FileNode[] {
  const nodes: FileNode[] = [];
  
  try {
    const items = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const item of items) {
      if (item.name.startsWith('.')) continue;
      
      const itemPath = path.join(dirPath, item.name);
      const relativePath = path.join(basePath, item.name);
      
      if (item.isDirectory()) {
        const children = buildFileTree(itemPath, relativePath);
        if (children.length > 0) {
          nodes.push({
            name: item.name,
            path: relativePath,
            type: 'directory',
            children,
          });
        }
      } else if (item.isFile()) {
        const fileType = getFileTypeFromExtension(item.name);
        if (fileType) {
          nodes.push({
            name: item.name,
            path: relativePath,
            type: 'file',
            fileType,
          });
        }
      }
    }
  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error);
  }
  
  return nodes.sort((a, b) => {
    // Directories first, then files
    if (a.type !== b.type) {
      return a.type === 'directory' ? -1 : 1;
    }
    return a.name.localeCompare(b.name);
  });
}

export function getFileContent(filePath: string): FileContent | null {
  try {
    const contentDir = getContentDirectory();
    const fullPath = path.join(contentDir, filePath);
    
    // Security check: ensure the path is within the content directory
    const resolvedPath = path.resolve(fullPath);
    const resolvedContentDir = path.resolve(contentDir);
    if (!resolvedPath.startsWith(resolvedContentDir)) {
      throw new Error('Invalid file path');
    }
    
    const stats = fs.statSync(fullPath);
    const content = fs.readFileSync(fullPath, 'utf-8');
    const fileType = getFileTypeFromExtension(filePath);
    
    if (!fileType) {
      return null;
    }
    
    let metadata: Record<string, any> | undefined;
    let processedContent = content;
    
    // Extract front matter for markdown files
    if (fileType === 'markdown') {
      const parsed = matter(content);
      metadata = parsed.data;
      processedContent = parsed.content;
    }
    
    return {
      path: filePath,
      name: path.basename(filePath),
      type: fileType,
      content: processedContent,
      metadata,
      size: stats.size,
      lastModified: stats.mtime,
    };
  } catch (error) {
    console.error(`Error reading file ${filePath}:`, error);
    return null;
  }
}

export function searchFiles(query: string): FileNode[] {
  const contentDir = getContentDirectory();
  const results: FileNode[] = [];
  
  function searchInDirectory(dirPath: string, basePath: string = '') {
    try {
      const items = fs.readdirSync(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        if (item.name.startsWith('.')) continue;
        
        const itemPath = path.join(dirPath, item.name);
        const relativePath = path.join(basePath, item.name);
        
        if (item.isDirectory()) {
          searchInDirectory(itemPath, relativePath);
        } else if (item.isFile()) {
          const fileType = getFileTypeFromExtension(item.name);
          if (fileType && item.name.toLowerCase().includes(query.toLowerCase())) {
            results.push({
              name: item.name,
              path: relativePath,
              type: 'file',
              fileType,
            });
          }
        }
      }
    } catch (error) {
      console.error(`Error searching in directory ${dirPath}:`, error);
    }
  }
  
  searchInDirectory(contentDir);
  return results;
}

export function getAllFiles(): FileNode[] {
  const contentDir = getContentDirectory();
  return buildFileTree(contentDir);
}
