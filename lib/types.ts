// Client-side types that don't depend on Node.js modules

export interface FileNode {
  name: string;
  path: string;
  type: 'file' | 'directory';
  fileType?: 'markdown' | 'yaml' | 'python';
  children?: FileNode[];
}

export interface FileContent {
  path: string;
  name: string;
  type: 'markdown' | 'yaml' | 'python';
  content: string;
  metadata?: Record<string, any>;
  size: number;
  lastModified: Date;
}

export type FileType = 'markdown' | 'yaml' | 'python';

// Utility function that can be used on both client and server
export function getFileTypeFromExtension(filePath: string): FileType | null {
  const SUPPORTED_EXTENSIONS = {
    '.md': 'markdown' as const,
    '.markdown': 'markdown' as const,
    '.yaml': 'yaml' as const,
    '.yml': 'yaml' as const,
    '.py': 'python' as const,
  };

  const ext = filePath.split('.').pop()?.toLowerCase();
  if (!ext) return null;
  
  return SUPPORTED_EXTENSIONS[`.${ext}` as keyof typeof SUPPORTED_EXTENSIONS] || null;
}
