# Villiers.ai System Definitions

This directory contains domain-driven system definitions for the Villiers.ai private jet booking platform, organized using patterns inspired by advanced system architecture approaches.

## Directory Structure

### Core Domains

#### 🛩️ **booking/** - Booking & Trip Management
Complete booking lifecycle from trip planning to post-flight experience.

**Key Components:**
- `booking_lifecycle/` - Booking status management and workflow
- `trip_planning/` - Natural language trip requests and routing optimization
- `quotes/` - Multi-operator quote aggregation and comparison
- `empty_legs/` - Empty leg opportunities and bookings
- `shared_flights/` - Seat sharing and collaborative booking

**Primary Services:**
- `booking_service.py` (26KB) - Core booking orchestration
- `trips_service.py` (45KB) - Trip planning and management
- `quotes_service.py` (25KB) - Quote aggregation

**API Endpoints:**
- `/api/v1/bookings/*` - Complete booking management
- `/api/v1/trips/*` - Trip planning and search
- `/api/v1/quotes/*` - Quote management

#### 🏢 **operator/** - Operator Management
Operator relationships, reliability tracking, and communication.

**Key Components:**
- `operator_management/` - Operator onboarding and profile management
- `reliability/` - Performance tracking and reliability scoring
- `communication/` - Operator email processing and response handling

**Primary Services:**
- `operator_service.py` (13KB) - Operator CRUD operations
- `operator_communication_service.py` (39KB) - Email integration
- `operator_reliability_service.py` (21KB) - Performance tracking

#### ✈️ **aircraft/** - Aircraft Data Management
Aircraft specifications, tracking, and positioning.

**Key Components:**
- Aircraft database management
- Real-time positioning and tracking
- Aircraft image management
- Maintenance and availability tracking

**Primary Services:**
- `aircraft_service.py` (12KB) - Aircraft data management
- `aircraft_tracking_service.py` (14KB) - Real-time tracking
- `positioning_service.py` (33KB) - Aircraft positioning

#### 💳 **payment/** - Payment Processing
Secure payment processing, invoicing, and financial transactions.

**Key Components:**
- Stripe integration for card payments
- BTCPay integration for cryptocurrency
- Invoice generation and management
- Fee calculation and platform fees

**Primary Services:**
- `payment_service.py` (40KB) - Multi-provider payment processing
- `btcpay_service.py` (24KB) - Cryptocurrency payments
- `fee_calculator_service.py` (8.4KB) - Dynamic fee calculation

#### 💬 **communication/** - Multi-Channel Communication
Email, chat, notifications, and webhook management.

**Key Components:**
- `email/` - Email processing and template management
- `chat/` - Real-time chat with AI assistance
- `notifications/` - System notifications and alerts
- `webhooks/` - External service integrations

**Primary Services:**
- `email_service.py` (60KB) - Email processing
- `chat_service.py` (33KB) - Chat interface
- `notifications_service.py` (27KB) - Notification delivery

#### 🔐 **authentication/** - Authentication & Authorization
User authentication, session management, and security.

**Key Components:**
- Nostr-based authentication
- JWT token management
- User session handling
- Security and encryption

**Primary Services:**
- `auth_service.py` (30KB) - Authentication orchestration
- `nostr_auth_service.py` (13KB) - Nostr protocol integration
- `user_service.py` (34KB) - User management

### Supporting Domains

#### 🛠️ **admin/** - Administrative Interface
System administration, monitoring, and management tools.

#### 💰 **pricing/** - Pricing Intelligence
Dynamic pricing, cost estimation, and price optimization.

#### 📊 **analytics/** - Business Intelligence
Event tracking, reporting, and performance metrics.

#### 🔧 **data/** - Data Processing
Data enrichment, NLP processing, and external data integration.

#### 🏗️ **infrastructure/** - Core Infrastructure
Database management, API framework, monitoring, and scheduling.

## Key Architectural Patterns

### 1. Domain-Driven Design
- Each domain is self-contained with clear boundaries
- Services, models, and APIs are organized by business domain
- Cross-domain communication through well-defined interfaces

### 2. System Definition Pattern
Each domain contains:
- **`system`**: Unique identifier
- **`description`**: Clear purpose statement
- **`intent_assertions`**: High-level goals
- **`technical_assertions`**: Implementation requirements
- **`behavior`**: Expected operational patterns
- **`invariants`**: Conditions that must always be true
- **`forbidden_states`**: Conditions that must never occur
- **`depends_on`/`provides`**: Dependency mapping

### 3. Performance Contracts
Explicit response time requirements:
- Trip search: <2000ms
- Quote aggregation: <30000ms
- Chat responses: <2000ms
- Database queries: <200ms

### 4. Data Sovereignty
- All aircraft data from database (no hardcoding)
- All operator information dynamically loaded
- All pricing calculated from database rules
- PostgreSQL as single source of truth

## Codebase Analysis Summary

### API Endpoints (Total: 100+ endpoints)
- **Booking Domain**: 14 endpoint files
- **Communication**: 8 endpoint files  
- **Admin**: 12 endpoint files
- **Aircraft**: 5 endpoint files
- **Authentication**: 1 endpoint file

### Services (Total: 80+ services)
- **Core Business Logic**: 25 services
- **Communication**: 15 services
- **Data Processing**: 20 services
- **Infrastructure**: 10 services
- **Integration**: 10 services

### Database Models (Total: 35+ models)
- **Booking Domain**: 8 models
- **User Management**: 5 models
- **Aircraft & Operators**: 6 models
- **Communication**: 8 models
- **Analytics**: 8 models

### Database Schemas (Total: 50+ schemas)
- **Pydantic v2 compliant**
- **Forward reference handling**
- **Relationship mapping**
- **Validation rules**

## Implementation Guidelines

### 1. Creating New Domains
1. Create domain directory under `villiers_system_definitions/`
2. Add `{domain}.yaml` with complete system definition
3. Update `index.yaml` with domain reference
4. Define clear boundaries and dependencies

### 2. System Definition Template
```yaml
system: villiers_{domain}
description: "Clear description of domain purpose"
intent_assertions: [list of high-level goals]
technical_assertions: [file paths and requirements]
behavior: {operational patterns}
endpoints: {API endpoints}
invariants: [must-be-true conditions]
forbidden_states: [never-allowed conditions]
depends_on: [other domains]
provides: [services to other domains]
```

### 3. Enforcement Patterns
- Automated validation against system definitions
- Performance monitoring against defined contracts
- Security compliance checking
- Data consistency validation

## Usage with AI Development Tools

This structure provides rich context for AI development assistants:

### 1. Domain Context
Each domain definition provides:
- Clear scope and purpose
- Expected behaviors and constraints
- File locations and relationships
- Performance requirements

### 2. Architectural Guidance
- Cross-domain dependency mapping
- Service boundary definitions
- Data flow patterns
- Integration points

### 3. Development Patterns
- Consistent naming conventions
- Standard file organization
- Common architectural patterns
- Validation and testing approaches

## Next Steps

1. **Complete Domain Definitions**: Create YAML files for all domains
2. **Enforcement Implementation**: Build automated validation system
3. **Performance Monitoring**: Implement contract-based monitoring
4. **Documentation Integration**: Connect with existing API documentation
5. **CI/CD Integration**: Validate against definitions in deployment pipeline

## Files Overview

- `index.yaml` - Master system definition index
- `booking/booking.yaml` - Complete booking domain definition (example)
- `README.md` - This documentation file

## Benefits

1. **Clear Architecture**: Well-defined domain boundaries and responsibilities
2. **AI-Friendly**: Rich context for development assistant tools
3. **Validation-Ready**: Structure supports automated compliance checking
4. **Scalable**: Easy to add new domains and services
5. **Maintainable**: Self-documenting system with clear contracts 