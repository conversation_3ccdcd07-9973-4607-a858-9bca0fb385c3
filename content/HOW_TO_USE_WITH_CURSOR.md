# 🚀 How to Use Villiers.ai System Definitions with Cursor

## 📋 What You Now Have

After running `cursor_integration.py`, you now have:

### ✅ **Generated Assets**
- **8 Domain-Specific Prompt Directories** in `cursor_prompts/`
  - `aircraft/` - Aircraft fleet management prompts
  - `airport/` - Airport data and routing prompts  
  - `analytics/` - Business intelligence prompts
  - `authentication/` - User auth and security prompts
  - `booking/` - Complete booking lifecycle prompts
  - `communication/` - Multi-channel communication prompts
  - `core/` - Infrastructure and utilities prompts
  - `operator/` - Operator management prompts

### 📁 **Each Domain Contains**
- `health_check.md` - Domain-specific system validation prompts
- `implementation_template.md` - Feature development prompts
- `bug_fix_template.md` - Debugging and fixing prompts

### 🌐 **System-Wide Assets**
- `system_health_check.md` - Complete platform health validation
- `CURSOR_USAGE_GUIDE.md` - Comprehensive usage patterns
- `.cursor/rules/system-definitions.mdc` - Cursor rules integration

---

## 🎯 **How to Use These with Cursor**

### **1. Quick Health Check**
Copy and paste this into Cursor:
```
Based on all system definitions in `villiers_system_definitions/`, please perform a comprehensive health check:

1. **Domain Integrity**: Verify each domain's technical assertions are met
2. **Performance Compliance**: Check all performance requirements are satisfied  
3. **Dependency Validation**: Ensure cross-domain dependencies are properly managed
4. **Invariant Maintenance**: Confirm all system invariants are preserved
5. **Forbidden State Detection**: Check for any forbidden states
6. **Architecture Compliance**: Validate domain-driven design principles

Report any violations and suggest specific fixes based on the system definitions.
```

### **2. Domain-Specific Development**

#### **🏢 Operator Domain Work**
```
Using `villiers_system_definitions/operator/` as context, I need to [YOUR_TASK]. This domain manages operator relationships, reliability tracking with multi-factor scoring, and persona-driven communication with 89% confidence quote extraction.

Key requirements to maintain:
- Operator records must have unique identification with consistent naming conventions
- Fleet relationships must be validated with active aircraft associations
- Performance metrics must use standardized algorithms with consistent weighting
- Reliability scores must be bounded between 0.0 and 1.0
- Communication must respect opt-out preferences with compliance monitoring
```

#### **📚 Booking Domain Work**
```
Based on `villiers_system_definitions/booking/`, help me [YOUR_TASK]. This domain handles the complete booking lifecycle from trip planning to post-flight completion, with quote orchestration completing within 30000ms and trip searches within 2000ms.

Key requirements to maintain:
- Multi-leg trip planning with intelligent route optimization
- Advanced shared flight marketplace with seat-level booking
- Comprehensive empty leg management with automated solicitation
- Sophisticated quote orchestration with AI-powered processing
- Zero booking failures through automated recovery workflows
```

#### **✈️ Aircraft Domain Work**
```
Reference `villiers_system_definitions/aircraft/` for [YOUR_TASK]. This domain encompasses fleet management, real-time positioning, image management, and maintenance scheduling with database-driven aircraft data.

Key requirements:
- All aircraft data must come from database (no hardcoding)
- Real-time positioning with ADS-B integration
- Image management with automated processing
- Maintenance scheduling with availability coordination
```

### **3. Cross-Domain Integration**
```
I need to integrate [DOMAIN_A] with [DOMAIN_B]. Based on the system definitions:
1. Review the dependency mappings between domains in `villiers_system_definitions/[DOMAIN_A]/` and `villiers_system_definitions/[DOMAIN_B]/`
2. Identify proper integration points
3. Maintain domain boundaries and encapsulation
4. Implement with proper error handling and monitoring
```

### **4. Performance-Driven Development**
```
Optimize [SERVICE] performance based on requirements in `villiers_system_definitions/[DOMAIN]/`. 

Performance contracts to maintain:
- Trip search: <2000ms ✓
- Quote aggregation: <30000ms ✓
- Chat responses: <2000ms ✓  
- Database queries: <200ms ✓
- API health checks: <100ms ✓

Current performance: [YOUR_METRICS]
Target: [CONTRACT_REQUIREMENT]
```

### **5. Bug Investigation & Fixing**
```
I'm experiencing [ERROR_DESCRIPTION] in the [DOMAIN] domain. Using `villiers_system_definitions/[DOMAIN]/` as reference:

1. Analyze what the expected behavior should be according to the system definition
2. Identify violations of intent assertions or invariants  
3. Check for performance requirement breaches
4. Implement a fix that maintains architectural integrity

Expected behavior based on system definition:
[PASTE_RELEVANT_SECTION_FROM_DOMAIN_YAML]
```

---

## 🛠️ **Advanced Usage Patterns**

### **Automated Prompt Generation**
Run the script whenever you need fresh prompts:
```bash
cd villiers_system_definitions
python cursor_integration.py
```

### **Custom Prompt Creation**
Use the script interactively:
```python
from cursor_integration import CursorSystemDefinitionAssistant

assistant = CursorSystemDefinitionAssistant()

# Generate custom prompts
health_check = assistant.generate_health_check_prompt("operator")
implementation = assistant.generate_implementation_prompt("booking", "multi-leg trip optimization")
bug_fix = assistant.generate_bug_fix_prompt("aircraft", "positioning service timeout errors")
integration = assistant.generate_integration_prompt("booking", "operator", "quote aggregation")

print(health_check)
```

### **System Evolution Workflow**
1. **Update System Definitions** - Modify YAML files as system evolves
2. **Regenerate Prompts** - Run `cursor_integration.py` 
3. **Update Cursor Rules** - Ensure `.cursor/rules/system-definitions.mdc` is current
4. **Validate Changes** - Use health check prompts to verify compliance

---

## 📊 **Domain-Specific Use Cases**

### **🔐 Authentication Domain**
```
Using `villiers_system_definitions/authentication/` context, assist with [TASK]. This domain owns all user identity, passwordless authentication with BIP39 mnemonic keys, and session management.

Key patterns:
- Nostr-based authentication with WebAuthn fallback
- BIP39 mnemonic key generation and recovery
- JWT token management with secure refresh cycles
- Session management with device fingerprinting
```

### **💬 Communication Domain**  
```
Based on `villiers_system_definitions/communication/`, help with [TASK]. This domain handles multi-channel communication, email processing, chat interfaces, and notification orchestration.

Key capabilities:
- Multi-channel message delivery (email, SMS, push, webhook)
- Email thread analysis with AI-powered conversation tracking
- Real-time chat with context-aware responses
- Notification orchestration with preference management
```

### **📊 Analytics Domain**
```
Reference `villiers_system_definitions/analytics/` for [TASK]. This domain provides comprehensive analytics and business intelligence for data-driven decision making.

Key features:
- Event tracking with privacy-compliant data collection
- User behavior analysis with conversion optimization
- Performance monitoring with real-time alerting
- Business intelligence reporting with automated insights
```

---

## 🚨 **Emergency Procedures**

### **System Recovery**
```
System recovery procedure based on definitions:
1. Identify violated invariants or forbidden states using `villiers_system_definitions/`
2. Reference recovery procedures in relevant domain system definitions
3. Implement fixes maintaining all architectural contracts
4. Validate system integrity post-recovery using health check prompts
```

### **Performance Crisis**
```
Performance issue in [DOMAIN] - using `villiers_system_definitions/[domain]/` contracts:

Current metrics: [YOUR_METRICS]
Contract requirements: [FROM_SYSTEM_DEFINITION]
Violation severity: [HIGH/MEDIUM/LOW]

Immediate actions:
1. Check technical assertions for performance bottlenecks
2. Validate database query patterns against <200ms requirement
3. Review caching strategies and connection pooling
4. Implement circuit breakers for external dependencies
```

---

## 💡 **Best Practices**

### **1. Always Start with Context**
Begin every Cursor session by loading the relevant system definition context:
```
"Load the context from `villiers_system_definitions/[domain]/` for today's work on [feature/bug/optimization]"
```

### **2. Reference Specific Requirements**
Always cite specific requirements from system definitions:
```
"According to the technical assertions in `villiers_system_definitions/booking/booking.yaml`, the quote orchestration must complete within 30000ms..."
```

### **3. Maintain Architectural Integrity**
```
"Ensure this implementation follows the domain boundaries defined in `villiers_system_definitions/` and doesn't violate any cross-domain constraints"
```

### **4. Validate Against Contracts**
```
"Validate this implementation meets all performance contracts and invariants specified in the system definition"
```

### **5. Keep Definitions Updated**
- Update YAML files as system evolves
- Regenerate prompts regularly
- Sync with actual implementation
- Document architectural decisions

---

## 🎉 **Ready to Use!**

You now have a **powerful AI-assisted development system** that:

✅ **Provides context-rich prompts** based on your actual architecture  
✅ **Maintains consistency** across all development work  
✅ **Enforces performance contracts** and architectural patterns  
✅ **Enables rapid debugging** with system-definition-driven analysis  
✅ **Supports system evolution** with automated prompt generation  

**Start using any of the prompts above with Cursor and experience architecture-aware AI development!** 