#!/usr/bin/env python3
"""
Cursor Integration Script for Villiers.ai System Definitions
==========================================================
This script generates Cursor-specific prompts and analysis based on
the system definitions to assist with development, debugging, and maintenance.
"""

import yaml
import os
from pathlib import Path
from datetime import datetime
import json

class CursorSystemDefinitionAssistant:
    """Assistant for integrating system definitions with Cursor AI"""
    
    def __init__(self, definitions_path="."):
        self.definitions_path = Path(definitions_path)
        self.domains = self._load_all_domains()
    
    def _load_all_domains(self):
        """Load all domain definitions"""
        domains = {}
        for domain_dir in self.definitions_path.iterdir():
            if (domain_dir.is_dir() and 
                not domain_dir.name.startswith('.') and 
                domain_dir.name not in ['__pycache__', 'cursor_prompts', 'villiers_system_definitions']):
                domain_data = self._load_domain(domain_dir)
                if domain_data:  # Only add if we successfully loaded data
                    domains[domain_dir.name] = domain_data
        return domains
    
    def _load_domain(self, domain_path):
        """Load a specific domain's system definitions"""
        domain_data = {}
        
        # Load main domain file
        main_file = domain_path / f"{domain_path.name}.yaml"
        if main_file.exists():
            try:
                with open(main_file, 'r') as f:
                    domain_data['main'] = yaml.safe_load(f)
            except Exception as e:
                print(f"Warning: Could not load {main_file}: {e}")
                return None
        else:
            return None  # Skip domains without main files
        
        # Load subdomain files
        for yaml_file in domain_path.glob("**/*.yaml"):
            if yaml_file.name != f"{domain_path.name}.yaml":
                try:
                    relative_path = yaml_file.relative_to(domain_path)
                    key = str(relative_path).replace('.yaml', '').replace('/', '_')
                    with open(yaml_file, 'r') as f:
                        domain_data[key] = yaml.safe_load(f)
                except Exception as e:
                    print(f"Warning: Could not load {yaml_file}: {e}")
        
        return domain_data
    
    def generate_health_check_prompt(self, domain=None):
        """Generate Cursor prompt for system health checking"""
        if domain:
            domain_info = self.domains.get(domain, {})
            prompt = f"""
# System Health Check for {domain.title()} Domain

Based on the system definition in `villiers_system_definitions/{domain}/`, please:

## 1. Technical Assertions Validation
Check that all technical assertions are properly implemented:
"""
            if 'main' in domain_info and 'technical_assertions' in domain_info['main']:
                for assertion in domain_info['main']['technical_assertions']:
                    if isinstance(assertion, dict) and 'path' in assertion:
                        prompt += f"- Verify {assertion['path']} meets its purpose: {assertion.get('purpose', 'N/A')}\n"
            
            prompt += f"""
## 2. Performance Requirements
Validate performance contracts:
"""
            if 'main' in domain_info and 'behavior' in domain_info['main']:
                behavior = domain_info['main']['behavior']
                if behavior and 'performance_requirements' in behavior:
                    for req in behavior['performance_requirements']:
                        prompt += f"- {req}\n"
            
            prompt += f"""
## 3. Invariants Check
Ensure these conditions are always maintained:
"""
            if 'main' in domain_info and 'invariants' in domain_info['main']:
                for invariant in domain_info['main']['invariants']:
                    prompt += f"- {invariant}\n"
                    
            return prompt
        else:
            return """
# Complete System Health Check

Based on all system definitions in `villiers_system_definitions/`, please perform a comprehensive health check:

1. **Domain Integrity**: Verify each domain's technical assertions are met
2. **Performance Compliance**: Check all performance requirements are satisfied  
3. **Dependency Validation**: Ensure cross-domain dependencies are properly managed
4. **Invariant Maintenance**: Confirm all system invariants are preserved
5. **Forbidden State Detection**: Check for any forbidden states
6. **Architecture Compliance**: Validate domain-driven design principles

Report any violations and suggest specific fixes based on the system definitions.
"""
    
    def generate_implementation_prompt(self, domain, feature_description):
        """Generate implementation prompt based on domain definition"""
        domain_info = self.domains.get(domain, {})
        
        prompt = f"""
# Implement {feature_description} in {domain.title()} Domain

Based on the system definition in `villiers_system_definitions/{domain}/`:

## Domain Intent
This domain is responsible for:
"""
        if 'main' in domain_info and 'description' in domain_info['main']:
            prompt += f"{domain_info['main']['description']}\n\n"
        
        prompt += "## Intent Assertions to Maintain\n"
        if 'main' in domain_info and 'intent_assertions' in domain_info['main']:
            for assertion in domain_info['main']['intent_assertions']:
                prompt += f"- {assertion}\n"
        
        prompt += f"""
## Implementation Requirements
1. Follow the established technical patterns in this domain
2. Maintain all performance requirements
3. Ensure proper error handling using domain-specific patterns
4. Preserve all invariant conditions
5. Follow dependency management rules

## Feature Implementation
Please implement: {feature_description}

Ensure the implementation:
- Aligns with the domain's architectural patterns
- Follows existing service and repository patterns  
- Maintains performance contracts
- Includes proper error handling
- Adds appropriate tests
"""
        return prompt
    
    def generate_bug_fix_prompt(self, domain, error_description):
        """Generate bug fixing prompt with system definition context"""
        domain_info = self.domains.get(domain, {})
        
        prompt = f"""
# Fix Bug in {domain.title()} Domain

## Error Description
{error_description}

## System Definition Context
Based on `villiers_system_definitions/{domain}/`, this domain should:

### Expected Behavior
"""
        if 'main' in domain_info and 'behavior' in domain_info['main']:
            behavior = domain_info['main']['behavior']
            if behavior:
                for key, value in behavior.items():
                    if isinstance(value, list):
                        prompt += f"\n#### {key.replace('_', ' ').title()}\n"
                        for item in value:
                            prompt += f"- {item}\n"
        
        prompt += """
### Debugging Steps
1. Check if the error violates any intent assertions
2. Verify technical assertions are properly implemented
3. Validate performance requirements are met
4. Check for forbidden states
5. Ensure proper error handling patterns

### Fix Requirements
- Maintain all domain invariants
- Follow established error handling patterns
- Preserve performance contracts
- Ensure proper logging and monitoring
"""
        return prompt
    
    def generate_integration_prompt(self, source_domain, target_domain, integration_type):
        """Generate cross-domain integration prompt"""
        source_info = self.domains.get(source_domain, {})
        target_info = self.domains.get(target_domain, {})
        
        prompt = f"""
# Cross-Domain Integration: {source_domain.title()} → {target_domain.title()}

## Integration Type: {integration_type}

## Source Domain ({source_domain.title()})
"""
        if 'main' in source_info and 'provides' in source_info['main']:
            prompt += "### Services Provided:\n"
            for service in source_info['main']['provides']:
                prompt += f"- {service}\n"
        
        prompt += f"""
## Target Domain ({target_domain.title()})
"""
        if 'main' in target_info and 'depends_on' in target_info['main']:
            prompt += "### Dependencies:\n"
            for dep in target_info['main']['depends_on']:
                prompt += f"- {dep}\n"
        
        prompt += """
## Integration Requirements
1. Maintain domain boundaries and encapsulation
2. Use proper abstraction layers for communication
3. Implement proper error handling across domains
4. Ensure performance requirements of both domains are met
5. Add appropriate monitoring and logging
6. Follow dependency injection patterns

Please implement the integration ensuring both domains maintain their architectural integrity.
"""
        return prompt
    
    def save_cursor_prompts(self, output_dir="cursor_prompts"):
        """Save generated prompts to files for easy access"""
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # Generate health check prompts
        with open(output_path / "system_health_check.md", 'w') as f:
            f.write(self.generate_health_check_prompt())
        
        # Generate domain-specific prompts
        for domain in self.domains.keys():
            domain_dir = output_path / domain
            domain_dir.mkdir(exist_ok=True)
            
            # Health check
            with open(domain_dir / "health_check.md", 'w') as f:
                f.write(self.generate_health_check_prompt(domain))
            
            # Implementation template
            with open(domain_dir / "implementation_template.md", 'w') as f:
                f.write(self.generate_implementation_prompt(domain, "[FEATURE_DESCRIPTION]"))
            
            # Bug fix template
            with open(domain_dir / "bug_fix_template.md", 'w') as f:
                f.write(self.generate_bug_fix_prompt(domain, "[ERROR_DESCRIPTION]"))
        
        print(f"Cursor prompts saved to {output_path}/")

def main():
    """Main function to demonstrate usage"""
    assistant = CursorSystemDefinitionAssistant()
    
    print("🤖 Villiers.ai Cursor Integration Assistant")
    print("==========================================")
    print(f"Loaded {len(assistant.domains)} domains")
    
    # Generate example prompts
    print("\nGenerating Cursor prompt templates...")
    assistant.save_cursor_prompts()
    
    # Example: Generate health check for operator domain
    print("\nExample Health Check Prompt for Operator Domain:")
    print("-" * 50)
    print(assistant.generate_health_check_prompt("operator"))

if __name__ == "__main__":
    main() 