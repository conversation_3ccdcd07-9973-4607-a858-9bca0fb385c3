# Cursor Usage Guide for Villiers.ai System Definitions

## Quick Start Commands

### 🔍 System Health Check
```
"Perform a comprehensive health check of the Villiers.ai system based on all definitions in `villiers_system_definitions/`. For each domain, verify:
1. Technical assertions are properly implemented
2. Performance requirements are met  
3. Invariants are maintained
4. No forbidden states exist
5. Dependencies are correctly managed"
```

### 🚀 Feature Implementation
```
"I need to implement [FEATURE] in the [DOMAIN] domain. Based on `villiers_system_definitions/[DOMAIN]/`:
1. Load the domain context and intent assertions
2. Follow the established technical patterns
3. Maintain all performance requirements
4. Ensure proper error handling
5. Add comprehensive tests"
```

### 🐛 Bug Investigation & Fix
```
"I'm experiencing [ERROR] in the [DOMAIN] domain. Using `villiers_system_definitions/[DOMAIN]/` as reference:
1. Analyze what the expected behavior should be
2. Identify violations of intent assertions or invariants
3. Check for performance requirement breaches
4. Implement a fix that maintains architectural integrity"
```

### 🔗 Cross-Domain Integration
```
"I need to integrate [DOMAIN_A] with [DOMAIN_B]. Based on the system definitions:
1. Review the dependency mappings between domains
2. Identify proper integration points
3. Maintain domain boundaries and encapsulation
4. Implement with proper error handling and monitoring"
```

## Domain-Specific Commands

### 📚 Booking Domain
```
"Based on `villiers_system_definitions/booking/`, help me with [task]. This domain handles the complete booking lifecycle from trip planning to post-flight completion, with quote orchestration completing within 30000ms and trip searches within 2000ms."
```

### 🏢 Operator Domain  
```
"Using `villiers_system_definitions/operator/` as context, assist with [task]. This domain manages operator relationships, reliability tracking with multi-factor scoring, and persona-driven communication with 89% confidence quote extraction."
```

### ✈️ Aircraft Domain
```
"Reference `villiers_system_definitions/aircraft/` for [task]. This domain encompasses fleet management, real-time positioning, image management, and maintenance scheduling with database-driven aircraft data."
```

### 💬 Communication Domain
```
"Based on `villiers_system_definitions/communication/`, help with [task]. This domain handles multi-channel communication, email processing, chat interfaces, and notification orchestration across the platform."
```

### 🔐 Authentication Domain
```
"Using `villiers_system_definitions/authentication/` context, assist with [task]. This domain owns all user identity, passwordless authentication with BIP39 mnemonic keys, and session management."
```

## Advanced Usage Patterns

### 🔄 Continuous System Validation
```
"Set up continuous validation against system definitions:
1. Monitor all services for performance contract compliance
2. Validate architectural patterns remain consistent
3. Check for dependency violations
4. Alert on invariant breaches"
```

### 📊 Implementation Gap Analysis
```
"Analyze implementation gaps across all domains by comparing current codebase with system definitions in `villiers_system_definitions/`. Generate a prioritized action plan for missing features and architectural improvements."
```

### 🏗️ Architectural Refactoring
```
"Guide architectural refactoring of [component] to align with system definitions:
1. Identify current deviations from defined patterns
2. Plan migration path maintaining system stability  
3. Implement changes preserving all contracts
4. Validate against domain requirements"
```

## Performance-Driven Development

### ⚡ Performance Contract Validation
```
"Validate performance against system definition contracts:
- Trip search: <2000ms ✓
- Quote aggregation: <30000ms ✓
- Chat responses: <2000ms ✓  
- Database queries: <200ms ✓
- API health checks: <100ms ✓"
```

### 📈 Performance Optimization
```
"Optimize [service] performance based on requirements in `villiers_system_definitions/[domain]/`. Current performance: [metrics]. Target: [contract]. Implement optimizations while maintaining all domain invariants."
```

## Error Handling & Recovery

### 🛡️ System Definition-Driven Error Handling
```
"Implement comprehensive error handling for [service] following patterns defined in `villiers_system_definitions/[domain]/`. Ensure:
1. Proper exception types for each error scenario
2. Graceful degradation strategies
3. Monitoring and alerting integration
4. Recovery procedures alignment"
```

### 🔧 Self-Healing Implementation
```
"Design self-healing mechanisms for [service] based on system definition requirements:
1. Automatic detection of invariant violations
2. Recovery procedures for forbidden states
3. Performance degradation responses
4. Dependency failure handling"
```

## Testing & Validation

### 🧪 System Definition-Based Testing
```
"Generate comprehensive tests for [domain] based on `villiers_system_definitions/[domain]/`:
1. Unit tests for all technical assertions
2. Integration tests for cross-domain dependencies
3. Performance tests for contract validation
4. Error scenario tests for edge cases"
```

### ✅ Compliance Validation
```
"Validate [implementation] complies with system definition requirements:
1. All intent assertions satisfied
2. Technical patterns followed correctly
3. Performance contracts met
4. Security constraints enforced
5. Data sovereignty rules maintained"
```

## Maintenance & Evolution

### 🔄 System Evolution
```
"Plan system evolution for [new requirement] while maintaining compatibility with existing system definitions. Update relevant definitions and ensure backward compatibility."
```

### 📚 Documentation Synchronization
```
"Ensure system definitions remain synchronized with implementation. Update `villiers_system_definitions/` to reflect current architecture and validate all references are accurate."
```

## Best Practices

1. **Always Reference System Definitions**: Start every development task by loading the relevant system definition context
2. **Maintain Architectural Integrity**: Ensure all changes align with domain boundaries and contracts
3. **Performance First**: Validate against performance requirements throughout development
4. **Security Compliance**: Follow security constraints and data sovereignty rules
5. **Cross-Domain Coordination**: Use dependency mappings for proper integration
6. **Continuous Validation**: Regularly check implementation against definitions
7. **Evolution Management**: Update definitions as system evolves

## Emergency Procedures

### 🚨 System Recovery
```
"System recovery procedure based on definitions:
1. Identify violated invariants or forbidden states
2. Reference recovery procedures in system definitions
3. Implement fixes maintaining all contracts
4. Validate system integrity post-recovery"
```

### 🔍 Root Cause Analysis
```
"Perform root cause analysis using system definitions:
1. Map error to domain boundaries
2. Check for contract violations
3. Identify dependency failures
4. Reference expected behavior patterns"
``` 