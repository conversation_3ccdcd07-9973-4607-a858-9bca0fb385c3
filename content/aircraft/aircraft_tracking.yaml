system: aircraft_tracking
description: "Aircraft Real-time Tracking Subsystem - Advanced ADS-B integration with intelligent polling, position caching, and fleet monitoring for continuous aircraft location awareness"

intent_assertions:
- Real-time aircraft position tracking with sub-10-minute accuracy
- Intelligent API rate limiting to stay within ADS-B Exchange monthly limits
- Priority-based tracking with active flights getting frequent updates
- Comprehensive position history with intelligent data retention
- Spatial indexing for efficient geographic queries
- Integration with fleet management for operational insights

technical_assertions:
- path: /app/services/aircraft_tracking_service.py
  desc: "Core tracking service with intelligent polling and fleet monitoring"
  critical: true
- path: /app/services/adsb_service.py
  desc: "ADS-B Exchange API integration with rate limiting and caching"
  critical: true
- path: /app/db/models/aircraft_position.py
  desc: "Position data models with spatial indexing and GeoJSON support"
  critical: true
- path: /app/tasks/aircraft_tracking.py
  desc: "Scheduled tasks for position cleanup and API management"
  critical: true

behavior:
intelligent_polling:
  - "Active flights tracked every 3 minutes for real-time updates"
  - "Inactive aircraft polled every 22 minutes to conserve API calls"
  - "Priority-based scheduling based on flight status and recency"
  - "Automatic rate limiting with 30-second minimum intervals"

data_management:
  - "Position cache with 10-minute TTL for API optimization"
  - "Intelligent data retention: 24hr full, 7-day hourly, 30-day daily"
  - "Automated cleanup of position data older than 1 year"
  - "Spatial indexing for efficient geographic searches"

invariants:
- "API calls must not exceed 8,000 per month (266 per day)"
- "Position data must include valid coordinates and timestamps"
- "Aircraft must have valid ICAO hex codes for tracking"
- "Cache TTL must be respected to prevent stale data usage"

depends_on:
- aircraft: "Aircraft specifications and registration data"
- external_adsb_exchange: "Real-time position data via ADS-B Exchange API"

provides:
- real_time_positions: "Current aircraft locations with accuracy metadata"
- position_history: "Historical position data for analysis and reporting"
- fleet_status: "Active/inactive flight status for entire fleet"
- spatial_queries: "Geographic search capabilities for aircraft positioning" 