system: aircraft_availability_maintenance
description: "Aircraft Availability and Maintenance Management - Real-time availability tracking, maintenance scheduling, conflict detection, and operational status management integrated with booking lifecycle"

intent_assertions:
- Real-time aircraft availability tracking synchronized with booking system
- Predictive maintenance scheduling with conflict avoidance
- Automated availability conflict detection and resolution
- Maintenance window management with operational impact assessment
- Integration with operator schedules and owner preferences
- Historical availability analytics for fleet optimization

technical_assertions:
- path: /app/db/models/aircraft.py
  desc: "AvailabilityStatus model for tracking aircraft operational status"
  critical: true
- path: /app/db/models/availability.py
  desc: "Availability patterns and maintenance scheduling models"
  critical: true
- path: /app/services/aircraft_service.py
  desc: "Availability management within core aircraft service"
  critical: true

behavior:
availability_tracking:
  - "Real-time status updates (available, booked, maintenance, reserved)"
  - "Time-based availability periods with start/end timestamps"
  - "Location-aware availability with departure/arrival airports"
  - "Integration with booking system for automatic status updates"

maintenance_management:
  - "Scheduled maintenance tracking with due dates and intervals"
  - "Maintenance type categorization (routine, major, emergency)"
  - "Conflict detection with existing bookings and availability"
  - "Maintenance history tracking with performed dates and notes"

conflict_resolution:
  - "Automated detection of availability conflicts"
  - "Booking vs maintenance schedule conflict alerts"
  - "Alternative aircraft suggestions for conflicted periods"
  - "Operator notification system for critical conflicts"

operational_analytics:
  - "Aircraft utilization rate calculations"
  - "Maintenance schedule optimization recommendations"
  - "Availability pattern analysis for fleet planning"
  - "Downtime impact assessment and cost analysis"

invariants:
- "Availability periods cannot overlap for the same aircraft"
- "Maintenance schedules must not conflict with confirmed bookings"
- "Aircraft status must be consistent across all systems"
- "Availability updates must include valid timestamps and locations"

forbidden_states:
- "Overlapping availability periods for the same aircraft"
- "Maintenance scheduled during confirmed booking periods"
- "Availability status without proper audit trail"
- "Maintenance records without required safety documentation"

depends_on:
- aircraft: "Aircraft specifications and operational data"
- booking: "Booking lifecycle and reservation management"
- operator: "Operator schedules and maintenance requirements"
- airport: "Airport references for location-based availability"

provides:
- availability_status: "Real-time aircraft availability for booking systems"
- maintenance_schedules: "Predictive maintenance planning and tracking"
- conflict_alerts: "Automated conflict detection and resolution suggestions"
- utilization_analytics: "Aircraft utilization and performance metrics"

enforcement_hooks:
pre_availability_update:
  - "Validate availability periods do not conflict with existing bookings"
  - "Check maintenance schedules for potential conflicts"
  - "Ensure location information is valid and accessible"

pre_maintenance_schedule:
  - "Verify no confirmed bookings during proposed maintenance window"
  - "Validate maintenance requirements and safety compliance"
  - "Check operator approval for maintenance scheduling"

post_status_change:
  - "Update dependent systems with availability changes"
  - "Notify relevant stakeholders of status updates"
  - "Log status changes for audit and analytics purposes"

monitoring:
availability_metrics:
  - "Track aircraft availability percentage and utilization rates"
  - "Monitor maintenance schedule adherence and delays"
  - "Measure conflict resolution effectiveness and response times" 