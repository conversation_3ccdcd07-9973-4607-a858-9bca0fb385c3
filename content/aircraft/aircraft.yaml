system: aircraft
description: "Villiers.ai Aircraft Domain - Comprehensive aircraft fleet management system encompassing aircraft specifications, real-time positioning, availability tracking, image management, repositioning optimization, empty leg opportunities, and maintenance scheduling for private jet charter operations"

intent_assertions:
- Must provide complete aircraft fleet visibility with real-time positioning and status
- Automated aircraft tracking through ADS-B Exchange integration with intelligent polling
- Dynamic empty leg opportunity detection and marketing optimization
- Comprehensive aircraft specification database with detailed performance metrics
- Real-time availability management synchronized with booking lifecycle
- Advanced aircraft image management with compression and metadata tracking
- Intelligent repositioning cost calculations and optimization algorithms
- Multi-operator fleet management with operator-specific configurations
- Aircraft maintenance tracking with predictive scheduling capabilities
- Integration with booking system for seamless aircraft assignment and scheduling

technical_assertions:
# Core Aircraft Management
- path: /app/api/v1/endpoints/aircraft/aircraft.py
  desc: "Primary aircraft API endpoints (558 lines) - CRUD operations, search, availability checking"
  critical: true
- path: /app/services/aircraft_service.py
  desc: "Core aircraft business logic service (319 lines) using centralized database manager pattern"
  critical: true
- path: /app/db/models/aircraft.py
  desc: "Aircraft database models (330 lines) - AircraftType, Aircraft, AircraftCategory, AircraftImage"
  critical: true
- path: /app/db/schemas/aircraft.py
  desc: "Aircraft API schemas (368 lines) with forward references and relationship handling"
  critical: true

# Aircraft Positioning & Tracking
- path: /app/api/v1/endpoints/aircraft/positioning.py
  desc: "Aircraft positioning API endpoints (208 lines) - current positions, movements, empty legs"
  critical: true
- path: /app/services/aircraft_tracking_service.py
  desc: "Aircraft tracking service (341 lines) with intelligent polling and fleet monitoring"
  critical: true
- path: /app/services/positioning_service.py
  desc: "Positioning service (695 lines) with cost calculations and movement management"
  critical: true
- path: /app/services/adsb_service.py
  desc: "ADS-B Exchange integration service (460 lines) with rate limiting and caching"
  critical: true
- path: /app/db/models/aircraft_position.py
  desc: "Aircraft position tracking models (184 lines) with spatial indexing"
  critical: true
- path: /app/db/models/positioning.py
  desc: "Positioning models (218 lines) - AircraftPositioning, AircraftMovement, AircraftAvailability"
  critical: true

# Aircraft Repositioning & Empty Legs
- path: /app/api/v1/endpoints/aircraft/repositioning.py
  desc: "Aircraft repositioning API endpoints (157 lines) - empty leg opportunities and bookings"
  critical: true

# Aircraft Images & Media
- path: /app/services/aircraft_images_service.py
  desc: "Aircraft image management service (484 lines) with compression and storage"
  critical: true

# Scheduled Tasks & Automation
- path: /app/tasks/aircraft_tracking.py
  desc: "Aircraft tracking scheduled tasks (131 lines) - position cleanup and API counter reset"
  critical: true

behavior:
# Aircraft Data Management
aircraft_specification_management:
  - "Maintain comprehensive aircraft type database with performance specifications"
  - "Support aircraft categories (Light, Midsize, Super-midsize, Large) with operational constraints"
  - "Track individual aircraft with operator assignments and maintenance schedules"
  - "Manage aircraft images with automatic compression and metadata extraction"

# Real-time Position Tracking
position_tracking_workflow:
  - "Continuously monitor aircraft positions through ADS-B Exchange API integration"
  - "Implement intelligent polling with priority-based updates (active flights every 3 minutes)"
  - "Cache position data for 10 minutes to optimize API usage within monthly limits"
  - "Store position history with intelligent data retention (24hr full, 7-day hourly, 30-day daily)"
  - "Track nearest airports and calculate positioning costs automatically"

# Empty Leg & Repositioning
empty_leg_optimization:
  - "Automatically detect empty leg opportunities from aircraft movements"
  - "Calculate optimal repositioning routes and costs"
  - "Market empty legs with dynamic discount pricing"
  - "Synchronize with booking system for availability conflicts"

# Fleet Availability Management
availability_management:
  - "Real-time aircraft availability tracking synchronized with booking lifecycle"
  - "Support maintenance windows and operator-specific constraints"
  - "Automated conflict detection and resolution suggestions"
  - "Integration with quote generation for accurate aircraft assignment"

# Search & Discovery
aircraft_search_capabilities:
  - "Multi-criteria aircraft search (category, capacity, range, airport proximity)"
  - "Similar aircraft recommendations for alternative options"
  - "Geographic search within radius of airports or coordinates"
  - "Performance-based filtering with operational constraints"

invariants:
# Data Integrity
- "Aircraft registration must be unique across the system"
- "Aircraft type relationships must be valid and maintained"
- "Position data must include timestamp and source attribution"
- "Availability periods cannot overlap for the same aircraft"
- "Maintenance schedules must not conflict with active bookings"

# API Rate Limiting
- "ADS-B Exchange API calls must not exceed 8,000 per month (80% of limit)"
- "Position updates must respect minimum 30-second intervals between calls"
- "Cache must be utilized to minimize redundant API requests"

# Operational Constraints
- "Aircraft must have valid operator assignment before activation"
- "Empty leg opportunities must have valid departure and arrival airports"
- "Repositioning costs must be calculated using current fuel and operational rates"

forbidden_states:
# Data Consistency
- "Aircraft without valid aircraft type reference"
- "Position data without proper timestamp or source attribution"
- "Empty legs with invalid or conflicting airport assignments"
- "Availability periods extending beyond aircraft operational limits"

# System Limits
- "Exceeding ADS-B Exchange API monthly or daily rate limits"
- "Storing position data without proper compression or retention policies"
- "Aircraft tracking without proper error handling and fallback mechanisms"

# Security
- "Exposing sensitive aircraft operator information to unauthorized users"
- "Allowing unauthorized modifications to aircraft specifications or positioning data"

depends_on:
- operator: "Aircraft must be associated with valid operators"
- airport: "Positioning and movements require valid airport references"
- booking: "Availability synchronization with booking lifecycle"
- pricing: "Repositioning cost calculations and empty leg pricing"
- external_adsb_exchange: "Real-time aircraft position data via ADS-B Exchange API"
- external_aviation_stack: "Backup aircraft data source for positioning fallback"
- external_opensky: "Alternative aircraft tracking data source"

provides:
- aircraft_fleet_data: "Complete aircraft specifications and performance data"
- real_time_positions: "Current aircraft locations with sub-10-minute accuracy"
- availability_status: "Real-time aircraft availability for booking systems"
- empty_leg_opportunities: "Dynamically generated empty leg flight opportunities"
- repositioning_costs: "Accurate positioning cost calculations for operations"
- aircraft_images: "Compressed and optimized aircraft media assets"
- maintenance_schedules: "Aircraft maintenance tracking and predictive scheduling"
- fleet_analytics: "Aircraft utilization and performance metrics"

enforcement_hooks:
# Data Validation
pre_aircraft_create:
  - "Validate aircraft registration uniqueness across system"
  - "Verify aircraft type and operator exist and are active"
  - "Ensure required operational specifications are provided"

pre_position_update:
  - "Validate position data completeness and accuracy"
  - "Check ADS-B API rate limits before making requests"
  - "Verify aircraft exists and is active for tracking"

pre_empty_leg_creation:
  - "Validate movement has valid departure and arrival airports"
  - "Ensure aircraft is available during proposed empty leg period"
  - "Calculate and validate pricing within operational constraints"

# System Monitoring
post_position_tracking:
  - "Log API usage statistics and rate limit compliance"
  - "Monitor position data quality and source reliability"
  - "Alert on aircraft tracking failures or data gaps"

post_availability_update:
  - "Validate availability periods do not conflict with existing bookings"
  - "Synchronize changes with dependent booking and quote systems"
  - "Update aircraft search indices for real-time discovery"

# Data Cleanup
scheduled_maintenance:
  - "Execute daily position data cleanup maintaining retention policies"
  - "Reset monthly API counters and usage tracking"
  - "Validate aircraft data integrity and repair inconsistencies"

security:
# Access Control
aircraft_data_access:
  - "Restrict aircraft specification modifications to authorized operators"
  - "Limit sensitive operational data to authenticated users only"
  - "Implement role-based access for aircraft management functions"

# API Security
external_api_security:
  - "Secure storage and rotation of ADS-B Exchange API keys"
  - "Rate limiting protection to prevent API abuse"
  - "Audit logging for all external aircraft data requests"

# Data Privacy
aircraft_privacy:
  - "Anonymize aircraft tracking data in public empty leg listings"
  - "Protect operator-specific aircraft operational details"
  - "Comply with aviation data sharing regulations and restrictions"

monitoring:
# Performance Metrics
aircraft_system_health:
  - "Track aircraft API response times and availability"
  - "Monitor ADS-B Exchange API usage and rate limit compliance"
  - "Measure aircraft search query performance and accuracy"

# Data Quality
aircraft_data_quality:
  - "Monitor position data accuracy and freshness"
  - "Track aircraft specification completeness and validity"
  - "Measure empty leg opportunity detection and conversion rates"

# Business Metrics
aircraft_utilization:
  - "Track aircraft availability and booking conversion rates"
  - "Monitor empty leg opportunity generation and success rates"
  - "Measure aircraft search and discovery effectiveness"

error_handling:
# API Failures
adsb_api_failures:
  - "Graceful degradation when ADS-B Exchange API is unavailable"
  - "Fallback to cached position data with staleness indicators"
  - "Alternative data source activation for critical operations"

# Data Inconsistencies
aircraft_data_errors:
  - "Handle aircraft specification validation failures with detailed errors"
  - "Resolve positioning conflicts through data source prioritization"
  - "Automated correction of minor aircraft data inconsistencies"

# System Limits
resource_exhaustion:
  - "API rate limit exceeded handling with queuing and retry mechanisms"
  - "Storage limit management with automated data archival"
  - "Memory optimization for large fleet position tracking operations" 