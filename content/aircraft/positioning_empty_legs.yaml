system: aircraft_positioning_empty_legs
description: "Aircraft Positioning and Empty Leg Management - Intelligent repositioning cost calculations, movement tracking, and empty leg opportunity detection with dynamic pricing optimization"

intent_assertions:
- Accurate aircraft positioning cost calculations for operational planning
- Automated empty leg opportunity detection from aircraft movements
- Dynamic pricing optimization for empty leg marketing
- Real-time movement tracking with departure/arrival management
- Integration with booking system for availability synchronization
- Multi-source positioning data with fallback mechanisms

technical_assertions:
- path: /app/api/v1/endpoints/aircraft/positioning.py
  desc: "Positioning API endpoints for movements and empty legs"
  critical: true
- path: /app/api/v1/endpoints/aircraft/repositioning.py
  desc: "Repositioning API endpoints for empty leg opportunities"
  critical: true
- path: /app/services/positioning_service.py
  desc: "Core positioning service with cost calculations and movement management"
  critical: true
- path: /app/db/models/positioning.py
  desc: "Positioning models for movements, availability, and empty legs"
  critical: true

behavior:
positioning_cost_calculation:
  - "Calculate positioning costs per nautical mile with operator-specific rates"
  - "Factor in fuel costs, crew expenses, and operational overhead"
  - "Provide minimum positioning cost thresholds"
  - "Calculate cost-to-home-base for repositioning decisions"

empty_leg_detection:
  - "Automatically detect empty leg opportunities from scheduled movements"
  - "Apply dynamic discount pricing based on route popularity and timing"
  - "Validate empty leg periods against aircraft availability"
  - "Generate marketing-ready empty leg listings with anonymized details"

movement_tracking:
  - "Track scheduled vs actual departure/arrival times"
  - "Calculate flight distances and estimated flight times"
  - "Monitor movement status (scheduled, in_progress, completed, cancelled)"
  - "Integrate with aircraft availability for conflict detection"

data_integration:
  - "Multi-source positioning data with OpenSky and Aviation Stack fallback"
  - "Real-time synchronization with aircraft tracking systems"
  - "Integration with booking system for availability conflicts"
  - "Automated positioning data updates with configurable frequency"

invariants:
- "Empty leg opportunities must have valid departure and arrival airports"
- "Positioning costs must be calculated using current operational rates"
- "Movement times must be chronologically consistent"
- "Aircraft availability must not conflict with scheduled movements"

forbidden_states:
- "Empty legs without valid route information"
- "Movements with conflicting departure/arrival times"
- "Positioning costs calculated without valid rate data"
- "Availability periods overlapping with confirmed movements"

depends_on:
- aircraft: "Aircraft specifications and operational data"
- airport: "Valid airport references for movements"
- booking: "Availability synchronization and conflict detection"
- pricing: "Current operational rates for cost calculations"
- external_opensky: "Backup positioning data source"
- external_aviation_stack: "Alternative positioning data source"

provides:
- positioning_costs: "Accurate cost calculations for aircraft repositioning"
- empty_leg_opportunities: "Dynamically generated empty leg flight opportunities"
- movement_tracking: "Real-time aircraft movement status and history"
- availability_data: "Aircraft availability synchronized with movements" 