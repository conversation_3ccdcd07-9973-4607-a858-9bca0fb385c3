aircraft_domain_index:
  description: "Villiers.ai Aircraft Domain - Complete aircraft fleet management ecosystem"
  
  core_system:
    path: ./aircraft.yaml
    description: "Primary aircraft domain system definition with comprehensive fleet management"
    
  subdomains:
    aircraft_tracking:
      path: ./aircraft_tracking.yaml
      description: "Real-time aircraft position tracking with ADS-B integration"
      key_capabilities:
        - "Intelligent polling with priority-based updates"
        - "API rate limiting and position caching"
        - "Spatial indexing for geographic queries"
        
    positioning_empty_legs:
      path: ./positioning_empty_legs.yaml
      description: "Aircraft positioning and empty leg opportunity management"
      key_capabilities:
        - "Positioning cost calculations"
        - "Empty leg detection and pricing"
        - "Movement tracking and availability sync"
        
    aircraft_images:
      path: ./aircraft_images.yaml
      description: "Aircraft image and media asset management"
      key_capabilities:
        - "Automated image compression and optimization"
        - "Metadata extraction and management"
        - "Batch processing and PDF extraction"
        
    availability_maintenance:
      path: ./availability_maintenance.yaml
      description: "Aircraft availability and maintenance scheduling"
      key_capabilities:
        - "Real-time availability tracking"
        - "Predictive maintenance scheduling"
        - "Conflict detection and resolution"

  integration_points:
    booking_system:
      - "Aircraft availability synchronization"
      - "Booking conflict detection"
      - "Aircraft assignment optimization"
      
    operator_management:
      - "Multi-operator fleet management"
      - "Operator-specific configurations"
      - "Maintenance scheduling coordination"
      
    pricing_system:
      - "Repositioning cost calculations"
      - "Empty leg pricing optimization"
      - "Operational rate management"
      
    communication_system:
      - "Aircraft status notifications"
      - "Maintenance alerts and scheduling"
      - "Empty leg marketing automation"

  external_dependencies:
    adsb_exchange:
      purpose: "Real-time aircraft position data"
      rate_limits: "8,000 calls/month, 30-second intervals"
      fallback: "Cached data with staleness indicators"
      
    aviation_stack:
      purpose: "Backup aircraft positioning data"
      usage: "Fallback when ADS-B Exchange unavailable"
      
    opensky_network:
      purpose: "Alternative aircraft tracking data"
      usage: "Secondary data source for validation"

  performance_targets:
    position_accuracy: "Sub-10-minute position updates"
    api_efficiency: "80% of monthly API limit utilization"
    availability_sync: "Real-time booking conflict detection"
    image_optimization: "85% compression ratio maintenance"
    
  monitoring_dashboard:
    - "Aircraft fleet status and positions"
    - "ADS-B API usage and rate limit compliance"
    - "Empty leg opportunities and conversion rates"
    - "Maintenance schedules and availability conflicts" 