system: aircraft_images
description: "Aircraft Image and Media Management - Advanced image processing, compression, storage, and metadata management for aircraft visual assets with automated optimization and batch processing capabilities"

intent_assertions:
- Automated image compression and optimization for web delivery
- Comprehensive metadata extraction and management
- Support for multiple image types (exterior, interior, floor plans, cabin)
- Batch processing of PDF documents with image extraction
- Storage optimization with configurable disk/database storage
- Integration with aircraft specifications for visual asset management

technical_assertions:
- path: /app/services/aircraft_images_service.py
  desc: "Core image management service with compression and storage"
  critical: true
- path: /app/db/models/aircraft.py
  desc: "AircraftImage model with metadata and storage path management"
  critical: true
- path: /app/schemas/aircraft_image.py
  desc: "Image API schemas for upload, metadata, and batch operations"
  critical: true

behavior:
image_processing:
  - "Automatic image compression with quality optimization (85% JPEG, optimized PNG)"
  - "Metadata extraction including dimensions, format, and file size"
  - "Support for multiple formats (JPEG, PNG, WebP) with format conversion"
  - "Compression ratio tracking and optimization reporting"

storage_management:
  - "Configurable storage (database BLOB vs file system)"
  - "Organized directory structure by aircraft registration"
  - "Unique filename generation with timestamps and image types"
  - "Storage path management with fallback mechanisms"

metadata_tracking:
  - "Complete image metadata including source attribution"
  - "Primary image designation per image type"
  - "Original filename preservation and content type tracking"
  - "Source reference tracking (email, PDF, upload, etc.)"

batch_operations:
  - "PDF document processing with automatic image extraction"
  - "Batch upload support with progress tracking"
  - "Bulk image optimization and reprocessing capabilities"
  - "Automated image categorization and tagging"

invariants:
- "Each aircraft can have only one primary image per image type"
- "Image data must be compressed before storage"
- "All images must have valid metadata including dimensions and format"
- "Storage paths must be unique and properly organized"

forbidden_states:
- "Images stored without compression or optimization"
- "Multiple primary images of the same type for one aircraft"
- "Images without proper metadata or source attribution"
- "Storage without proper error handling or fallback mechanisms"

depends_on:
- aircraft: "Aircraft specifications and registration data"
- external_storage: "File system or cloud storage for image assets"
- external_pil: "Python Imaging Library for image processing"

provides:
- aircraft_images: "Compressed and optimized aircraft visual assets"
- image_metadata: "Complete image metadata and categorization"
- batch_processing: "PDF and bulk image processing capabilities"
- storage_optimization: "Efficient image storage and retrieval systems"

enforcement_hooks:
pre_image_upload:
  - "Validate image format and size constraints"
  - "Check aircraft exists and is accessible"
  - "Ensure storage capacity and permissions"

post_image_processing:
  - "Verify compression was successful and within quality thresholds"
  - "Update image metadata and database records"
  - "Generate thumbnails and optimized versions if required"

security:
image_access_control:
  - "Restrict image modifications to authorized users"
  - "Validate image content for security threats"
  - "Implement proper access controls for sensitive aircraft images" 