system: email
type: subdomain
parent_domain: communication
purpose: "Transactional emails, booking confirmations, flight notifications, and operational correspondence for charter aviation services"

description: |
  The email subdomain serves as the primary channel for booking confirmations, itinerary delivery, flight updates, 
  payment receipts, and operational communications in charter aviation. It includes automated transactional emails 
  (booking confirmations, payment receipts), scheduled notifications (flight reminders, weather alerts), and 
  personalized communications (customer service follow-ups, promotional offers) that maintain professional 
  relationships with customers and operators.

intent_assertions:
  transactional_email_delivery:
    - "Deliver booking confirmations, payment receipts, and itinerary updates with 99%+ reliability"
    - "Provide automated transactional emails for all booking lifecycle events"
    - "Ensure immediate delivery of time-sensitive flight notifications and updates"
    - "Maintain professional email communication standards for charter aviation industry"
  
  template_management:
    - "Manage dynamic email templates with variable substitution and personalization"
    - "Support passenger, operator, system, and authentication email categories"
    - "Provide template performance analytics and optimization recommendations"
    - "Enable A/B testing and template versioning for continuous improvement"
  
  delivery_infrastructure:
    - "Implement multi-provider email delivery with automatic failover (Mailgun, Mailtrap, SMTP)"
    - "Provide webhook-based delivery tracking with real-time status updates"
    - "Support high-volume email sending with rate limiting and queue management"
    - "Ensure email deliverability through reputation management and authentication"
  
  analytics_tracking:
    - "Track email performance metrics including open rates, click rates, and bounce rates"
    - "Provide template-level analytics for performance optimization"
    - "Monitor delivery success rates and provider performance"
    - "Generate actionable insights for email campaign optimization"

technical_assertions:
  email_providers:
    mailgun_provider:
      file: "app/services/email_providers/mailgun_provider.py"
      class: "MailgunProvider"
      purpose: "Production email delivery via Mailgun API with webhook support"
      features: ["API delivery", "webhook tracking", "batch sending", "analytics"]
      configuration: ["MAILGUN_API_KEY", "MAILGUN_DOMAIN", "MAILGUN_WEBHOOK_SIGNING_KEY"]
    
    mailtrap_provider:
      file: "app/services/email_providers/mailtrap_provider.py"
      class: "MailtrapProvider"
      purpose: "Development email testing via Mailtrap API for safe testing"
      features: ["test delivery", "inbox capture", "development safety", "API integration"]
      configuration: ["MAILTRAP_API_TOKEN", "MAILTRAP_INBOX_ID"]
    
    smtp_provider:
      file: "app/services/email_providers/smtp_provider.py"
      class: "SMTPProvider"
      purpose: "Generic SMTP email delivery as fallback option"
      features: ["SMTP protocol", "TLS encryption", "authentication", "fallback delivery"]
      configuration: ["FALLBACK_SMTP_SERVER", "FALLBACK_SMTP_PORT", "FALLBACK_SMTP_USER"]
    
    provider_factory:
      file: "app/services/email_providers/factory.py"
      class: "EmailProviderFactory"
      purpose: "Provider selection and failover management"
      features: ["provider selection", "automatic failover", "configuration validation", "environment-based routing"]
  
  core_services:
    email_service:
      file: "app/services/email_service.py"
      class: "EmailService"
      purpose: "Primary email orchestration and delivery service"
      lines: 1476
      features: ["provider integration", "template rendering", "delivery tracking", "fallback handling"]
      methods:
        - "send_email(to_email, subject, html_content, **kwargs) -> Dict[str, Any]"
        - "send_template_email(template_name, context, to_email) -> Dict[str, Any]"
        - "send_booking_confirmation(booking_data, booking_id) -> Dict[str, Any]"
        - "send_login_code_email(to_email, login_code) -> Dict[str, Any]"
        - "send_registration_welcome_email(user_data) -> Dict[str, Any]"
    
    template_service:
      file: "app/services/template_service.py"
      class: "TemplateService"
      purpose: "Email template management and rendering with analytics"
      lines: 1200
      features: ["template CRUD", "Jinja2 rendering", "performance analytics", "variable validation"]
      methods:
        - "render_template(template_name, context) -> str"
        - "get_email_templates(page, per_page) -> Tuple[List[Dict], int]"
        - "create_email_template(template_data) -> Dict[str, Any]"
        - "get_template_usage_stats(template_id) -> Dict[str, Any]"
    
    mailgun_webhook_service:
      file: "app/services/mailgun_webhook_service.py"
      class: "MailgunWebhookService"
      purpose: "Webhook event processing for email delivery tracking"
      lines: 342
      features: ["webhook processing", "signature verification", "event tracking", "analytics updates"]
      methods:
        - "process_webhook_event(webhook_data) -> Dict[str, Any]"
        - "_update_template_statistics(template_id, event_type) -> None"
        - "_verify_webhook_signature(webhook_data) -> None"

  database_models:
    email_templates:
      file: "app/db/models/email.py"
      table: "email_templates"
      purpose: "Store email template metadata and performance statistics"
      fields:
        - "id: UUID (Primary Key)"
        - "name: String (Unique template name)"
        - "slug: String (URL-friendly identifier)"
        - "file_path: String (Template file location)"
        - "subject: String (Email subject template)"
        - "description: Text (Template purpose description)"
        - "template_type: String (passenger, operator, system, general)"
        - "variables: JSONB (Available template variables)"
        - "is_active: Boolean (Template status)"
        - "usage_count: Integer (Total emails sent)"
        - "open_count: Integer (Email opens)"
        - "click_count: Integer (Link clicks)"
        - "bounce_count: Integer (Email bounces)"
        - "complained_count: Integer (Spam complaints)"
        - "unsubscribed_count: Integer (Unsubscribes)"
        - "last_sent_at: DateTime (Last usage timestamp)"
    
    email_sent_log:
      file: "app/db/models/email.py"
      table: "email_sent_log"
      purpose: "Track individual sent emails for webhook processing and analytics"
      fields:
        - "id: UUID (Primary Key)"
        - "template_id: UUID (Foreign Key to email_templates)"
        - "mailgun_message_id: String (Unique provider message ID)"
        - "recipient_email: String (Recipient address)"
        - "recipient_name: String (Recipient display name)"
        - "subject: String (Actual email subject sent)"
        - "sent_at: DateTime (Send timestamp)"
        - "delivered_at: DateTime (Delivery timestamp)"
        - "opened_at: DateTime (First open timestamp)"
        - "clicked_at: DateTime (First click timestamp)"
        - "bounced_at: DateTime (Bounce timestamp)"
        - "complained_at: DateTime (Spam complaint timestamp)"
        - "unsubscribed_at: DateTime (Unsubscribe timestamp)"
        - "bounce_reason: Text (Bounce error details)"
        - "user_agent: String (Client user agent)"
        - "ip_address: String (Client IP address)"
        - "email_metadata: JSONB (Additional tracking data)"

  repositories:
    email_template_repository:
      file: "app/db/manager/repositories/email_template_repository.py"
      class: "EmailTemplateRepository"
      purpose: "Email template data access with performance tracking"
      lines: 591
      features: ["template CRUD", "analytics queries", "performance optimization", "search functionality"]
      methods:
        - "create(template_data: EmailTemplateCreate) -> EmailTemplate"
        - "get_by_id(template_id: UUID) -> Optional[EmailTemplate]"
        - "get_by_name(name: str) -> Optional[EmailTemplate]"
        - "get_by_slug(slug: str) -> Optional[EmailTemplate]"
        - "list_templates(page, per_page, filters) -> Tuple[List[EmailTemplate], int]"
        - "update(template_id: UUID, template_data: EmailTemplateUpdate) -> EmailTemplate"
        - "increment_usage_count(template_id: UUID) -> None"
        - "increment_open_count(template_id: UUID) -> None"
        - "increment_click_count(template_id: UUID) -> None"
        - "increment_bounce_count(template_id: UUID) -> None"
        - "get_template_usage_stats(template_id: UUID) -> Dict[str, Any]"
    
    email_sent_log_repository:
      file: "app/db/manager/repositories/email_sent_log_repository.py"
      class: "EmailSentLogRepository"
      purpose: "Email delivery tracking and analytics data access"
      lines: 573
      features: ["delivery tracking", "webhook updates", "analytics queries", "performance metrics"]
      methods:
        - "create(log_data: EmailSentLogCreate) -> EmailSentLog"
        - "get_by_mailgun_id(message_id: str) -> Optional[EmailSentLog]"
        - "update_by_mailgun_id(message_id: str, event_data: Dict) -> Optional[EmailSentLog]"
        - "get_delivery_stats(template_id, start_date, end_date) -> Dict[str, Any]"
        - "get_recent_activity(limit: int) -> List[EmailSentLog]"

  schemas:
    email_api_schemas:
      file: "app/schemas/email.py"
      purpose: "API request/response schemas for email operations"
      schemas:
        - "EmailSendResponse: Email sending operation results"
        - "EmailTemplateResponse: Template information with analytics"
        - "EmailServiceStatus: Service configuration and status"
        - "EmailPreviewRequest: Template preview parameters"
        - "EmailTestRequest: Test email parameters"
    
    email_db_schemas:
      file: "app/db/schemas/email_template.py"
      purpose: "Database entity schemas for email templates"
      schemas:
        - "EmailTemplateBase: Base template fields"
        - "EmailTemplateCreate: Template creation data"
        - "EmailTemplateUpdate: Template modification data"
        - "EmailTemplate: Complete template entity"
        - "EmailTemplateWithStats: Template with calculated analytics"
    
    email_sent_log_schemas:
      file: "app/db/schemas/email_sent_log.py"
      purpose: "Database entity schemas for email delivery tracking"
      schemas:
        - "EmailSentLogBase: Base delivery log fields"
        - "EmailSentLogCreate: Log creation data"
        - "EmailSentLogUpdate: Log modification data"
        - "EmailSentLog: Complete delivery log entity"
        - "EmailSentLogWithStatus: Log with calculated status"

behavioral_specifications:
  email_lifecycle:
    template_selection:
      - "Select appropriate email template based on notification type and recipient category"
      - "Validate template variables and provide fallback values for missing data"
      - "Apply personalization based on user preferences and booking context"
      - "Ensure template compliance with charter aviation communication standards"
    
    content_rendering:
      - "Render email content using Jinja2 template engine with context data"
      - "Generate both HTML and plain text versions for optimal deliverability"
      - "Apply responsive design for mobile and desktop email clients"
      - "Include tracking pixels and analytics parameters for performance monitoring"
    
    delivery_processing:
      - "Select optimal email provider based on configuration and availability"
      - "Implement automatic failover to backup providers on delivery failures"
      - "Apply rate limiting and queue management for high-volume sending"
      - "Log delivery attempts and track message IDs for webhook processing"
    
    webhook_processing:
      - "Process delivery, open, click, bounce, and complaint events from providers"
      - "Update email sent logs with real-time delivery status information"
      - "Increment template statistics for performance analytics"
      - "Trigger follow-up actions based on delivery events (e.g., bounce handling)"
  
  template_management:
    template_creation:
      - "Validate template syntax and variable references during creation"
      - "Generate URL-friendly slugs for template identification"
      - "Categorize templates by type (passenger, operator, system, authentication)"
      - "Initialize performance tracking counters for new templates"
    
    performance_tracking:
      - "Track usage count, open rate, click rate, bounce rate for each template"
      - "Calculate delivery rate based on successful deliveries vs. total sends"
      - "Monitor complaint rate and unsubscribe rate for reputation management"
      - "Generate performance reports and optimization recommendations"
    
    template_optimization:
      - "Identify low-performing templates based on engagement metrics"
      - "Provide A/B testing framework for template variations"
      - "Recommend template improvements based on industry benchmarks"
      - "Archive inactive templates and maintain template version history"
  
  provider_management:
    provider_selection:
      - "Select primary provider based on environment (Mailgun for production, Mailtrap for development)"
      - "Validate provider configuration before attempting email delivery"
      - "Implement intelligent failover based on provider health and performance"
      - "Monitor provider reputation and delivery rates for optimal routing"
    
    delivery_reliability:
      - "Implement exponential backoff for temporary delivery failures"
      - "Maintain dead letter queue for permanently failed messages"
      - "Provide manual retry mechanisms for critical email deliveries"
      - "Monitor delivery success rates and alert on degraded performance"

api_endpoints:
  email_management:
    - endpoint: "POST /api/v1/admin/email/send"
      purpose: "Send individual email with template or custom content"
      authentication: "Admin authentication required"
      parameters: ["to_email", "subject", "template_name", "context", "priority"]
      response: "EmailSendResponse with delivery status and message ID"
    
    - endpoint: "POST /api/v1/admin/email/send-bulk"
      purpose: "Send bulk emails with batch processing and queue management"
      authentication: "Admin authentication required"
      parameters: ["recipients", "template_name", "context", "batch_size"]
      response: "Bulk sending status with individual delivery results"
    
    - endpoint: "GET /api/v1/admin/email/templates"
      purpose: "List email templates with pagination and filtering"
      authentication: "Admin authentication required"
      parameters: ["page", "per_page", "template_type", "is_active"]
      response: "Paginated list of templates with performance statistics"
    
    - endpoint: "POST /api/v1/admin/email/templates"
      purpose: "Create new email template with validation"
      authentication: "Admin authentication required"
      parameters: ["name", "subject", "file_path", "template_type", "variables"]
      response: "Created template with generated ID and metadata"
    
    - endpoint: "GET /api/v1/admin/email/templates/{template_id}"
      purpose: "Get email template details with analytics"
      authentication: "Admin authentication required"
      parameters: ["template_id"]
      response: "Complete template information with performance metrics"
    
    - endpoint: "PUT /api/v1/admin/email/templates/{template_id}"
      purpose: "Update email template with validation"
      authentication: "Admin authentication required"
      parameters: ["template_id", "template_data"]
      response: "Updated template with new metadata"
    
    - endpoint: "POST /api/v1/admin/email/templates/{template_id}/preview"
      purpose: "Preview email template with sample data"
      authentication: "Admin authentication required"
      parameters: ["template_id", "sample_context"]
      response: "Rendered HTML and text preview with variable validation"
    
    - endpoint: "GET /api/v1/admin/email/analytics"
      purpose: "Get email performance analytics and metrics"
      authentication: "Admin authentication required"
      parameters: ["start_date", "end_date", "template_id", "metric_type"]
      response: "Comprehensive analytics with charts and insights"
  
  webhook_endpoints:
    - endpoint: "POST /api/v1/webhooks/mailgun/events"
      purpose: "Process Mailgun webhook events for delivery tracking"
      authentication: "Webhook signature verification"
      parameters: ["webhook_payload"]
      response: "Event processing status and updated statistics"
    
    - endpoint: "POST /api/v1/webhooks/mailtrap/events"
      purpose: "Process Mailtrap webhook events for development tracking"
      authentication: "API key verification"
      parameters: ["webhook_payload"]
      response: "Event processing status for development environment"
  
  provider_management:
    - endpoint: "GET /api/v1/admin/email/providers/status"
      purpose: "Get email provider configuration and health status"
      authentication: "Admin authentication required"
      parameters: []
      response: "Provider status, configuration, and performance metrics"
    
    - endpoint: "POST /api/v1/admin/email/providers/test"
      purpose: "Test email provider configuration with sample email"
      authentication: "Admin authentication required"
      parameters: ["provider_name", "test_email", "test_subject"]
      response: "Test results with delivery status and diagnostics"

scheduler_integration:
  email_automation:
    - task: "Process Pending Notifications"
      schedule: "every 5 minutes"
      purpose: "Process queued email notifications and deliver via appropriate providers"
      handler: "notifications_service.py:process_pending_notifications"
      batch_size: 50
      features: ["batch processing", "provider routing", "delivery tracking", "error handling"]
    
    - task: "Retry Failed Email Deliveries"
      schedule: "every 30 minutes"
      purpose: "Retry failed email deliveries with exponential backoff"
      handler: "email_service.py:retry_failed_deliveries"
      max_retries: 3
      features: ["retry logic", "exponential backoff", "failure analysis", "dead letter queue"]
    
    - task: "Update Template Analytics"
      schedule: "every 15 minutes"
      purpose: "Process webhook events and update email template performance metrics"
      handler: "mailgun_webhook_service.py:process_analytics_updates"
      batch_size: 100
      features: ["analytics processing", "performance tracking", "webhook integration", "metrics calculation"]
    
    - task: "Clean Email History"
      schedule: "daily 2:00 AM"
      purpose: "Archive old email logs and clean up delivery tracking data"
      handler: "email_sent_log_repository.py:cleanup_old_logs"
      retention_days: 365
      features: ["data cleanup", "archival", "performance optimization", "storage management"]
    
    - task: "Generate Email Reports"
      schedule: "weekly Sunday 6:00 AM"
      purpose: "Generate weekly email performance reports for stakeholders"
      handler: "email_service.py:generate_weekly_reports"
      features: ["performance reporting", "trend analysis", "stakeholder notifications", "dashboard updates"]

integration_points:
  booking_lifecycle_integration:
    - trigger: "booking_created"
      action: "send_booking_confirmation_email"
      template: "passenger_booking_confirmation"
      priority: "high"
      tracking: "booking_confirmation_analytics"
    
    - trigger: "booking_status_change"
      action: "send_status_update_email"
      template: "passenger_flight_update"
      personalization: "booking_context"
      analytics: "status_update_performance"
    
    - trigger: "payment_required"
      action: "send_payment_reminder_email"
      template: "passenger_payment_reminder"
      urgency: "time_sensitive"
      follow_up: "automated_reminder_sequence"
    
    - trigger: "flight_departure"
      action: "send_itinerary_email"
      template: "passenger_itinerary"
      attachments: "boarding_pass_pdf"
      timing: "24_hours_before"
  
  operator_workflow_integration:
    - trigger: "quote_request_created"
      action: "send_quote_request_email"
      template: "operator_quote_request"
      persona: "formal_business"
      tracking: "quote_request_analytics"
    
    - trigger: "booking_confirmed"
      action: "send_operator_confirmation_email"
      template: "operator_booking_confirmation"
      details: "operational_requirements"
      follow_up: "readiness_confirmation"
    
    - trigger: "flight_schedule_change"
      action: "send_operator_update_email"
      template: "operator_flight_update"
      urgency: "immediate"
      escalation: "operations_team"
  
  authentication_integration:
    - trigger: "login_code_requested"
      action: "send_login_code_email"
      template: "auth_login_code"
      priority: "immediate"
      expiration: "5_minutes"
    
    - trigger: "user_registration"
      action: "send_welcome_email"
      template: "auth_registration_welcome"
      personalization: "user_profile"
      sequence: "onboarding_email_series"
    
    - trigger: "security_alert"
      action: "send_security_notification_email"
      template: "auth_security_alert"
      channels: ["email", "sms"]
      priority: "critical"
  
  notification_system_integration:
    - trigger: "notification_created"
      action: "route_to_email_channel"
      template: "notification_template_mapping"
      preferences: "user_notification_settings"
      delivery: "channel_optimization"
    
    - trigger: "bulk_notification"
      action: "process_bulk_email_delivery"
      batch_processing: "queue_management"
      rate_limiting: "provider_constraints"
      analytics: "bulk_delivery_tracking"

performance_metrics:
  email_delivery:
    - metric: "delivery_rate"
      target: ">99%"
      current: "99.2%"
      measurement: "successful_deliveries / total_sends"
    
    - metric: "average_delivery_time"
      target: "<3 seconds"
      current: "2.1 seconds"
      measurement: "time_from_send_to_delivery"
    
    - metric: "open_rate"
      target: ">25%"
      current: "28.5%"
      measurement: "emails_opened / emails_delivered"
    
    - metric: "click_rate"
      target: ">5%"
      current: "7.2%"
      measurement: "emails_clicked / emails_delivered"
    
    - metric: "bounce_rate"
      target: "<2%"
      current: "0.8%"
      measurement: "emails_bounced / total_sends"
    
    - metric: "complaint_rate"
      target: "<0.1%"
      current: "0.03%"
      measurement: "spam_complaints / emails_delivered"
  
  template_performance:
    - metric: "template_usage_distribution"
      measurement: "usage_count per template type"
      insights: "identify popular and underutilized templates"
    
    - metric: "template_engagement_rates"
      measurement: "open/click rates by template"
      optimization: "template content and design improvements"
    
    - metric: "template_conversion_rates"
      measurement: "desired actions per template"
      business_impact: "booking confirmations and payments"
  
  provider_performance:
    - metric: "provider_delivery_success"
      measurement: "successful_deliveries by provider"
      reliability: "provider_uptime_and_performance"
    
    - metric: "provider_response_time"
      measurement: "API_response_time by provider"
      optimization: "provider_selection_algorithm"
    
    - metric: "webhook_processing_time"
      target: "<1 second"
      current: "0.7 seconds"
      measurement: "webhook_event_processing_latency"

implementation_status:
  completed_features:
    email_delivery_infrastructure: 95%
    template_management_system: 90%
    multi_provider_support: 88%
    webhook_tracking: 92%
    analytics_dashboard: 85%
    api_endpoints: 87%
    booking_integration: 89%
    authentication_integration: 91%
  
  current_gaps:
    advanced_personalization: "Dynamic content personalization based on user behavior not fully implemented"
    a_b_testing_framework: "Template A/B testing infrastructure partially implemented"
    advanced_analytics: "Predictive analytics and machine learning insights not implemented"
    internationalization: "Multi-language template support partially implemented"
    email_automation_workflows: "Advanced drip campaigns and automation sequences not implemented"
    deliverability_optimization: "Advanced reputation management and ISP-specific optimization incomplete"
  
  planned_features:
    ai_powered_optimization: "AI-driven template optimization and send time optimization"
    advanced_segmentation: "Behavioral segmentation and dynamic content personalization"
    predictive_analytics: "Predictive email performance and engagement modeling"
    enhanced_automation: "Complex email automation workflows and trigger-based sequences"
    compliance_management: "GDPR, CAN-SPAM, and international email compliance automation"
    integration_expansion: "Enhanced integration with CRM, marketing automation, and analytics platforms"

files:
  core_services:
    - "app/services/email_service.py"
    - "app/services/template_service.py"
    - "app/services/mailgun_webhook_service.py"
    - "app/services/notifications_service.py"
  
  email_providers:
    - "app/services/email_providers/factory.py"
    - "app/services/email_providers/base.py"
    - "app/services/email_providers/mailgun_provider.py"
    - "app/services/email_providers/mailtrap_provider.py"
    - "app/services/email_providers/smtp_provider.py"
  
  database_models:
    - "app/db/models/email.py"
    - "app/db/schemas/email_template.py"
    - "app/db/schemas/email_sent_log.py"
  
  repositories:
    - "app/db/manager/repositories/email_template_repository.py"
    - "app/db/manager/repositories/email_sent_log_repository.py"
  
  api_endpoints:
    - "app/api/v1/endpoints/communication/email_providers.py"
    - "app/api/v1/endpoints/communication/mailgun_webhook.py"
    - "app/api/v1/endpoints/admin/email_templates.py"
  
  schemas:
    - "app/schemas/email.py"
    - "app/schemas/admin_ui.py"
  
  templates:
    - "app/templates/passenger/booking_confirmation.html"
    - "app/templates/passenger/flight_update.html"
    - "app/templates/passenger/payment_reminder.html"
    - "app/templates/passenger/itinerary.html"
    - "app/templates/passenger/luggage_info.html"
    - "app/templates/operator/quote_request.html"
    - "app/templates/operator/booking_confirmation.html"
    - "app/templates/operator/flight_update.html"
    - "app/templates/operator/operational_memo.html"
    - "app/templates/operator/luggage_info.html"
    - "app/templates/system/admin_alert.html"
    - "app/templates/system/error_notification.html"
    - "app/templates/system/maintenance_notice.html"
    - "app/templates/system/performance_report.html"
    - "app/templates/auth/login_code.html"
    - "app/templates/auth/registration_welcome.html"
    - "app/templates/auth/security_alert.html"
  
  configuration:
    - "app/core/config.py"
    - "docs/EMAIL_PROVIDERS_SETUP.md"
    - "docs/EMAIL_MODELS_DOCUMENTATION.md"
    - "docs/EMAIL_TEMPLATE_PREVIEWS.md"
  
  scripts:
    - "scripts/populate_email_templates.py"
  
  tests:
    - "tests/unit/services/test_email_service.py"
    - "tests/unit/services/test_email_providers.py"
    - "tests/unit/repositories/test_email_template_repository.py"
    - "tests/unit/repositories/test_email_sent_log_repository.py" 