system: "communication"
description: "Multi-channel customer and operator communication, notification orchestration, and message delivery across the charter aviation platform"

intent_assertions:
  - "Enable reliable multi-channel communication between customers, operators, and internal systems"
  - "Provide real-time notification orchestration with delivery tracking and webhook integration"
  - "Maintain comprehensive message threading and conversation history for all stakeholder interactions"
  - "Support intelligent template management with personalization and A/B testing capabilities"
  - "Ensure automated workflow triggers for booking lifecycle, flight operations, and customer service"
  - "Deliver consistent agent persona management across all operator communications"
  - "Provide robust email delivery infrastructure with fallback providers and error recovery"
  - "Enable real-time chat interface with AI-powered intent parsing and response generation"

technical_assertions:
  communication_channels:
    email_infrastructure:
      - "Primary email delivery via Mailgun API with webhook-based delivery tracking"
      - "Fallback email providers (Mailtrap, SMTP) for development and redundancy"
      - "Template-based email rendering with Jinja2 engine and database template storage"
      - "Email delivery logging with comprehensive webhook event processing"
      - "Performance monitoring: <3s email send time, >99% delivery rate"
    
    real_time_messaging:
      - "WebSocket-based chat interface with session management and message persistence"
      - "AI-powered intent parsing with confidence scoring and follow-up generation"
      - "Agent persona rotation for consistent operator communication styles"
      - "Message threading with conversation context and attachment support"
      - "Real-time typing indicators and message status updates"
    
    notification_orchestration:
      - "Multi-channel notification delivery (email, SMS, push, in-app, Nostr)"
      - "User preference management with granular notification type controls"
      - "Scheduled notification processing with retry logic and failure handling"
      - "Priority-based notification queuing with immediate delivery for critical alerts"
      - "Notification analytics with open rates, click tracking, and engagement metrics"
    
    webhook_integration:
      - "Mailgun webhook processing for delivery, open, click, bounce, and complaint events"
      - "Gmail webhook integration for inbound email processing and thread management"
      - "BTCPay webhook processing for payment notification integration"
      - "Webhook signature verification and security enforcement"
      - "Event-driven architecture with real-time status updates"

  message_threading:
    conversation_management:
      - "Thread-based conversation tracking with operator and customer contexts"
      - "Message direction tracking (inbound/outbound) with metadata preservation"
      - "Quote extraction from operator emails using AI-powered content analysis"
      - "Attachment handling with file storage and metadata tracking"
      - "Thread status management (active, archived, completed) with lifecycle automation"
    
    operator_communication:
      - "Persona-driven communication with formal, friendly, and technical styles"
      - "Automated follow-up generation based on thread activity and response patterns"
      - "Quote request orchestration with template-based operator outreach"
      - "Response tracking with confidence scoring and quote extraction"
      - "Reliability scoring based on response time and communication quality"
    
    customer_engagement:
      - "Booking lifecycle notifications with status updates and flight information"
      - "Personalized communication based on user preferences and travel history"
      - "Emergency notification protocols for flight operations and schedule changes"
      - "Marketing communication with unsubscribe management and preference controls"
      - "Support ticket integration with escalation and resolution tracking"

  template_management:
    email_templates:
      - "Database-driven template storage with version control and A/B testing"
      - "Jinja2 template rendering with context data validation and error handling"
      - "Template performance analytics with open rates, click rates, and conversion tracking"
      - "Fallback template system for reliability during template errors"
      - "Template variable validation and dynamic content personalization"
    
    content_personalization:
      - "Dynamic content insertion based on user profile and booking context"
      - "Agent persona integration for consistent communication tone and style"
      - "Multilingual template support with locale-based content selection"
      - "Brand consistency enforcement with standardized styling and messaging"
      - "Template usage analytics with performance optimization recommendations"

behavior:
  notification_lifecycle:
    creation_and_scheduling:
      - "Notification creation with type classification and priority assignment"
      - "Scheduled delivery processing with timezone awareness and user preferences"
      - "Batch notification processing for system-wide announcements and updates"
      - "Template selection and context data preparation for personalized content"
      - "Channel selection based on user preferences and notification urgency"
    
    delivery_and_tracking:
      - "Multi-provider email delivery with automatic failover and retry logic"
      - "Real-time delivery status tracking via webhook integration and event processing"
      - "Delivery confirmation with timestamp recording and status updates"
      - "Bounce handling with automatic list management and sender reputation protection"
      - "Engagement tracking with open rates, click tracking, and user interaction analytics"
    
    error_handling_and_recovery:
      - "Failed notification retry with exponential backoff and maximum attempt limits"
      - "Provider failover for email delivery issues with automatic provider switching"
      - "Error logging with detailed failure analysis and resolution recommendations"
      - "Dead letter queue management for permanently failed notifications"
      - "Performance monitoring with alerting for delivery rate degradation"

  communication_workflows:
    booking_lifecycle_integration:
      - "Automated booking confirmation emails with flight details and payment information"
      - "Status update notifications for booking modifications, cancellations, and confirmations"
      - "Pre-flight reminders with check-in instructions and contact information"
      - "Post-flight follow-up with feedback collection and future booking incentives"
      - "Emergency communication protocols for flight delays, cancellations, and weather events"
    
    operator_coordination:
      - "Quote request automation with template-based operator outreach and follow-up"
      - "Response processing with AI-powered quote extraction and confidence scoring"
      - "Performance tracking with response time analytics and reliability scoring"
      - "Escalation protocols for non-responsive operators with alternative sourcing"
      - "Relationship management with communication history and preference tracking"
    
    customer_service_integration:
      - "Support ticket creation from inbound emails with automatic categorization"
      - "Escalation workflows with priority-based routing and response time tracking"
      - "Knowledge base integration with automated response suggestions"
      - "Satisfaction surveys with feedback collection and service improvement analytics"
      - "Live chat integration with agent handoff and conversation continuity"

  real_time_communication:
    chat_interface:
      - "WebSocket connection management with session persistence and reconnection handling"
      - "Message queuing with offline message delivery and synchronization"
      - "Typing indicators and read receipts for enhanced user experience"
      - "File sharing capabilities with security scanning and storage management"
      - "Chat history preservation with search functionality and export capabilities"
    
    ai_integration:
      - "Intent parsing with natural language understanding and confidence scoring"
      - "Automated response generation with context awareness and personalization"
      - "Escalation detection with human agent handoff and conversation transfer"
      - "Learning capabilities with conversation analysis and response optimization"
      - "Multi-language support with automatic translation and locale detection"

invariants:
  - "All email delivery must be tracked with webhook-based status updates and comprehensive logging"
  - "Message threads must maintain conversation continuity with proper direction tracking and metadata"
  - "Template rendering must have fallback mechanisms to prevent communication failures"
  - "User notification preferences must be respected across all communication channels"
  - "Webhook signatures must be verified for all external service integrations"
  - "Communication workflows must integrate with booking lifecycle and operational events"
  - "Agent persona consistency must be maintained across all operator communications"
  - "Real-time chat sessions must persist conversation history and context"

forbidden_states:
  - "Email delivery without proper webhook tracking and status monitoring"
  - "Template rendering failures without fallback content delivery"
  - "Notification delivery that ignores user preference settings"
  - "Webhook processing without signature verification and security validation"
  - "Communication workflows that operate independently of booking lifecycle events"
  - "Message threading without proper conversation context and metadata preservation"
  - "Operator communication without persona consistency and relationship tracking"
  - "Chat sessions without proper session management and message persistence"

depends_on:
  - booking
  - authentication
  - core
  - operator
  - aircraft

provides:
  - "Multi-channel notification delivery infrastructure"
  - "Template-based email communication system"
  - "Real-time chat interface with AI integration"
  - "Webhook-based delivery tracking and analytics"
  - "Message threading and conversation management"
  - "Agent persona management for operator communications"
  - "Communication workflow automation and integration"
  - "Performance monitoring and analytics dashboard"

files:
  api_endpoints:
    communication_endpoints:
      - file: "app/api/v1/endpoints/communication/notifications.py"
        lines: 200
        description: "Notification management API with user preferences and delivery tracking"
        endpoints: 6
        features: ["notification creation", "preference management", "delivery status", "analytics"]
      
      - file: "app/api/v1/endpoints/communication/chat.py"
        lines: 381
        description: "Real-time chat API with WebSocket support and AI integration"
        endpoints: 8
        features: ["message processing", "session management", "history retrieval", "persona management"]
      
      - file: "app/api/v1/endpoints/communication/chat_ws.py"
        lines: 245
        description: "WebSocket chat interface with real-time messaging capabilities"
        endpoints: 1
        features: ["WebSocket connections", "real-time messaging", "session persistence"]
      
      - file: "app/api/v1/endpoints/communication/email_providers.py"
        lines: 215
        description: "Email provider management and testing API"
        endpoints: 4
        features: ["provider status", "configuration testing", "failover management"]
      
      - file: "app/api/v1/endpoints/communication/mailgun_webhook.py"
        lines: 205
        description: "Mailgun webhook processing for email delivery tracking"
        endpoints: 3
        features: ["delivery tracking", "event processing", "batch processing"]
      
      - file: "app/api/v1/endpoints/communication/gmail_webhook.py"
        lines: 141
        description: "Gmail webhook integration for inbound email processing"
        endpoints: 2
        features: ["inbound email processing", "thread management"]
      
      - file: "app/api/v1/endpoints/communication/feedback.py"
        lines: 33
        description: "Customer feedback collection and management"
        endpoints: 1
        features: ["feedback submission", "rating collection"]

  core_services:
    communication_services:
      - file: "app/services/communication_service.py"
        lines: 486
        description: "Primary communication orchestration service"
        methods: 12
        features: ["passenger notifications", "operator notifications", "system notifications", "history management"]
      
      - file: "app/services/email_service.py"
        lines: 1476
        description: "Email delivery service with multi-provider support"
        methods: 25
        features: ["multi-provider delivery", "template integration", "webhook tracking", "fallback handling"]
      
      - file: "app/services/notifications_service.py"
        lines: 386
        description: "Multi-channel notification delivery service"
        methods: 15
        features: ["channel management", "preference handling", "delivery tracking", "analytics"]
      
      - file: "app/services/template_service.py"
        lines: 1200
        description: "Template management and rendering service"
        methods: 20
        features: ["template rendering", "database storage", "performance analytics", "fallback handling"]
      
      - file: "app/services/email_threads_service.py"
        lines: 613
        description: "Email thread management with AI-powered conversation tracking"
        methods: 18
        features: ["thread management", "follow-up generation", "quote extraction", "persona rotation"]
      
      - file: "app/services/operator_communication_service.py"
        lines: 302
        description: "Specialized operator communication with persona management"
        methods: 8
        features: ["quote requests", "persona management", "response tracking", "reliability scoring"]
      
      - file: "app/services/mailgun_webhook_service.py"
        lines: 342
        description: "Mailgun webhook event processing service"
        methods: 12
        features: ["event processing", "signature verification", "analytics updates", "error handling"]

  email_providers:
    provider_implementations:
      - file: "app/services/email_providers/mailgun_provider.py"
        lines: 245
        description: "Production Mailgun email provider implementation"
        features: ["API integration", "webhook support", "delivery tracking", "error handling"]
      
      - file: "app/services/email_providers/mailtrap_provider.py"
        lines: 180
        description: "Development Mailtrap email provider for testing"
        features: ["testing environment", "email capture", "debugging tools"]
      
      - file: "app/services/email_providers/smtp_provider.py"
        lines: 165
        description: "Generic SMTP provider for fallback delivery"
        features: ["SMTP protocol", "fallback delivery", "configuration flexibility"]
      
      - file: "app/services/email_providers/__init__.py"
        lines: 95
        description: "Email provider factory and management"
        features: ["provider selection", "configuration management", "failover logic"]

  database_models:
    communication_models:
      - file: "app/db/models/message_thread.py"
        lines: 180
        description: "Message thread and conversation tracking models"
        models: ["MessageThread", "ThreadMessage"]
        features: ["conversation tracking", "metadata storage", "status management", "persona tracking"]
      
      - file: "app/db/models/notification.py"
        lines: 113
        description: "Notification and preference management models"
        models: ["Notification", "NotificationPreference"]
        features: ["multi-channel notifications", "user preferences", "delivery tracking", "analytics"]
      
      - file: "app/db/models/email_template.py"
        lines: 156
        description: "Email template storage and management model"
        models: ["EmailTemplate"]
        features: ["template versioning", "performance analytics", "variable tracking"]
      
      - file: "app/db/models/email_sent_log.py"
        lines: 98
        description: "Email delivery logging and tracking model"
        models: ["EmailSentLog"]
        features: ["delivery tracking", "webhook events", "performance metrics"]
      
      - file: "app/db/models/chat.py"
        lines: 187
        description: "Chat session and message models"
        models: ["ChatSession", "ChatMessage"]
        features: ["session management", "message persistence", "AI integration"]

  schemas:
    communication_schemas:
      - file: "app/db/schemas/communication.py"
        lines: 58
        description: "Communication service request/response schemas"
        schemas: ["NotificationRequest", "NotificationResponse", "CommunicationHistory"]
        features: ["request validation", "response formatting", "type safety"]
      
      - file: "app/db/schemas/message_thread.py"
        lines: 245
        description: "Message thread and conversation schemas"
        schemas: ["MessageThread", "ThreadMessage", "MessageThreadCreate"]
        features: ["thread management", "message tracking", "relationship handling"]
      
      - file: "app/db/schemas/notification.py"
        lines: 178
        description: "Notification and preference schemas"
        schemas: ["Notification", "NotificationPreference", "NotificationCreate"]
        features: ["notification management", "preference handling", "validation"]
      
      - file: "app/db/schemas/email_template.py"
        lines: 198
        description: "Email template management schemas"
        schemas: ["EmailTemplate", "EmailTemplateCreate", "EmailTemplateUpdate"]
        features: ["template management", "version control", "analytics tracking"]
      
      - file: "app/db/schemas/email_sent_log.py"
        lines: 138
        description: "Email delivery tracking schemas"
        schemas: ["EmailSentLog", "EmailEventUpdate", "EmailSentLogWithStatus"]
        features: ["delivery tracking", "webhook processing", "status management"]

  repositories:
    communication_repositories:
      - file: "app/db/manager/repositories/message_thread_repository.py"
        lines: 445
        description: "Message thread data access with conversation management"
        methods: 18
        features: ["thread CRUD", "message management", "search functionality", "analytics"]
      
      - file: "app/db/manager/repositories/notification_repository.py"
        lines: 312
        description: "Notification management repository"
        methods: 14
        features: ["notification CRUD", "preference management", "delivery tracking", "analytics"]
      
      - file: "app/db/manager/repositories/email_template_repository.py"
        lines: 298
        description: "Email template management repository"
        methods: 12
        features: ["template CRUD", "performance tracking", "version management", "analytics"]
      
      - file: "app/db/manager/repositories/email_sent_log_repository.py"
        lines: 267
        description: "Email delivery tracking repository"
        methods: 15
        features: ["delivery logging", "webhook processing", "analytics", "reporting"]
      
      - file: "app/db/manager/repositories/chat_repository.py"
        lines: 234
        description: "Chat session and message management repository"
        methods: 11
        features: ["session management", "message persistence", "search functionality"]

  templates:
    email_templates:
      - directory: "app/templates/passenger/"
        description: "Customer-facing email templates"
        templates: ["booking_confirmation", "status_updates", "pre_flight_reminders", "post_flight_follow_up"]
        features: ["personalization", "branding", "responsive design"]
      
      - directory: "app/templates/operator/"
        description: "Operator communication templates"
        templates: ["quote_requests", "booking_notifications", "performance_updates", "relationship_management"]
        features: ["persona-driven content", "professional formatting", "attachment support"]
      
      - directory: "app/templates/system/"
        description: "System notification templates"
        templates: ["alerts", "maintenance_notifications", "performance_reports", "error_notifications"]
        features: ["technical formatting", "severity indicators", "action items"]
      
      - directory: "app/templates/auth/"
        description: "Authentication and security templates"
        templates: ["login_codes", "registration_welcome", "password_reset", "security_alerts"]
        features: ["security compliance", "clear instructions", "brand consistency"]

  legacy_services:
    legacy_communication:
      - file: "app/services/legacy_services/communication.py"
        lines: 416
        description: "Legacy communication service implementation"
        methods: 10
        features: ["backward compatibility", "migration support", "legacy API integration"]
      
      - file: "app/services/legacy_services/notifications.py"
        lines: 373
        description: "Legacy notification service implementation"
        methods: 12
        features: ["legacy notification handling", "migration utilities", "compatibility layer"]
      
      - file: "app/services/legacy_services/email_threads.py"
        lines: 710
        description: "Legacy email thread management implementation"
        methods: 15
        features: ["thread migration", "legacy data handling", "compatibility support"]

endpoints:
  notification_management:
    - path: "/api/v1/communication/notifications"
      methods: ["GET", "POST"]
      description: "User notification management and creation"
      response_time: "<500ms"
      authentication: "required"
      features: ["notification listing", "preference management", "creation"]
    
    - path: "/api/v1/communication/notifications/count"
      methods: ["GET"]
      description: "Notification count and unread status"
      response_time: "<200ms"
      authentication: "required"
      features: ["count retrieval", "unread tracking"]
    
    - path: "/api/v1/communication/notifications/mark-read/{notification_id}"
      methods: ["POST"]
      description: "Mark notification as read"
      response_time: "<300ms"
      authentication: "required"
      features: ["status updates", "read tracking"]
    
    - path: "/api/v1/communication/notifications/preferences"
      methods: ["POST"]
      description: "Update notification preferences"
      response_time: "<400ms"
      authentication: "required"
      features: ["preference management", "channel configuration"]

  chat_interface:
    - path: "/api/v1/communication/chat"
      methods: ["POST"]
      description: "Process chat messages with AI integration"
      response_time: "<2s"
      authentication: "optional"
      features: ["intent parsing", "AI responses", "session management"]
    
    - path: "/api/v1/communication/chat/history/{session_key}"
      methods: ["GET"]
      description: "Retrieve chat conversation history"
      response_time: "<500ms"
      authentication: "optional"
      features: ["history retrieval", "message filtering"]
    
    - path: "/api/v1/communication/chat/clear/{session_key}"
      methods: ["POST"]
      description: "Clear chat session and start fresh"
      response_time: "<300ms"
      authentication: "optional"
      features: ["session reset", "history clearing"]
    
    - path: "/api/v1/communication/chat/persona/{session_key}"
      methods: ["POST"]
      description: "Set AI agent persona for chat session"
      response_time: "<400ms"
      authentication: "optional"
      features: ["persona management", "conversation style"]

  email_management:
    - path: "/api/v1/communication/email-providers/status"
      methods: ["GET"]
      description: "Email provider status and configuration"
      response_time: "<300ms"
      authentication: "required"
      features: ["provider status", "configuration validation"]
    
    - path: "/api/v1/communication/email-providers/test"
      methods: ["POST"]
      description: "Test email provider configuration"
      response_time: "<5s"
      authentication: "required"
      features: ["provider testing", "configuration validation"]

  webhook_processing:
    - path: "/api/v1/communication/mailgun/events"
      methods: ["POST"]
      description: "Process Mailgun delivery tracking webhooks"
      response_time: "<1s"
      authentication: "webhook_signature"
      features: ["delivery tracking", "event processing", "analytics updates"]
    
    - path: "/api/v1/communication/mailgun/webhook"
      methods: ["POST"]
      description: "Process inbound email webhooks from Mailgun"
      response_time: "<3s"
      authentication: "api_key"
      features: ["inbound email processing", "thread management"]
    
    - path: "/api/v1/communication/gmail/webhook"
      methods: ["POST"]
      description: "Process Gmail webhook notifications"
      response_time: "<2s"
      authentication: "oauth"
      features: ["Gmail integration", "thread synchronization"]

database_models:
  message_threading:
    message_threads:
      table: "message_threads"
      description: "Conversation tracking with operator and customer contexts"
      key_fields: ["id", "subject", "recipient_email", "thread_type", "status", "agent_persona"]
      relationships: ["messages", "booking", "operator", "user"]
      indexes: ["recipient_email", "thread_type", "status", "last_message_at"]
      features: ["conversation management", "persona tracking", "status workflow"]
    
    thread_messages:
      table: "thread_messages"
      description: "Individual messages within conversation threads"
      key_fields: ["id", "thread_id", "content", "direction", "from_email", "to_email"]
      relationships: ["thread"]
      indexes: ["thread_id", "direction", "received_at", "message_id"]
      features: ["message tracking", "attachment support", "quote extraction"]

  notification_system:
    notifications:
      table: "notifications"
      description: "Multi-channel notification tracking and delivery"
      key_fields: ["id", "user_id", "title", "body", "notification_type", "channel", "status"]
      relationships: ["user", "quote", "booking"]
      indexes: ["user_id", "notification_type", "status", "scheduled_for"]
      features: ["multi-channel delivery", "scheduling", "tracking", "analytics"]
    
    notification_preferences:
      table: "notification_preferences"
      description: "User preferences for notification delivery"
      key_fields: ["id", "user_id", "notification_type", "channel", "enabled"]
      relationships: ["user"]
      indexes: ["user_id", "notification_type", "channel"]
      constraints: ["unique(user_id, notification_type, channel)"]
      features: ["preference management", "channel configuration", "granular controls"]

  email_infrastructure:
    email_templates:
      table: "email_templates"
      description: "Template storage with performance analytics"
      key_fields: ["id", "name", "slug", "subject", "file_path", "template_type", "is_active"]
      relationships: ["email_sent_logs"]
      indexes: ["name", "slug", "template_type", "is_active"]
      features: ["template management", "version control", "performance tracking"]
    
    email_sent_logs:
      table: "email_sent_logs"
      description: "Email delivery tracking with webhook event processing"
      key_fields: ["id", "template_id", "mailgun_message_id", "recipient_email", "subject"]
      relationships: ["template"]
      indexes: ["mailgun_message_id", "template_id", "sent_at", "recipient_email"]
      features: ["delivery tracking", "webhook processing", "analytics", "performance metrics"]

  chat_system:
    chat_sessions:
      table: "chat_sessions"
      description: "Chat session management with AI integration"
      key_fields: ["id", "session_key", "user_id", "status", "agent_persona"]
      relationships: ["messages", "user"]
      indexes: ["session_key", "user_id", "status", "created_at"]
      features: ["session persistence", "persona management", "user association"]
    
    chat_messages:
      table: "chat_messages"
      description: "Chat message persistence with AI metadata"
      key_fields: ["id", "session_id", "content", "role", "intent_type", "confidence"]
      relationships: ["session"]
      indexes: ["session_id", "created_at", "role", "intent_type"]
      features: ["message persistence", "AI integration", "intent tracking"]

services:
  communication_orchestration:
    communication_service:
      class: "CommunicationService"
      file: "app/services/communication_service.py"
      description: "Primary communication orchestration with multi-channel delivery"
      methods:
        - "send_passenger_notification(notification: PassengerNotificationRequest) -> NotificationResponse"
        - "send_operator_notification(notification: OperatorNotificationRequest) -> NotificationResponse"
        - "send_system_notification(notification: SystemNotificationRequest) -> NotificationResponse"
        - "get_communication_history(request: CommunicationHistoryRequest) -> CommunicationHistoryResponse"
      features: ["notification orchestration", "template integration", "delivery tracking", "history management"]
    
    email_service:
      class: "EmailService"
      file: "app/services/email_service.py"
      description: "Multi-provider email delivery with fallback and tracking"
      methods:
        - "send_email(to_email, subject, html_content, **kwargs) -> Dict[str, Any]"
        - "send_template_email(to_email, template_identifier, context, subject, **kwargs) -> Dict[str, Any]"
        - "send_login_code_email(to_email, login_code) -> Dict[str, Any]"
        - "send_registration_welcome_email(to_email, first_name, last_name, **kwargs) -> Dict[str, Any]"
        - "process_inbound_email(webhook_data: Dict[str, Any]) -> Dict[str, Any]"
      features: ["multi-provider delivery", "template integration", "webhook tracking", "fallback handling"]
    
    notifications_service:
      class: "NotificationService"
      file: "app/services/notifications_service.py"
      description: "Multi-channel notification delivery with preference management"
      methods:
        - "create_notification(user_id, notification_type, title, body, **kwargs) -> NotificationSchema"
        - "send_notification(notification_id: UUID) -> Dict[str, Any]"
        - "process_pending_notifications(max_notifications: int = 50) -> Dict[str, Any]"
        - "get_user_notifications(user_id, limit, offset, include_read) -> List[NotificationSchema]"
        - "set_notification_preferences(user_id, notification_type, channel, enabled, settings) -> NotificationPreference"
      features: ["multi-channel delivery", "preference management", "batch processing", "analytics"]

  template_management:
    template_service:
      class: "TemplateService"
      file: "app/services/template_service.py"
      description: "Template rendering and management with database storage"
      methods:
        - "render_template(template_name: str, context: Dict[str, Any]) -> str"
        - "create_email_template(name, subject, template_type, file_path, **kwargs) -> Dict[str, Any]"
        - "get_email_templates(page: int = 1, per_page: int = 20) -> Tuple[List[Dict], int]"
        - "get_email_template(template_id: UUID) -> Optional[Dict[str, Any]]"
        - "send_template_email(template_name, recipient_email, context_data, **kwargs) -> Dict[str, Any]"
        - "preview_template(template_name: str, context_data: Dict[str, Any]) -> Dict[str, Any]"
      features: ["template rendering", "database storage", "performance analytics", "fallback handling"]

  message_threading:
    email_threads_service:
      class: "EmailThreadService"
      file: "app/services/email_threads_service.py"
      description: "Email thread management with AI-powered conversation tracking"
      methods:
        - "create_thread(thread_data: MessageThreadCreate) -> Dict[str, Any]"
        - "add_message(thread_id, content, direction, from_email, to_email, **kwargs) -> Dict[str, Any]"
        - "get_thread_with_messages(thread_id: UUID) -> Tuple[Dict, List[Dict]]"
        - "generate_follow_up(thread_id: UUID, context: Optional[Dict] = None) -> Dict[str, Any]"
        - "process_scheduled_followups() -> int"
        - "find_threads_needing_followup(**kwargs) -> List[MessageThread]"
      features: ["thread management", "AI integration", "follow-up automation", "quote extraction"]
    
    operator_communication_service:
      class: "OperatorCommunicationService"
      file: "app/services/operator_communication_service.py"
      description: "Specialized operator communication with persona management"
      methods:
        - "send_quote_request(operator_id, quote_id, request_details) -> Dict[str, Any]"
        - "track_operator_response(thread_id, response_data) -> Dict[str, Any]"
        - "update_operator_reliability(operator_id, response_metrics) -> Dict[str, Any]"
        - "get_operator_communication_history(operator_id) -> List[Dict[str, Any]]"
      features: ["quote request automation", "persona management", "response tracking", "reliability scoring"]

  webhook_processing:
    mailgun_webhook_service:
      class: "MailgunWebhookService"
      file: "app/services/mailgun_webhook_service.py"
      description: "Mailgun webhook event processing with signature verification"
      methods:
        - "process_webhook_event(webhook_data: Dict[str, Any]) -> Dict[str, Any]"
        - "update_email_template_stats(template_id, event_type, event_data) -> None"
        - "update_email_sent_log(message_id, event_type, event_data) -> None"
        - "process_delivery_event(event_data: Dict[str, Any]) -> Dict[str, Any]"
        - "process_engagement_event(event_data: Dict[str, Any]) -> Dict[str, Any]"
      features: ["webhook processing", "signature verification", "analytics updates", "event handling"]

repositories:
  communication_data_access:
    message_thread_repository:
      class: "MessageThreadRepository"
      file: "app/db/manager/repositories/message_thread_repository.py"
      description: "Message thread data access with conversation management"
      methods:
        - "create_thread(thread_data: MessageThreadCreate) -> MessageThread"
        - "add_message(message_data: ThreadMessageCreate) -> ThreadMessage"
        - "get_thread_by_id(thread_id: UUID) -> Optional[MessageThread]"
        - "get_thread_with_messages(thread_id: UUID) -> Optional[Tuple[MessageThread, List[ThreadMessage]]]"
        - "find_thread(operator_id, booking_id, thread_type) -> Optional[MessageThread]"
        - "update_thread(thread_id: UUID, update_data: Dict) -> MessageThread"
        - "update_message(message_id: UUID, update_data: Dict) -> ThreadMessage"
        - "get_threads_for_entity(entity_type, entity_id, **kwargs) -> List[Tuple[MessageThread, List[ThreadMessage]]]"
      features: ["thread CRUD", "message management", "search functionality", "relationship handling"]
    
    notification_repository:
      class: "NotificationRepository"
      file: "app/db/manager/repositories/notification_repository.py"
      description: "Notification management with preference handling"
      methods:
        - "create(notification_data: NotificationCreate) -> Notification"
        - "get_by_id(notification_id: UUID) -> Optional[Notification]"
        - "get_user_notifications(user_id, limit, offset, include_read) -> List[Notification]"
        - "get_pending_notifications(limit: int = 50) -> List[Notification]"
        - "update_status(notification_id: UUID, status: NotificationStatusEnum) -> Notification"
        - "create_preference(preference_data: NotificationPreferenceCreate) -> NotificationPreference"
        - "get_user_preferences(user_id: UUID) -> List[NotificationPreference]"
        - "update_preference(preference_id: UUID, preference_data: NotificationPreferenceUpdate) -> NotificationPreference"
      features: ["notification CRUD", "preference management", "status tracking", "batch operations"]
    
    email_template_repository:
      class: "EmailTemplateRepository"
      file: "app/db/manager/repositories/email_template_repository.py"
      description: "Email template management with performance tracking"
      methods:
        - "create(template_data: EmailTemplateCreate) -> EmailTemplate"
        - "get_by_id(template_id: UUID) -> Optional[EmailTemplate]"
        - "get_by_name(name: str) -> Optional[EmailTemplate]"
        - "get_by_slug(slug: str) -> Optional[EmailTemplate]"
        - "list_templates(page, per_page, filters) -> Tuple[List[EmailTemplate], int]"
        - "update(template_id: UUID, template_data: EmailTemplateUpdate) -> EmailTemplate"
        - "update_stats(template_id: UUID, stat_type: str, increment: int = 1) -> None"
        - "get_template_analytics(template_id: UUID) -> Dict[str, Any]"
      features: ["template CRUD", "performance tracking", "analytics", "search functionality"]

scheduler_integration:
  communication_automation:
    - task: "Process Pending Notifications"
      schedule: "every 5 minutes"
      purpose: "Process queued notifications and deliver via appropriate channels"
      handler: "notifications_service.py:process_pending_notifications"
      batch_size: 50
      features: ["batch processing", "channel routing", "delivery tracking", "error handling"]
    
    - task: "Retry Failed Notifications"
      schedule: "every 30 minutes"
      purpose: "Retry failed notification deliveries with exponential backoff"
      handler: "notifications_service.py:retry_failed_notifications"
      max_retries: 3
      features: ["retry logic", "exponential backoff", "failure analysis", "dead letter queue"]
    
    - task: "Process Email Followups"
      schedule: "every 60 minutes"
      purpose: "Generate and send automated follow-up emails for operator threads"
      handler: "email_threads_service.py:process_scheduled_followups"
      max_threads: 50
      features: ["follow-up automation", "AI generation", "persona consistency", "timing optimization"]
    
    - task: "Update Template Analytics"
      schedule: "every 15 minutes"
      purpose: "Process webhook events and update email template performance metrics"
      handler: "mailgun_webhook_service.py:process_analytics_updates"
      batch_size: 100
      features: ["analytics processing", "performance tracking", "webhook integration", "metrics calculation"]
    
    - task: "Clean Message History"
      schedule: "daily 2:00 AM"
      purpose: "Archive old messages and clean up conversation threads"
      handler: "message_thread_repository.py:cleanup_old_threads"
      retention_days: 365
      features: ["data cleanup", "archival", "performance optimization", "storage management"]

implementation_status:
  completed_features:
    core_infrastructure: 95%
    email_delivery: 90%
    template_management: 85%
    notification_system: 88%
    webhook_integration: 92%
    message_threading: 87%
    chat_interface: 83%
    operator_communication: 89%
    
  current_gaps:
    sms_integration: "SMS delivery not implemented - placeholder methods exist"
    push_notifications: "Push notification infrastructure not configured"
    advanced_analytics: "Advanced analytics dashboard and reporting incomplete"
    internationalization: "Multi-language template support partially implemented"
    a_b_testing: "Template A/B testing framework not implemented"
    
  planned_features:
    enhanced_ai_integration: "Advanced AI conversation analysis and optimization"
    marketing_automation: "Comprehensive marketing campaign management"
    social_media_integration: "Social platform communication channels"
    voice_communication: "Voice call integration and management"
    video_communication: "Video conferencing integration"
    
  performance_metrics:
    email_delivery_rate: ">99%"
    average_delivery_time: "<3 seconds"
    webhook_processing_time: "<1 second"
    template_rendering_time: "<500ms"
    notification_queue_processing: "<5 minutes"
    chat_response_time: "<2 seconds"
    
  monitoring_and_alerting:
    delivery_rate_monitoring: "Real-time monitoring with alerts for <95% delivery rate"
    provider_health_checks: "Continuous monitoring of email provider availability"
    webhook_processing_alerts: "Alerts for webhook processing failures or delays"
    template_performance_tracking: "Performance analytics with optimization recommendations"
    conversation_quality_metrics: "AI-powered conversation analysis and improvement suggestions"

error_handling:
  communication_failures:
    - error_type: "EmailDeliveryFailure"
      description: "Email delivery failed through primary provider"
      recovery: "Automatic failover to backup provider with retry logic"
      monitoring: "Real-time alerts and provider health monitoring"
    
    - error_type: "TemplateRenderingError"
      description: "Template rendering failed due to syntax or data issues"
      recovery: "Fallback to generic template with error logging and notification"
      monitoring: "Template error tracking with developer notifications"
    
    - error_type: "WebhookSignatureVerificationFailure"
      description: "Webhook signature verification failed for security"
      recovery: "Reject webhook with security alert and logging"
      monitoring: "Security event monitoring with immediate alerts"
    
    - error_type: "NotificationPreferenceViolation"
      description: "Attempted notification delivery violates user preferences"
      recovery: "Block delivery and log preference violation"
      monitoring: "Preference compliance monitoring and reporting"
    
    - error_type: "ConversationThreadCorruption"
      description: "Message thread data integrity issues detected"
      recovery: "Thread reconstruction from message history with data validation"
      monitoring: "Data integrity monitoring with automatic repair mechanisms"

integration_points:
  booking_lifecycle_integration:
    - trigger: "booking_status_change"
      action: "send_passenger_notification"
      template: "booking_status_update"
      channels: ["email", "push"]
    
    - trigger: "flight_departure"
      action: "send_tracking_notification"
      template: "flight_tracking"
      channels: ["email", "sms"]
    
    - trigger: "booking_completion"
      action: "send_feedback_request"
      template: "post_flight_feedback"
      channels: ["email"]

  operator_workflow_integration:
    - trigger: "quote_request_created"
      action: "send_operator_notification"
      template: "quote_request"
      persona: "formal"
    
    - trigger: "operator_response_received"
      action: "process_quote_extraction"
      service: "email_threads_service"
      ai_integration: true
    
    - trigger: "operator_non_responsive"
      action: "send_follow_up"
      template: "follow_up_reminder"
      escalation: true

  authentication_integration:
    - trigger: "login_code_requested"
      action: "send_login_code_email"
      template: "login_code"
      priority: "high"
    
    - trigger: "user_registration"
      action: "send_welcome_email"
      template: "registration_welcome"
      scheduling: "immediate"
    
    - trigger: "security_alert"
      action: "send_security_notification"
      template: "security_alert"
      channels: ["email", "sms"]