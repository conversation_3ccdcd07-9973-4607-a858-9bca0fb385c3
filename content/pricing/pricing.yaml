system: "pricing"
description: |
  Comprehensive pricing intelligence domain managing sophisticated multi-factor cost calculations,
  machine learning-powered price estimation, dynamic market adjustments, and intelligent discount management.
  Integrates aircraft specifications, operator pricing rules, seasonal factors, and market dynamics to deliver
  accurate, competitive pricing with transparent breakdowns and real-time optimization capabilities.

intent_assertions:
  primary_functions:
    - "Deliver sophisticated multi-factor pricing calculations with aircraft-specific rates, route optimization, seasonal adjustments, and market dynamics"
    - "Provide ML-powered price estimation with continuous learning from historical data, market trends, and conversion performance"
    - "Enable dynamic pricing optimization with real-time market adjustments, demand forecasting, and competitive positioning"
    - "Support comprehensive discount code management with validation, usage tracking, and promotional campaign optimization"
    - "Facilitate intelligent price comparison across aircraft categories, operators, and booking configurations"
    - "Maintain pricing model intelligence with automated rebuilding, performance tuning, and accuracy improvement"

  business_value:
    - "Maximize revenue through intelligent pricing strategies that balance competitiveness with profitability"
    - "Enhance customer experience with transparent pricing breakdowns, accurate estimates, and competitive quote generation"
    - "Optimize operator relationships through fair pricing, performance-based adjustments, and market-driven fee structures"
    - "Enable data-driven pricing decisions with comprehensive analytics, trend analysis, and predictive modeling"
    - "Support promotional strategies with flexible discount systems, usage analytics, and conversion optimization"
    - "Ensure pricing accuracy and consistency across all customer touchpoints and booking channels"

  system_capabilities:
    - "Multi-engine pricing architecture with PricingService, PriceEstimationService, and PricingOptimizerService integration"
    - "Comprehensive pricing breakdown with base costs, fuel surcharges, airport fees, positioning costs, and operator margins"
    - "Dynamic adjustment framework with global, category-specific, route-specific, and operator-specific multipliers"
    - "Machine learning pricing models with continuous improvement, historical analysis, and market trend integration"
    - "Real-time quote competitiveness assessment with market positioning and adjustment recommendations"
    - "Advanced discount management with code validation, usage tracking, restriction enforcement, and campaign analytics"

technical_assertions:
  database_models:
    - "PricingRule - Operator-specific pricing rules with JSON configuration, activation status, and rule metadata"
    - "PricingAdjustment - Dynamic pricing adjustments with type-specific factors, entity targeting, and effective periods"
    - "SeasonalMultiplier - Time-based pricing multipliers with regional variations, date ranges, and seasonal factors"
    - "PricingModel - ML pricing models with versioning, training data, performance metrics, and activation status"
    - "QuotePrice - Individual quote pricing with breakdown details, calculation metadata, and validation status"
    - "PriceComponent - Itemized pricing components with category classification, calculation methods, and fee structures"
    - "DiscountCode - Promotional discount codes with validation rules, usage limits, restrictions, and performance tracking"

  api_endpoints:
    - "/pricing/estimate - ML-powered price estimation with category-based ranges, market analysis, and confidence intervals"
    - "/pricing/calculate - Detailed price calculation for specific aircraft with comprehensive breakdown and fee itemization"
    - "/pricing/compare - Multi-aircraft price comparison with sorting, filtering, and competitive analysis"
    - "/pricing/discount/check - Discount code validation with eligibility verification, amount calculation, and restriction enforcement"
    - "/pricing/discount - Admin endpoints for discount code management with CRUD operations and usage analytics"

  service_architecture:
    - "PricingService - Core pricing engine with quote calculations, model management, and fee processing"
    - "PriceEstimationService - Advanced estimation with ML tuning, adjustment factors, and market optimization"
    - "PricingOptimizerService - Dynamic optimization with competitiveness assessment, fee reduction, and operator notifications"
    - "PricingRepository - Database operations with complex queries, pricing analytics, and model persistence"
    - "PricingAdjustmentRepository - Dynamic adjustment management with factor calculations and update operations"

  integration_points:
    - "Aircraft integration for specifications, hourly rates, fuel consumption, and range calculations"
    - "Operator integration for pricing rules, commission structures, and performance-based adjustments"
    - "Airport integration for fee structures, handling charges, positioning costs, and operational constraints"
    - "Quote integration for price calculations, breakdown generation, and competitiveness assessment"
    - "Booking integration for final pricing, discount application, and payment processing coordination"
    - "Analytics integration for pricing performance, conversion tracking, and optimization insights"

behavior:
  price_calculation_workflow:
    basic_price_estimation:
      - "Route distance calculation using great circle algorithms with accuracy validation and fallback mechanisms"
      - "Aircraft selection and hourly rate determination with category fallbacks and performance characteristics"
      - "Flight time estimation based on aircraft speed, route complexity, and operational constraints"
      - "Base flight cost calculation with hourly rates, flight time, and minimum pricing thresholds"
      - "Airport fee integration with origin/destination charges, handling fees, and passenger-specific costs"
      - "Global pricing adjustments with market conditions, demand factors, and seasonal multipliers"

    advanced_price_calculation:
      - "Multi-factor pricing engine with aircraft-specific rates, operator rules, and route optimization"
      - "Dynamic adjustment application with category-specific, route-specific, and operator-specific multipliers"
      - "Seasonal pricing with regional variations, peak season adjustments, and demand forecasting"
      - "Empty leg integration with discount calculations, positioning optimization, and availability matching"
      - "Fuel surcharge calculation with current market rates, route distance, and aircraft consumption"
      - "Comprehensive fee breakdown with transparent itemization and cost component attribution"

    pricing_intelligence_workflow:
      - "Historical pricing analysis with trend identification, performance correlation, and market positioning"
      - "ML model training with quote data, conversion rates, and market feedback integration"
      - "Competitive analysis with market rate comparison, positioning assessment, and adjustment recommendations"
      - "Price optimization with conversion rate analysis, profit margin optimization, and market responsiveness"
      - "Model performance monitoring with accuracy tracking, prediction validation, and continuous improvement"
      - "Automated model rebuilding with data quality validation, training pipeline execution, and deployment automation"

  discount_management_workflow:
    discount_code_creation:
      - "Administrative discount code creation with validation rules, usage limits, and restriction configuration"
      - "Flexible discount types with percentage-based, fixed-amount, and conditional discount structures"
      - "Restriction management with aircraft category, region, operator, trip type, and passenger count limitations"
      - "Usage tracking setup with limit enforcement, expiration management, and performance monitoring"
      - "Campaign integration with promotional strategies, marketing attribution, and conversion tracking"

    discount_validation_workflow:
      - "Real-time discount code validation with existence verification, activation status, and expiration checking"
      - "Eligibility assessment with booking criteria validation, restriction enforcement, and qualification verification"
      - "Discount calculation with percentage/fixed amount computation, maximum limits, and booking value validation"
      - "Usage increment with atomic operations, limit enforcement, and concurrent access protection"
      - "Audit trail maintenance with validation history, usage tracking, and performance analytics"

  pricing_optimization_workflow:
    competitiveness_assessment:
      - "Quote competitiveness analysis with historical comparison, market positioning, and pricing trend evaluation"
      - "Price variance calculation with statistical analysis, outlier detection, and market context evaluation"
      - "Competitive ranking with peer comparison, market differential assessment, and positioning optimization"
      - "Adjustment recommendation generation with price reduction suggestions and conversion likelihood analysis"
      - "Operator notification with automated communication, pricing feedback, and market intelligence sharing"

    dynamic_fee_optimization:
      - "Platform fee calculation with multi-factor analysis, user loyalty assessment, and conversion optimization"
      - "Dynamic fee reduction with competitiveness analysis, conversion probability, and profit margin protection"
      - "Real-time fee optimization with market conditions, demand patterns, and booking success correlation"
      - "Performance tracking with conversion rate monitoring, revenue impact analysis, and optimization effectiveness"
      - "Strategic fee management with minimum thresholds, maximum reductions, and profitability safeguards"

invariants:
  - "All pricing calculations must use valid aircraft data with verified hourly rates and operational specifications"
  - "Route distance calculations must be accurate within 1% margin with great circle methodology validation"
  - "Pricing adjustments must be mathematically consistent with non-negative factors and valid ranges"
  - "Discount codes must be validated for authenticity, activation status, and eligibility criteria before application"
  - "Fee calculations must maintain transparency with itemized breakdowns and component attribution"
  - "Pricing models must be versioned with training data lineage and performance validation"
  - "All monetary values must be handled with proper precision to avoid rounding errors"
  - "Seasonal multipliers must have valid date ranges with no gaps or overlaps"
  - "Airport fees must be non-negative with proper currency and regional validation"
  - "Quote pricing must include all mandatory cost components with comprehensive breakdown"

forbidden_states:
  - "Pricing calculations with missing aircraft data or invalid hourly rates"
  - "Route calculations returning zero or negative distances for valid airport pairs"
  - "Pricing adjustments with negative factors or mathematically impossible configurations"
  - "Discount codes with expired dates, exceeded usage limits, or invalid restriction criteria"
  - "Fee calculations without proper breakdown or missing cost components"
  - "Pricing models without version control or training data validation"
  - "Quote prices without competitiveness assessment or market positioning analysis"
  - "Adjustment factors outside valid ranges or with inconsistent calculation methods"
  - "Discount applications without proper validation or eligibility verification"
  - "Pricing operations without audit trails or change tracking"

depends_on:
  - core
  - authentication
  - aircraft
  - operator
  - airport
  - quote
  - booking
  - analytics

provides:
  - "Comprehensive pricing engine with multi-factor calculation and market optimization"
  - "ML-powered price estimation with continuous learning and accuracy improvement"
  - "Dynamic pricing adjustments with real-time market responsiveness"
  - "Advanced discount management with validation, tracking, and campaign analytics"
  - "Pricing intelligence with competitive analysis and optimization recommendations"
  - "Transparent fee breakdown with itemized cost components and calculation methodology"
  - "Real-time quote competitiveness assessment with market positioning insights"
  - "Automated pricing model management with performance monitoring and rebuilding"

enforcement_hooks:
  pre_price_calculation:
    - hook: "validate_pricing_parameters"
      purpose: "Validate pricing parameters with aircraft verification, route validation, and data completeness"
      triggers: ["price_calculation", "quote_generation", "estimate_request"]
    
    - hook: "verify_aircraft_specifications"
      purpose: "Verify aircraft specifications with hourly rates, fuel consumption, and operational data"
      triggers: ["aircraft_pricing", "rate_calculation", "specification_lookup"]
    
    - hook: "check_route_validity"
      purpose: "Check route validity with airport existence, distance calculation, and operational feasibility"
      triggers: ["route_pricing", "distance_calculation", "flight_planning"]

  pre_discount_application:
    - hook: "validate_discount_code"
      purpose: "Validate discount code with existence verification, activation status, and expiration checking"
      triggers: ["discount_application", "code_validation", "booking_discount"]
    
    - hook: "verify_discount_eligibility"
      purpose: "Verify discount eligibility with restriction enforcement and qualification assessment"
      triggers: ["eligibility_check", "restriction_validation", "discount_qualification"]
    
    - hook: "check_usage_limits"
      purpose: "Check usage limits with current count validation and limit enforcement"
      triggers: ["usage_validation", "limit_check", "discount_application"]

  pre_pricing_adjustment:
    - hook: "validate_adjustment_factors"
      purpose: "Validate adjustment factors with range checking, consistency validation, and mathematical correctness"
      triggers: ["adjustment_update", "factor_modification", "pricing_rule_change"]
    
    - hook: "verify_adjustment_permissions"
      purpose: "Verify adjustment permissions with admin authorization and approval workflows"
      triggers: ["adjustment_authorization", "permission_check", "admin_validation"]

  post_price_calculation:
    - hook: "log_pricing_calculation"
      purpose: "Log pricing calculation with audit trail, performance metrics, and accuracy tracking"
      triggers: ["calculation_completion", "price_generation", "quote_creation"]
    
    - hook: "update_pricing_analytics"
      purpose: "Update pricing analytics with calculation metrics, performance data, and trend analysis"
      triggers: ["analytics_update", "metric_collection", "performance_tracking"]
    
    - hook: "assess_quote_competitiveness"
      purpose: "Assess quote competitiveness with market comparison and positioning analysis"
      triggers: ["competitiveness_analysis", "market_assessment", "quote_evaluation"]

  post_discount_application:
    - hook: "increment_usage_count"
      purpose: "Increment usage count with atomic operations and concurrent access protection"
      triggers: ["usage_increment", "count_update", "discount_applied"]
    
    - hook: "track_discount_performance"
      purpose: "Track discount performance with conversion metrics and campaign analytics"
      triggers: ["performance_tracking", "conversion_measurement", "campaign_analysis"]

implementation_gaps:
  real_time_market_integration:
    status: "enhancement_opportunity"
    description: "Real-time market data integration for dynamic pricing adjustments"
    current_state: "Static pricing models with periodic updates"
    enhancement: "Live market data feeds with real-time pricing optimization"
    priority: "medium"
    estimated_effort: "6-8 weeks"

  advanced_ml_optimization:
    status: "enhancement_opportunity"
    description: "Advanced machine learning models for pricing optimization"
    current_state: "Basic ML models with historical data analysis"
    enhancement: "Deep learning models with multi-factor optimization and real-time learning"
    priority: "low"
    estimated_effort: "8-10 weeks"

  predictive_demand_pricing:
    status: "planned_future"
    description: "Predictive demand-based pricing with market forecasting"
    current_state: "Seasonal adjustments with basic demand factors"
    enhancement: "AI-powered demand prediction with dynamic pricing optimization"
    priority: "low"
    estimated_effort: "10-12 weeks"

implementation_reports:
  pricing_engine_consolidation:
    status: "completed"
    description: "Consolidated pricing functionality into unified service architecture"
    completion_date: "2024-12-15"
    impact: "Improved pricing consistency, reduced code duplication, enhanced maintainability"
    components_affected: ["PricingService", "PriceEstimationService", "pricing repositories"]

  discount_management_system:
    status: "completed"
    description: "Implemented comprehensive discount code management with validation and tracking"
    completion_date: "2024-11-20"
    impact: "Enabled promotional campaigns, improved conversion tracking, enhanced user experience"
    components_affected: ["DiscountCode model", "pricing API endpoints", "validation workflows"]

  pricing_adjustment_framework:
    status: "completed"
    description: "Dynamic pricing adjustment system with multi-factor optimization"
    completion_date: "2024-10-30"
    impact: "Flexible pricing rules, market responsiveness, competitive positioning"
    components_affected: ["PricingAdjustment model", "adjustment repository", "calculation engine"]

primary_flow:
  trigger_event: "Price calculation request (estimate, detailed calculation, or comparison)"
  
  flow_sequence:
    1. "Parameter validation with aircraft verification, route checking, and data completeness assessment"
    2. "Route analysis with distance calculation, airport fee lookup, and operational constraint validation"
    3. "Aircraft specification retrieval with hourly rates, fuel consumption, and performance characteristics"
    4. "Base price calculation with flight time estimation, hourly rate application, and minimum threshold validation"
    5. "Dynamic adjustment application with global, category, route, and operator-specific multipliers"
    6. "Fee integration with airport charges, fuel surcharges, positioning costs, and passenger-specific fees"
    7. "Discount validation and application with code verification, eligibility assessment, and amount calculation"
    8. "Competitiveness assessment with market comparison, positioning analysis, and optimization recommendations"
    9. "Final price compilation with breakdown generation, audit logging, and analytics update"
    10. "Response delivery with structured pricing data, transparency information, and market insights"

  success_criteria:
    - "Accurate price calculation within 5% of market rates with comprehensive breakdown"
    - "Response time under 2 seconds for standard pricing requests"
    - "Complete audit trail with calculation methodology and data source attribution"
    - "Market competitiveness assessment with positioning insights and optimization recommendations"

restoration_method:
  data_recovery:
    - "Pricing model backup with version control and training data preservation"
    - "Adjustment factor backup with historical values and change tracking"
    - "Discount code backup with usage history and campaign performance data"
    - "Quote pricing backup with calculation details and market analysis"

  service_recovery:
    - "Pricing service restart with model reloading and cache warming"
    - "Database connection restoration with transaction consistency verification"
    - "API endpoint recovery with rate limiting reset and security validation"
    - "Calculation engine restart with accuracy validation and performance testing"

core_principles:
  - "Pricing transparency with itemized breakdowns and calculation methodology disclosure"
  - "Market competitiveness through intelligent analysis and dynamic optimization"
  - "Data-driven decisions with ML models, historical analysis, and performance tracking"
  - "Flexibility and adaptability with configurable rules and real-time adjustments"
  - "Accuracy and consistency across all pricing operations and customer touchpoints"
  - "Performance optimization with sub-second response times and efficient calculations"

strengths:
  - "Comprehensive multi-factor pricing engine with sophisticated calculation capabilities"
  - "Machine learning integration with continuous improvement and accuracy optimization"
  - "Dynamic adjustment framework with real-time market responsiveness"
  - "Advanced discount management with flexible rules and comprehensive tracking"
  - "Intelligent competitiveness assessment with market positioning insights"
  - "Transparent pricing breakdown with detailed fee itemization and cost attribution"
  - "Robust validation and error handling with comprehensive audit trails"
  - "High-performance architecture with efficient calculations and response times"

limitations:
  - "Limited real-time market data integration requiring manual model updates"
  - "Basic demand forecasting without advanced predictive analytics"
  - "Static seasonal adjustments without dynamic market condition adaptation"
  - "Manual operator pricing rule management without automated optimization"
  - "Limited A/B testing capabilities for pricing strategy optimization"
  - "Basic competitive analysis without real-time market monitoring"

database_models:
  - model: "PricingRule"
    purpose: "Operator-specific pricing rules with JSON configuration and activation management"
    fields: ["id", "name", "description", "is_active", "rule_data", "operator_id", "created_at", "updated_at"]
    relationships: ["operator"]
    tracking: "Rule application and performance impact"

  - model: "PricingAdjustment"
    purpose: "Dynamic pricing adjustments with type-specific factors and entity targeting"
    fields: ["id", "adjustment_type", "entity_id", "factor", "description", "effective_from", "effective_until", "is_active"]
    relationships: []
    tracking: "Adjustment application and market impact"

  - model: "SeasonalMultiplier"
    purpose: "Time-based pricing multipliers with regional variations and date ranges"
    fields: ["id", "name", "multiplier", "start_date", "end_date", "region", "description", "is_active"]
    relationships: []
    tracking: "Seasonal adjustment application and effectiveness"

  - model: "PricingModel"
    purpose: "ML pricing models with versioning, training data, and performance metrics"
    fields: ["id", "name", "version", "description", "is_active", "data", "created_at", "updated_at"]
    relationships: []
    tracking: "Model performance and accuracy metrics"

  - model: "QuotePrice"
    purpose: "Individual quote pricing with breakdown details and calculation metadata"
    fields: ["id", "quote_id", "base_price", "platform_fee", "flight_time_hours", "distance_nm", "hourly_rate", "factors"]
    relationships: ["quote"]
    tracking: "Price accuracy and conversion correlation"

  - model: "DiscountCode"
    purpose: "Promotional discount codes with validation rules and usage tracking"
    fields: ["id", "code", "description", "discount_type", "discount_value", "min_booking_value", "max_discount", "usage_limit", "usage_count", "is_active", "valid_from", "valid_to", "restrictions"]
    relationships: []
    tracking: "Usage patterns and conversion impact"

services:  
  - service: "PricingService"
    path: "app/services/pricing_service.py"
    purpose: "Core pricing engine with multi-factor cost calculation and model management"
    methods: ["calculate_quote_price", "estimate_cost", "estimate_price_range", "compare_aircraft_options", "load_pricing_model", "rebuild_pricing_model"]
    integration: "Aircraft data, operator pricing rules, airport fees, seasonal multipliers"
    
  - service: "PriceEstimationService"
    path: "app/services/price_estimation_service.py"
    purpose: "Advanced price estimation with ML tuning and market optimization"
    methods: ["estimate_price", "estimate_price_range", "update_adjustment_factors", "_get_base_hourly_rate"]
    automation: "Market-based pricing adjustments and performance optimization"
    
  - service: "PricingOptimizerService"
    path: "app/services/pricing_optimizer_service.py"
    purpose: "Dynamic pricing optimization with competitiveness assessment"
    methods: ["assess_quote_competitiveness", "notify_operator_for_adjustment", "reduce_platform_fee", "calculate_total_price"]
    optimization: "Quote competitiveness and conversion likelihood improvement"

repositories:
  - repository: "PricingRepository"
    path: "app/db/manager/repositories/pricing_repository.py"
    purpose: "Comprehensive pricing database operations with analytics and model management"
    methods: ["get_pricing_rules", "create_pricing_rule", "get_seasonal_multipliers", "calculate_flight_distance", "get_price_range_by_category", "compare_aircraft_prices"]
    integration: "Aircraft, operator, airport, and quote data relationships"
    
  - repository: "PricingAdjustmentRepository"
    path: "app/db/manager/repositories/pricing_adjustment_repository.py"
    purpose: "Dynamic pricing adjustment management with factor calculations"
    methods: ["get_adjustment_factor", "update_adjustment_factor"]
    automation: "Real-time adjustment factor application and management"

schemas:
  database_schemas:
    - "app/db/schemas/pricing.py - Complete pricing database schemas with relationships"
    
  api_schemas:
    - "app/schemas/pricing.py - Pricing API request/response models with validation"

security_modules:
  input_validation:
    - "Airport code validation with XSS protection and format verification"
    - "Aircraft ID validation with UUID format checking and injection prevention"
    - "Discount code validation with format restrictions and security scanning"
    - "Pricing parameter validation with range checking and type enforcement"
    
  access_control:
    - "User authentication required for all pricing operations"
    - "Admin authorization for discount code management and pricing rule modifications"
    - "Rate limiting on pricing endpoints to prevent abuse"
    - "Audit logging for all pricing calculations and adjustments"
    
  data_protection:
    - "Sensitive pricing data encryption at rest and in transit"
    - "Pricing model protection with access controls and versioning"
    - "Discount code security with usage tracking and fraud prevention"
    - "Calculation audit trails with immutable logging and data integrity"

performance:
  response_times:
    - "Price estimation: <1 second for 95th percentile"
    - "Detailed calculation: <2 seconds with full breakdown"
    - "Price comparison: <3 seconds for up to 50 aircraft"
    - "Discount validation: <500ms for standard checks"
    
  throughput:
    - "Concurrent pricing calculations: 100+ requests per second"
    - "Bulk price updates: 1000+ adjustments per minute"
    - "Model training: Complete rebuild in under 10 minutes"
    - "Analytics queries: Real-time performance metrics"
    
  caching:
    - "Distance calculations cached for 24 hours"
    - "Airport fee data cached for 4 hours"
    - "Pricing model data cached for 1 hour"
    - "Adjustment factors cached for 30 minutes"

consolidation_notes:
  pricing_architecture:
    - "Unified pricing engine with multiple specialized services for different use cases"
    - "Centralized pricing repository with comprehensive data access and analytics"
    - "Consistent pricing breakdown format across all calculation methods"
    - "Standardized discount management with validation and tracking workflows"
    - "Integrated competitiveness assessment with market positioning insights"

restoration_key_system:
  primary_keys:
    - "Pricing model data integrity with version control and backup systems"
    - "Adjustment factor consistency with change tracking and audit trails"
    - "Discount code security with usage validation and fraud detection"
    - "Quote pricing accuracy with calculation validation and error detection"

advanced_recovery_options:
  pricing_model_recovery:
    - "Automatic model retraining with fallback to previous stable version"
    - "Real-time accuracy monitoring with automatic adjustment triggers"
    - "Cross-validation with market data sources for accuracy verification"
    - "Progressive rollback capabilities with granular version control"
    
  calculation_error_recovery:
    - "Fallback pricing calculations with simplified algorithms"
    - "Error detection with automatic retry mechanisms"
    - "Data validation with comprehensive error reporting"
    - "Manual override capabilities for critical pricing operations"

security:
  pricing_data_protection:
    - "Encryption of sensitive pricing models and calculation parameters"
    - "Access control for pricing rule modifications and discount management"
    - "Audit logging for all pricing operations with immutable records"
    - "Fraud detection for discount code usage and pricing manipulation"
    
  api_security:
    - "Authentication required for all pricing endpoints"
    - "Rate limiting to prevent pricing data harvesting"
    - "Input validation with injection attack prevention"
    - "Response filtering to prevent sensitive data exposure"
    
  compliance:
    - "Data retention policies for pricing history and calculation audits"
    - "Privacy protection for user pricing data and booking information"
    - "Regulatory compliance for pricing transparency and disclosure"
    - "Security monitoring for pricing system integrity and availability" 