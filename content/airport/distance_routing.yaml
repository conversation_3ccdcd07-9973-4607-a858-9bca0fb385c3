system: airport_distance_routing
description: "Airport Distance and Routing Subsystem - Accurate inter-airport distance calculations with caching optimization, geographical queries, route planning support, and spatial indexing for efficient private jet route optimization"

intent_assertions:
- Accurate geodesic distance calculations between airports using geopy library
- Distance caching in AirportDistance table for frequently requested routes
- Batch distance calculations for route optimization and planning
- Spatial indexing for efficient geographical queries and radius searches
- Alternative airport suggestions based on proximity and capabilities
- Integration with pricing service for comprehensive cost calculations
- Support for route optimization and multi-leg trip planning

technical_assertions:
- path: /app/api/v1/endpoints/aircraft/airports.py
  desc: "Distance calculation API endpoints with caching and optimization"
  critical: true
  key_endpoints:
    - "POST /airports/distance - Calculate distance between airports"
    - "GET /airports/radius/{lat}/{lon} - Find airports within radius"
- path: /app/db/manager/repositories/airport_repository.py
  desc: "Distance calculation repository with caching and spatial queries"
  critical: true
  key_methods:
    - "calculate_distance with geodesic calculations and caching"
    - "find_airports_in_radius with spatial indexing"
- path: /app/services/airport_service.py
  desc: "Distance calculation service with business logic and optimization"
  critical: true
  key_methods:
    - "calculate_distance with caching optimization"
    - "find_airports_in_radius with spatial queries"
- path: /app/db/models/airport.py
  desc: "AirportDistance model for caching distance calculations"
  critical: true
  key_features:
    - "Distance caching with origin/destination UUID references"
    - "Spatial indexing for geographical coordinates"

behavior:
distance_calculation_workflow:
  - "Calculate geodesic distance using geopy library with nautical mile conversion"
  - "Check AirportDistance cache before performing new calculations"
  - "Store calculated distances in cache for future use and performance optimization"
  - "Support batch distance calculations for route optimization scenarios"
  - "Validate airport existence before performing distance calculations"

geographical_queries:
  - "Find airports within specified radius using spatial indexing"
  - "Calculate distances to multiple airports for proximity-based searches"
  - "Support coordinate-based searches for geographical route planning"
  - "Provide nearest airport detection for aircraft positioning"

route_optimization:
  - "Generate alternative airport suggestions based on proximity"
  - "Support multi-leg trip planning with distance calculations"
  - "Integration with pricing service for cost-optimized route planning"
  - "Provide route alternatives for operational flexibility"

invariants:
- Distance calculations must use geodesic formulas for accuracy
- All calculated distances must be cached for performance optimization
- Geographical queries must use proper spatial indexing
- Distance cache hit ratio must exceed 90% for common routes
- Airport existence must be validated before distance calculations

forbidden_states:
- Distance calculations without proper caching mechanism
- Geographical queries without spatial optimization
- Distance calculations between non-existent airports
- Cache storage without proper indexing and retrieval
- Batch operations without performance optimization

depends_on:
- airport: "Airport data and coordinates for distance calculations"
- external_geodesic: "Geopy library for accurate distance calculations"
- external_spatial_db: "PostgreSQL spatial extensions for geographical queries"

provides:
- distance_calculation: "Accurate inter-airport distance calculations with caching"
- geographical_queries: "Spatial airport discovery and radius-based searches"
- route_optimization: "Airport alternatives and routing support for trip planning"
- proximity_search: "Nearest airport detection and proximity-based discovery"

enforcement_hooks:
pre_distance_calculation:
  - "validate_airport_existence - Ensure both airports exist before calculation"
  - "check_distance_cache - Look for existing cached distance first"
  - "validate_coordinates - Ensure airport coordinates are valid"

post_distance_calculation:
  - "cache_distance_result - Store calculated distance for future use"
  - "update_usage_statistics - Track calculation frequency and patterns"
  - "monitor_cache_performance - Ensure cache hit ratios meet targets"

pre_geographical_query:
  - "validate_coordinates - Ensure query coordinates are within valid ranges"
  - "optimize_spatial_query - Use proper spatial indexing for performance"
  - "apply_radius_limits - Enforce reasonable radius limits"

post_geographical_query:
  - "log_query_performance - Track spatial query response times"
  - "update_spatial_statistics - Monitor spatial index efficiency"

security:
distance_security:
  - "Validate all coordinate inputs to prevent injection attacks"
  - "Rate limiting on distance calculation endpoints"
  - "Authentication required for batch distance operations"
  - "Audit logging for distance calculation usage"

geographical_security:
  - "Coordinate validation for geographical queries"
  - "Radius limits to prevent resource abuse"
  - "Rate limiting on spatial query endpoints"
  - "Query logging for security monitoring"

performance_contracts:
  distance_calculation: "<500ms with caching, <2000ms without cache"
  geographical_queries: "<800ms with spatial indexing"
  batch_distance_calculations: "<2000ms for up to 100 airport pairs"
  cache_hit_ratio: ">90% for frequently requested routes"
  spatial_query_efficiency: "<800ms for radius searches" 