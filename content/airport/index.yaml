airport_domain_index:
  description: "Villiers.ai Airport Domain - Complete airport data management ecosystem"
  
  core_system:
    path: ./airport.yaml
    description: "Primary airport domain system definition with comprehensive global airport management"
    
  subdomains:
    search_discovery:
      path: ./search_discovery.yaml
      description: "Intelligent airport search with relevance ranking and geographical proximity"
      key_capabilities:
        - "ICAO/IATA code prioritization with exact match handling"
        - "Fuzzy matching for names and cities with typo tolerance"
        - "Geographical proximity-based ranking"
        - "Region-based filtering and multi-criteria search"
        
    distance_routing:
      path: ./distance_routing.yaml
      description: "Distance calculations and geographical routing with caching optimization"
      key_capabilities:
        - "Geodesic distance calculations with nautical mile conversion"
        - "Distance caching for performance optimization"
        - "Spatial indexing for radius searches"
        - "Route optimization and alternative airport suggestions"

  domain_relationships:
    internal_dependencies:
      - "search_discovery depends on core airport data for intelligent search"
      - "distance_routing uses airport coordinates for accurate calculations"
      - "Both subdomains share common security and performance patterns"
      
    external_integrations:
      - "aircraft domain: positioning and home base relationships"
      - "booking domain: departure/arrival validation and route planning"
      - "pricing domain: fee integration and cost calculations"
      - "data_enrichment: automated metadata updates and quality management"

  shared_capabilities:
    data_validation:
      - "ICAO/IATA code format validation and uniqueness"
      - "Geographical coordinate validation and range checking"
      - "Airport metadata validation and quality assurance"
      
    performance_optimization:
      - "Intelligent caching for frequently accessed data"
      - "Spatial indexing for geographical operations"
      - "Query optimization and database performance tuning"
      
    security_measures:
      - "Input validation and sanitization across all operations"
      - "Rate limiting on all public endpoints"
      - "Authentication and authorization for sensitive operations"
      - "Comprehensive audit logging and monitoring"

  key_metrics:
    performance:
      - "Airport search: <1000ms for 95th percentile"
      - "Distance calculation: <500ms with caching"
      - "Geographical queries: <800ms with spatial indexing"
      - "Cache hit ratio: >90% for distance calculations"
      
    reliability:
      - "Search accuracy: >95% for code-based searches"
      - "Data freshness: <24 hours for enrichment updates"
      - "System availability: >99.9% uptime"
      - "Error rate: <1% for all operations"

  integration_points:
    api_endpoints:
      - "GET /api/v1/airports - Search with intelligent ranking"
      - "GET /api/v1/airports/{icao_code} - Detailed airport information"
      - "POST /api/v1/airports/distance - Distance calculations"
      - "GET /api/v1/airports/radius/{lat}/{lon} - Radius searches"
      - "GET /api/v1/airports/fees/{icao_code} - Fee information"
      - "GET /api/v1/airports/slots/{icao_code} - Slot availability"
      
    service_interfaces:
      - "AirportService: centralized business logic with database manager"
      - "AirportRepository: data access layer with comprehensive operations"
      - "AirportDataService: utility functions and caching optimization"
      
    data_models:
      - "Airport: comprehensive airport model with metadata"
      - "AirportDistance: distance caching with spatial optimization"
      - "AirportSearchResult: optimized search response format" 