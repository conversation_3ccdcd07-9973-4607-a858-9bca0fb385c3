system: airport
description: "Villiers.ai Airport Domain - Comprehensive airport data management system encompassing global airport database, geographical search, distance calculations, fee management, slot availability, and operational metadata for private jet charter route planning and optimization"

intent_assertions:
- Must provide comprehensive global airport database with ICAO/IATA code validation
- Real-time geographical search with intelligent ranking by relevance and proximity
- Accurate distance calculations between airports with caching for performance optimization
- Dynamic fee management with peak hour and weekend multipliers for cost estimation
- Slot availability tracking with operational restrictions and noise regulations
- Comprehensive airport metadata including facilities, customs, fuel types, and VIP services
- Integration with aircraft positioning for nearest airport detection and routing
- Support for alternative airport suggestions and route optimization
- Automated data enrichment from multiple sources with quality validation
- Seamless integration with booking lifecycle for departure/arrival validation

technical_assertions:
# Core Airport Management
- path: /app/api/v1/endpoints/aircraft/airports.py
  desc: "Comprehensive airport API endpoints (390 lines) - search, distance, fees, slot availability"
  critical: true
  lines: 390
  key_endpoints:
    - "GET /airports - Search airports with intelligent ranking"
    - "GET /airports/{icao_code} - Get detailed airport information"
    - "POST /airports/distance - Calculate distance between airports"
    - "GET /airports/radius/{lat}/{lon} - Find airports within radius"
    - "GET /airports/slots/{icao_code} - Check slot availability"
    - "GET /airports/fees/{icao_code} - Get airport fees and charges"
- path: /app/services/airport_service.py
  desc: "Core airport business logic service (447 lines) using centralized database manager pattern"
  critical: true
- path: /app/db/models/airport.py
  desc: "Airport database models (201 lines) - Airport, AirportDistance with comprehensive metadata"
  critical: true
  lines: 201
  key_fields:
    - "ICAO/IATA codes with validation and indexing"
    - "Geographical coordinates with spatial indexing"
    - "Comprehensive facility metadata (customs, fuel, VIP)"
    - "Dynamic fee structure with multipliers"
    - "Operational data (slots, noise restrictions, hours)"
    - "Relationships with aircraft, flights, and movements"
- path: /app/db/schemas/airport.py
  desc: "Airport API schemas (158 lines) with validation and relationship handling"
  critical: true
  lines: 158
  key_schemas:
    - "AirportBase with comprehensive field validation"
    - "AirportSearchResult for optimized search responses"
    - "AirportDistanceWithDetails for route calculations"
- path: /app/services/airport_service.py
  desc: "Core airport business logic service with centralized database management"

# Airport Data Management
- path: /app/db/manager/repositories/airport_repository.py
  desc: "Centralized airport data access layer (706 lines) with comprehensive operations"
  critical: true
  
- path: /app/services/legacy_services/airports.py
  desc: "Legacy airport service (504 lines) with direct database access patterns"
  critical: false
  status: "deprecated - migrating to centralized service"

# Airport Utilities & Caching
- path: /app/utils/airports.py
  desc: "Airport utility functions and caching (148 lines) for performance optimization"
  critical: true
- path: /app/data/airports.json
  desc: "Static airport data (202 lines) for fallback and initial seeding"
  critical: false

# Airport Data Seeding
- path: /app/db/seeders/airport_seeder.py
  desc: "Airport database seeder (320 lines) with essential global airport data"
  critical: true

behavior:
# Airport Search & Discovery
airport_search_workflow:
  - "Intelligent airport search with ICAO/IATA code prioritization"
  - "Fuzzy matching for airport names, cities, and countries"
  - "Geographical proximity-based ranking for location-aware results"
  - "Region-based filtering for localized search results"
  - "Multi-criteria search supporting partial matches and typo tolerance"

# Distance Calculations & Routing
distance_calculation_workflow:
  - "Geodesic distance calculation using geopy library with nautical mile conversion"
  - "Distance caching in AirportDistance table for frequently requested routes"
  - "Batch distance calculations for route optimization and planning"
  - "Spatial indexing for efficient geographical queries and radius searches"
  - "Alternative airport suggestions based on proximity and capabilities"

# Fee Management & Pricing
fee_management_workflow:
  - "Comprehensive fee structure (landing, handling, parking, passenger, security)"
  - "Dynamic fee calculation with peak hour and weekend multipliers"
  - "Fee caching and optimization for rapid quote generation"
  - "Integration with pricing service for total cost calculations"
  - "Historical fee tracking for pricing trend analysis"

# Slot Availability & Operational Constraints
slot_availability_workflow:
  - "Real-time slot availability checking with operational context"
  - "Noise restrictions and operational hour validation"
  - "Peak season and time-based availability assessment"
  - "Slot requirement validation for different aircraft types"
  - "Integration with booking system for conflict detection"

# Data Enrichment & Quality Management
data_enrichment_workflow:
  - "Automated airport metadata enrichment from multiple sources"
  - "Data quality validation and consistency checking"
  - "Enrichment tracking with source attribution and timestamps"
  - "Continuous data updates and synchronization processes"
  - "Fallback mechanisms for data source failures"

# Geographical Operations
geographical_operations:
  - "Radius-based airport discovery with distance calculations"
  - "Nearest airport detection for aircraft positioning"
  - "Spatial queries with PostgreSQL spatial extensions"
  - "Coordinate validation and normalization"
  - "Regional airport clustering and optimization"

invariants:
# Data Integrity
- "All airports must have valid ICAO codes (4 characters, uppercase)"
- "All airports must have valid geographical coordinates within valid ranges"
- "IATA codes must be 3 characters when present and unique"
- "Airport names and cities must be non-empty strings"
- "Distance calculations must be cached for performance optimization"

# Operational Constraints
- "Airport fees must be non-negative values"
- "Slot availability must consider operational restrictions and noise regulations"
- "Search results must be ranked by relevance and proximity"
- "Geographical queries must use proper spatial indexing"
- "Airport metadata must be validated before storage"

# System Performance
- "Distance cache hit ratio must exceed 90% for common routes"
- "Airport search response time must be under 1000ms for 95th percentile"
- "Geographical queries must complete within 800ms"
- "Fee retrieval must be under 200ms for cached data"

forbidden_states:
# Data Consistency
- "Airport with invalid ICAO code format (not 4 uppercase characters)"
- "Airport with coordinates outside valid range (-90 to 90 lat, -180 to 180 lon)"
- "Duplicate ICAO codes in the database"
- "Airport fees with negative values"
- "Airport without proper geographical coordinates"

# System Performance
- "Distance calculations without proper caching mechanism"
- "Search queries without proper validation and sanitization"
- "Geographical queries without spatial optimization"
- "Airport data modifications without proper audit logging"

# Security
- "Exposing sensitive airport operational data to unauthorized users"
- "Allowing unauthorized modifications to airport fee structures"
- "Airport search without proper rate limiting protection"

depends_on:
- aircraft: "Aircraft positioning and home base relationships"
- booking: "Departure and arrival airport validation for trips and quotes"
- pricing: "Airport fee integration for comprehensive cost calculations"
- data_enrichment: "Automated airport metadata updates and quality management"
- external_geodesic: "Distance calculation library (geopy) for accurate measurements"
- external_spatial_db: "PostgreSQL spatial extensions for geographical queries"

provides:
- airport_search: "Intelligent airport search with relevance ranking"
- distance_calculation: "Accurate inter-airport distance calculations with caching"
- fee_management: "Comprehensive airport fee structure and dynamic pricing"
- slot_availability: "Real-time slot and operational restriction checking"
- geographical_queries: "Spatial airport discovery and radius-based searches"
- airport_metadata: "Comprehensive airport operational data and facilities"
- route_optimization: "Airport alternatives and routing support for trip planning"
- data_validation: "Airport data quality and validation services"

enforcement_hooks:
# Data Validation
pre_airport_create:
  - "validate_icao_code_format - Ensure ICAO code is 4 uppercase characters"
  - "validate_geographical_coordinates - Check coordinates are within valid ranges"
  - "check_duplicate_codes - Prevent duplicate ICAO/IATA codes"
  - "validate_fee_structure - Ensure fees are non-negative and reasonable"

pre_airport_update:
  - "validate_updated_fields - Ensure all updates maintain data integrity"
  - "maintain_referential_integrity - Preserve relationships with aircraft and flights"
  - "update_enrichment_tracking - Track data modification sources and timestamps"

pre_search:
  - "validate_search_parameters - Sanitize and validate search inputs"
  - "optimize_query_performance - Ensure efficient database queries"
  - "apply_rate_limiting - Prevent search abuse and system overload"

# Performance Optimization
post_distance_calculation:
  - "cache_distance_result - Store calculated distances for future use"
  - "update_usage_statistics - Track calculation frequency and patterns"
  - "monitor_cache_performance - Ensure cache hit ratios meet targets"

post_search:
  - "log_search_analytics - Track search patterns and performance"
  - "update_search_rankings - Improve relevance based on usage patterns"

# Data Quality Management
data_quality_checks:
  - "continuous_data_validation - Regular validation of airport data integrity"
  - "enrichment_status_monitoring - Track data enrichment success and failures"
  - "geographical_accuracy_checks - Validate coordinate accuracy and consistency"

# System Maintenance
scheduled_maintenance:
  - "cleanup_expired_cache - Remove old distance calculations and cache entries"
  - "update_airport_metadata - Refresh airport operational information"
  - "validate_system_integrity - Check for data inconsistencies and repair"

security:
# Access Control
airport_data_access:
  - "Restrict airport creation/update to admin users only"
  - "Limit sensitive operational data access to authenticated users"
  - "Implement role-based access for airport management functions"
  - "Control access to fee information based on user permissions"

# API Security
api_security:
  - "Rate limiting on all airport search and query endpoints"
  - "Input validation and sanitization for all search parameters"
  - "SQL injection prevention in geographical and search queries"
  - "Authentication required for all airport modification operations"

# Data Protection
data_protection:
  - "Airport operational data encrypted at rest in database"
  - "Sensitive fee information access logged and monitored"
  - "Geographical queries rate limited to prevent abuse"
  - "Airport metadata access controlled by user authorization levels"

# Audit & Monitoring
audit_logging:
  - "Airport data modifications logged with user context and timestamps"
  - "Search queries logged for analytics and security monitoring"
  - "Distance calculations tracked for usage monitoring and optimization"
  - "Fee access logged for billing and compliance purposes"
  - "Geographical queries monitored for unusual patterns"

performance_contracts:
  airport_search: "<1000ms response time for 95th percentile"
  distance_calculation: "<500ms with caching, <2000ms without cache"
  geographical_queries: "<800ms with spatial indexing"
  fee_retrieval: "<200ms for cached data, <500ms for database queries"
  slot_availability: "<300ms with operational context"
  database_operations: "<200ms for single airport queries"
  batch_operations: "<2000ms for up to 100 airports"
  cache_hit_ratio: ">90% for distance calculations"
  search_accuracy: ">95% relevance for code-based searches"
  data_freshness: "<24 hours for enrichment updates"

monitoring:
  metrics:
    - "Airport search query volume, performance, and success rates"
    - "Distance calculation cache hit rates and performance"
    - "Geographical query response times and spatial index efficiency"
    - "Fee retrieval frequency, patterns, and cache performance"
    - "Slot availability check volume and operational accuracy"
    - "Data enrichment success rates and source reliability"
    - "API endpoint usage, error rates, and rate limiting effectiveness"
    - "Database query performance and spatial index utilization"
  
  alerts:
    - "Airport search response time > 1500ms"
    - "Distance calculation cache miss rate > 20%"
    - "Geographical query errors > 5%"
    - "Airport data enrichment failures > 10%"
    - "Invalid airport code submissions > 50/hour"
    - "Fee calculation errors or inconsistencies detected"
    - "Spatial index performance degradation"
    - "Rate limiting threshold breaches"
  
  dashboards:
    - "Airport domain performance overview with key metrics"
    - "Search and discovery usage patterns and optimization opportunities"
    - "Distance calculation efficiency and cache performance metrics"
    - "Data quality and enrichment status with source reliability"
    - "Geographical coverage, accuracy, and spatial query performance"
    - "Fee management and pricing trends analysis"
    - "API usage patterns and security monitoring" 