system: authentication
description: "Villiers.ai Comprehensive Authentication System - World-Class Email-Based Passwordless Authentication with BIP39 Mnemonic Restoration Keys and Bulletproof Security. This system owns all user identity, authentication, registration, session management, and account recovery logic across the entire platform."

intent_assertions:
- "Email-based passwordless authentication as the primary and only production method"
- "Zero-friction onboarding with email verification codes - no passwords required"
- "Client-side generated BIP39 mnemonic restoration keys for email account recovery"
- "Complete account recovery capability when users lose email access"
- "Seamless authentication state persistence across browser sessions and page reloads"
- "Zero authentication failures - comprehensive error handling and retry logic"
- "JWT token management with automatic refresh, rotation, and secure revocation"
- "Anonymous sessions for unauthenticated users with seamless upgrade to authenticated state"
- "All authentication flows must be tested with <1% failure rate end-to-end"
- "Restoration keys generated client-side and never transmitted to server"

technical_assertions:
- path: app/services/auth_service.py
  type: file
  description: "Primary authentication orchestration service with email-based passwordless flow"
- path: app/services/email_service.py
  type: file
  description: "Email delivery service for verification codes and account notifications"
- path: app/nostr/recovery.py
  type: file
  description: "BIP39 mnemonic-based restoration key generation and recovery (client-side only)"
- path: app/core/security.py
  type: file
  description: "JWT token creation, verification, and cryptographic operations"
- path: app/api/v1/endpoints/auth/auth.py
  type: file
  description: "Authentication API endpoints with comprehensive error handling"
- path: app/db/models/auth.py
  type: file
  description: "Authentication database models with optimized relationships"
- path: app/db/manager/repositories/revoked_token_repository.py
  type: file
  description: "Token blacklist management with automated cleanup methods"
- path: app/api/deps.py
  type: file
  description: "Authentication dependencies and middleware"

implementation_status:
  fully_implemented:
  - "Email-based passwordless authentication flow"
  - "User registration with profile creation"
  - "JWT token lifecycle management with revocation"
  - "Token refresh with automatic rotation"
  - "Comprehensive audit logging and activity tracking"
  - "Database models with proper indexing and relationships"
  - "Error handling with centralized exception management"
  
  partially_implemented:
  - "BIP39 restoration key generation (backend utilities exist, frontend integration needed)"
  - "Account recovery using restoration keys (design complete, implementation needed)"
  
  planned_future_enhancements:
  - "Nostr protocol authentication (moved to future roadmap)"
  - "Challenge-response authentication (Nostr-based, future enhancement)"
  - "Client-side cryptographic key management for Nostr (future)"

authentication_architecture:
  primary_flow: "email_based_passwordless"
  restoration_method: "bip39_mnemonic_keys"
  description: "Users authenticate with email verification codes and receive client-generated restoration keys for account recovery"
  
  core_principles:
  - "Email as primary identity anchor"
  - "Client-side restoration key generation using BIP39 standard"
  - "Server never stores or receives restoration keys"
  - "Restoration keys only used for email account recovery scenarios"
  - "Zero-knowledge recovery - server cannot access user's restoration capabilities"

current_authentication_flow:
  primary_method: "email_based_passwordless"
  description: "Users register with email, receive verification codes, and authenticate with JWT tokens"
  steps:
  - "User provides email and profile information"
  - "System generates login code and sends via email"
  - "User enters verification code"
  - "System validates code and issues JWT tokens"
  - "Access token used for authenticated requests"
  - "Refresh token used for token renewal"
  - "Logout revokes tokens via blacklist"
  
  restoration_key_flow:
  - "During registration, client generates BIP39 mnemonic restoration key"
  - "Restoration key stored locally and optionally backed up by user"
  - "If email access lost, user can initiate recovery with restoration key"
  - "System validates restoration key against stored hash/identifier"
  - "User can update email address using restoration key authentication"
  - "New email verification required to complete recovery"
  
  strengths:
  - "Zero-friction onboarding - no passwords required"
  - "Works on all devices and browsers"
  - "Comprehensive token lifecycle management"
  - "Robust error handling and audit logging"
  - "Secure token revocation system"
  - "Client-side restoration key generation ensures user sovereignty"
  - "Account recovery possible even with complete email loss"
  
  limitations:
  - "Requires email access for regular authentication"
  - "Restoration key management responsibility lies with user"
  - "No offline authentication capability"

database_models:
- model: "NostrUser"
  purpose: "Primary user entity with email authentication and optional restoration key identifier"
  fields: ["id", "email", "display_name", "is_active", "is_superuser", "user_metadata", "airtable_id", "restoration_key_hash"]
  indexes: ["email", "airtable_id", "restoration_key_hash"]
  relationships: ["bookings", "quotes", "activities", "notifications", "payments"]

- model: "LoginCode"
  purpose: "One-time codes for passwordless authentication"
  fields: ["email", "code", "expires_at", "used", "used_at"]
  indexes: ["email", "code"]
  cleanup: "expired_codes_hourly"

- model: "UserProfile"
  purpose: "Extended user profile information"
  fields: ["user_id", "full_name", "first_name", "last_name", "phone", "company"]
  relationships: ["user"]

- model: "RevokedToken"
  purpose: "JWT token blacklist for logout and security"
  fields: ["jti", "user_id", "token_type", "revoked_at", "expires_at", "reason"]
  indexes: ["jti", "user_id_token_type", "expires_at"]
  cleanup: "expired_tokens_daily"

services:
- service: "AuthService"
  path: "app/services/auth_service.py"
  purpose: "Primary authentication orchestration service"
  methods: ["register_user", "create_login_request", "verify_login_code", "refresh_token", "logout"]
  dependencies: ["db_manager", "email_service"]

- service: "EmailService"
  path: "app/services/email_service.py"
  purpose: "Email delivery service for verification codes and notifications"
  methods: ["send_verification_code", "send_login_alert", "send_recovery_notification"]
  dependencies: ["email_provider", "template_engine"]

- service: "RestorationKeyService"
  path: "app/nostr/recovery.py"
  purpose: "BIP39 mnemonic-based restoration key generation and validation (client-side utilities)"
  methods: ["generate_recovery_phrase", "validate_restoration_key", "derive_key_identifier"]
  security: "client_side_generation_only"

repositories:
- repository: "NostrUserRepository"
  purpose: "User data access and management"
  methods: ["get_user_by_email", "get_user_by_restoration_key_hash", "create_user", "update_user"]
  validation: "email_format_restoration_key_format"

- repository: "RevokedTokenRepository"
  path: "app/db/manager/repositories/revoked_token_repository.py"
  purpose: "Token blacklist management and cleanup"
  methods: ["create_revoked_token", "is_token_revoked", "cleanup_expired_tokens", "get_user_revoked_tokens"]
  maintenance: "automated_cleanup"

schemas:
- schema: "RegisterRequest"
  purpose: "User registration input validation"
  fields: ["email", "first_name", "last_name", "phone"]
  validation: ["email_format", "name_sanitization", "phone_format"]

- schema: "RegisterResponse"
  purpose: "Registration response with tokens and status"
  fields: ["user_id", "access_token", "refresh_token", "account_status", "requires_verification"]

- schema: "VerifyRequest"
  purpose: "Login code verification input"
  fields: ["login_request_id", "verification_code"]
  validation: ["uuid_format", "code_format"]

- schema: "TokenResponse"
  purpose: "Token refresh response"
  fields: ["access_token", "refresh_token", "token_type", "expires_in"]

- schema: "RevokedTokenCreate"
  purpose: "Token revocation data"
  fields: ["jti", "user_id", "token_type", "revoked_at", "expires_at", "reason"]

security_modules:
- module: "app/core/security.py"
  purpose: "JWT token creation, verification, and management"
  functions: ["create_access_token", "create_refresh_token", "verify_token", "generate_login_code"]

- module: "app/core/auth.py"
  purpose: "Authentication dependencies and user resolution"
  functions: ["get_current_user", "get_current_superuser", "get_optional_current_user", "verify_api_key"]

- module: "app/nostr/keys.py"
  purpose: "Nostr cryptographic key operations"
  functions: ["generate_keypair", "encrypt_nsec", "decrypt_nsec", "verify_signature"]

behavior:
  email_authentication:
    passwordless_flow:
    - "Users register with email and profile information only"
    - "Login codes generated server-side and sent via email"
    - "Verification codes have short expiry times (5-10 minutes)"
    - "Failed verification attempts are rate limited and logged"
    restoration_key_management:
    - "BIP39 mnemonic restoration keys generated client-side during registration"
    - "Restoration key identifiers derived and stored server-side for validation"
    - "Restoration keys never transmitted to server in plain text"
    - "Recovery process validates restoration key without revealing it to server"
    - "Email update requires both restoration key validation and new email verification"
  session_management:
    jwt_tokens:
    - "Issue JWT access tokens valid for 24 hours"
    - "Issue refresh tokens valid for 30 days with secure storage"
    - "Automatic token refresh 5 minutes before expiration"
    - "Secure token storage using httpOnly cookies where possible"
    - "Token revocation on logout with server-side invalidation"
    anonymous_sessions:
    - "Create anonymous session IDs for unauthenticated users"
    - "Allow anonymous users to use platform with limited functionality"
    - "Seamless upgrade from anonymous to authenticated session"
    - "Preserve anonymous session data during authentication upgrade"
  authentication_endpoints:
    login_flows:
    - path: "/api/v1/auth/request-login"
      method: "POST"
      description: "Email-based login request with verification code generation"
      response_time: "<100ms"
    - path: "/api/v1/auth/register"
      method: "POST"
      description: "Email-based registration with profile creation"
      response_time: "<100ms"
    verification_endpoints:
    - path: "/api/v1/auth/verify"
      method: "POST"
      description: "Login code verification and JWT token issuance"
      response_time: "<50ms"
    recovery_endpoints:
    - path: "/api/v1/auth/recovery/initiate"
      method: "POST"
      description: "Initiate account recovery with restoration key"
      response_time: "<100ms"
    - path: "/api/v1/auth/recovery/complete"
      method: "POST"
      description: "Complete account recovery and email update"
      response_time: "<100ms"
    session_endpoints:
    - path: "/api/v1/auth/logout"
      method: "POST"
      description: "Session termination with token revocation"
    - path: "/api/v1/auth/refresh-token"
      method: "POST"
      description: "Token refresh with automatic rotation"

invariants:
- "Restoration keys (BIP39 mnemonics) must never be transmitted to server in plain text"
- "Server must only store restoration key identifiers/hashes, never the keys themselves"
- "All JWT tokens must include unique JTI for revocation capability"
- "Email verification codes must have short expiry times and be single-use"
- "Token revocation must be checked on every authenticated request"
- "Account recovery must verify restoration key against stored identifier"
- "User sessions must maintain audit trail for security monitoring"
- "Rate limiting must be enforced on all authentication endpoints"
- "Email delivery failures must be logged and retried with exponential backoff"
- "Restoration key generation must use cryptographically secure random sources"

forbidden_states:
- "Restoration keys transmitted in plain text to server"
- "BIP39 mnemonic phrases stored on server in any form"
- "JWT tokens without JTI for revocation tracking"
- "Email verification codes with unlimited attempts or no expiry"
- "Tokens accepted without revocation status checking"
- "Account recovery without restoration key validation"
- "User authentication without rate limiting protection"
- "Tokens with predictable or non-unique identifiers"
- "Authentication events without audit logging"
- "Email delivery without proper error handling and retry logic"

depends_on:
- system: "database"
  purpose: "User data storage and token blacklist management"
  components: ["postgresql", "async_sessions", "repositories"]

- system: "communication"
  purpose: "Email delivery for login codes and recovery"
  components: ["email_service", "template_engine"]

- system: "analytics"
  purpose: "Authentication activity tracking and monitoring"
  components: ["user_activity", "security_events"]

enforcement_hooks:
- hook: "authentication_validator"
  frequency: "every_request"
  validates: ["token_validity", "revocation_status", "user_active"]

- hook: "token_cleanup_job"
  frequency: "daily"
  validates: ["expired_tokens", "cleanup_completion"]

- hook: "security_monitor"
  frequency: "real_time"
  validates: ["failed_attempts", "rate_limits", "suspicious_activity"]

- hook: "challenge_expiry_cleanup"
  frequency: "hourly"
  validates: ["expired_challenges", "used_challenges"]

error_handling:
authentication_errors:
- "Invalid credentials return generic error messages"
- "Token expiry handled with refresh flow"
- "Revoked tokens rejected with clear error"
- "Rate limit exceeded returns 429 status"
- "Service unavailable returns 503 status"

recovery_errors:
- "Invalid recovery tokens expire after 1 hour"
- "Mismatch mnemonic verification fails securely"
- "Recovery rate limiting prevents abuse"
- "Email delivery failures logged and retried"

validation_errors:
- "Input sanitization prevents injection attacks"
- "Email format validation with proper regex"
- "JWT format validation with signature verification"
- "Challenge format validation prevents manipulation"

scheduler_integration:
cleanup_tasks:
- task: "cleanup_expired_tokens"
  frequency: "daily_at_02:00"
  purpose: "Remove expired revoked tokens from blacklist"
  batch_size: 1000
  
- task: "cleanup_expired_challenges"
  frequency: "hourly"
  purpose: "Remove expired authentication challenges"
  retention: "24_hours"

- task: "cleanup_expired_login_codes"
  frequency: "hourly"
  purpose: "Remove expired passwordless login codes"
  retention: "1_hour"

monitoring_tasks:
- task: "security_metrics_collection"
  frequency: "every_15_minutes"
  purpose: "Collect authentication security metrics"
  metrics: ["failed_attempts", "token_usage", "recovery_requests"]

- task: "audit_log_aggregation"
  frequency: "daily"
  purpose: "Aggregate authentication audit events"
  retention: "90_days"

files:
- app/api/v1/endpoints/auth/auth.py
- app/services/auth_service.py
- app/services/nostr_auth_service.py
- app/services/legacy_services/auth.py
- app/nostr/auth.py
- app/nostr/keys.py
- app/nostr/recovery.py
- app/core/auth.py
- app/core/security.py
- app/db/models/auth.py
- app/schemas/auth.py
- app/db/schemas/auth.py
- app/db/manager/repositories/revoked_token_repository.py
- app/api/deps.py
- docs/authentication/AUTHENTICATION_WORKFLOW.md
- docs/authentication/WEB_BROWSER_AUTH.md
- docs/authentication/SECURE_LOGIN_ANALYSIS.md
- docs/authentication/secure_login_challenge.py

integrations:
  ui_consolidation:
    moved_from_ui_yaml:
    - "Nostr authentication flow requirements and UX specifications"
    - "Authentication state persistence and management logic"
    - "Login/logout user experience and visual requirements"
    - "Authentication-related API endpoint declarations"
    ownership: "authentication.yaml now owns all identity and authentication UI logic"
  api_consolidation:
    authentication_endpoints:
    - path: "/api/v1/auth/login"
      method: "POST"
      description: "Nostr-based authentication with signature verification"
    - path: "/api/v1/auth/register"
      method: "POST"
      description: "Nostr-based user registration and identity creation"
    - path: "/api/v1/auth/verify"
      method: "POST"
      description: "JWT token verification and user identification"
    - path: "/api/v1/auth/logout"
      method: "POST"
      description: "Session termination with token revocation"
    - path: "/api/v1/auth/refresh-token"
      method: "POST"
      description: "Token refresh with automatic rotation"

monitoring:
  authentication_metrics:
    login_success_rate:
      frequency: "every 5 minutes"
      threshold: ">95% success rate for login attempts"
      escalation: "Alert if success rate drops below threshold"
    session_health:
      frequency: "every 1 minute"
      metrics:
      - "Active authenticated sessions count"
      - "Anonymous session creation rate"
      - "Session upgrade success rate"
      - "Token refresh success rate"
  security_monitoring:
    authentication_failures:
      tracking:
      - "Failed login attempts by IP and frequency"
      - "Invalid token usage attempts"
      - "Suspicious authentication patterns"
      - "Potential brute force attack detection"

testing:
  end_to_end_validation:
    authentication_flows:
    - "Complete registration flow with keypair generation and backup"
    - "Login flow with existing Nostr credentials"
    - "Browser extension authentication and integration"
    - "Anonymous session creation and upgrade to authenticated state"
    - "Session persistence across browser restart and page refresh"
    - "Logout flow with complete session termination"
  visual_regression_testing:
    authentication_components:
    - "Login modal rendering and responsiveness"
    - "Registration flow visual consistency"
    - "Error message display and formatting"
    - "Loading states and progress indicators"
    - "Mobile responsiveness for all authentication screens"
  automated_testing:
    frequency: "every 15 minutes"
    coverage_requirement: ">98% for all authentication-critical code paths"
    performance_targets:
    - "Authentication flow completion <30 seconds"
    - "Login modal render <100ms"
    - "JWT token verification <50ms"
    - "Session state update <10ms"

security:
  cryptographic_standards:
  - "secp256k1 elliptic curve cryptography for all Nostr operations"
  - "Cryptographically secure random number generation (crypto.getRandomValues)"
  - "Proper signature verification using established Nostr libraries"
  - "Protection against timing attacks in signature verification"
  data_protection:
  - "Zero server-side private key storage or transmission"
  - "Minimal data collection - only public keys stored for identity"
  - "Secure session token storage with appropriate security flags"
  - "Protection against common web vulnerabilities (XSS, CSRF, etc.)"

performance:
  response_times:
    authentication: "<100ms for all auth endpoints"
    token_verification: "<50ms for JWT validation"
    session_operations: "<100ms for session management"
    ui_components: "<100ms for component rendering"
  availability:
    uptime_target: "99.99%"
    downtime_budget: "4.32 minutes per month"
    recovery_time: "<30 seconds for authentication service failures"

consolidation_notes:
  authentication_ownership: "All authentication logic consolidated into authentication.yaml"
  ui_integration: "Authentication UI components and flows managed by authentication.yaml"
  api_endpoints: "Authentication API endpoints removed from ui.yaml and api.yaml - owned by authentication.yaml"
  responsibility: "Authentication system focuses on identity, security, and user sovereignty"

provides:
- "Nostr-based identity management"
- "JWT authentication services"
- "User registration and onboarding"
- "Session management and persistence"
- "Zero-confusion authentication UX"

implementation_gaps:
  restoration_key_integration:
    status: "design_complete_implementation_needed"
    description: "BIP39 restoration key system designed but not integrated"
    current_state:
    - "BIP39 mnemonic generation utilities exist in app/nostr/recovery.py"
    - "Restoration key concept documented in authentication workflow"
    - "Database schema can support restoration key identifiers"
    missing_components:
    - "Frontend restoration key generation during registration"
    - "API endpoints for restoration key-based recovery"
    - "Restoration key validation and email update flow"
    - "User interface for restoration key backup and recovery"
    implementation_priority: "high"
    estimated_effort: "1-2 weeks"
    
  frontend_authentication_components:
    status: "not_implemented"
    description: "No frontend authentication components implemented"
    current_state:
    - "Backend API endpoints fully functional"
    - "Comprehensive error handling and validation"
    missing_components:
    - "React/TypeScript authentication components"
    - "Authentication state management (Redux/Context)"
    - "Login/registration UI components with restoration key generation"
    - "Token management and refresh logic"
    - "Restoration key backup and recovery UI"
    implementation_priority: "high"
    estimated_effort: "1-2 weeks"
    
  scheduler_integration:
    status: "defined_not_integrated"
    description: "Cleanup methods exist but not scheduled for execution"
    current_state:
    - "RevokedTokenRepository.cleanup_expired_tokens() implemented"
    - "Cleanup task definitions in system specification"
    - "SchedulerService exists with system job registration"
    missing_components:
    - "Registration of authentication cleanup jobs in SchedulerService"
    - "Login code cleanup job implementation and registration"
    implementation_priority: "medium"
    estimated_effort: "1-2 days"

restoration_key_system:
  purpose: "Provide account recovery when users lose email access"
  technology: "BIP39 mnemonic phrases (12-24 words)"
  generation: "Client-side using cryptographically secure random"
  storage: "User responsibility - local backup, secure storage"
  server_knowledge: "Only stores hash/identifier for validation, never the key itself"
  
  use_cases:
  - "Email account compromised or lost"
  - "Email provider service discontinued"
  - "User wants to change primary email address"
  - "Account locked due to email delivery issues"
  
  security_model:
  - "Restoration key generated client-side using crypto.getRandomValues()"
  - "Server stores only a hash or public identifier derived from restoration key"
  - "Restoration key never transmitted to server in plain text"
  - "Recovery process validates restoration key without revealing it to server"
  - "User must verify new email address after restoration key recovery"
  
  implementation_approach:
  - "Generate 12-word BIP39 mnemonic during registration"
  - "Derive identifier from mnemonic for server storage"
  - "Provide secure backup options (download, print, copy)"
  - "Recovery flow validates mnemonic and allows email update"
  - "Require new email verification to complete recovery"

future_enhancements:
  nostr_protocol_integration:
    status: "future_roadmap"
    description: "Advanced cryptographic authentication for sovereignty-focused users"
    timeline: "Phase 2 - after core email authentication is perfected"
    features:
    - "Challenge-response authentication with Nostr signatures"
    - "Client-side keypair generation and management"
    - "Integration with Nostr browser extensions"
    - "Offline authentication capabilities"
    - "Advanced multi-device key synchronization"
    
  advanced_recovery_options:
    status: "future_enhancement"
    description: "Additional recovery mechanisms beyond email and restoration keys"
    features:
    - "Multi-signature recovery with trusted contacts"
    - "Time-locked recovery mechanisms"
    - "Hardware security key integration"
    - "Biometric authentication options" 