system: villiers_empty_legs
description: "Villiers.ai Empty Legs Subdomain - World-Class Aircraft Repositioning Flight Opportunities with Automated Discovery, Dynamic Pricing, and Marketplace Integration. This subdomain owns all empty leg identification, publication, booking conversion, and revenue optimization workflows for discounted private jet charter opportunities."

intent_assertions:
- "Complete empty legs lifecycle management from aircraft repositioning identification to booking conversion"
- "Automated empty leg discovery from aircraft movements and scheduling gaps"
- "Dynamic pricing optimization with 40-60% discounts from regular charter rates"
- "Seamless empty leg to shared flight conversion for revenue maximization"
- "Intelligent empty leg matching with customer route preferences and timing flexibility"
- "Operator solicitation automation for proactive empty leg inventory management"
- "Real-time empty leg availability tracking with expiration management"
- "Integration with booking domain for seamless conversion to confirmed flights"

technical_assertions:
- path: "app/db/models/empty_leg.py"
  purpose: "Complete empty leg data models with positioning and pricing information"
  lines: 177
  models: ["EmptyLeg", "EmptyLegRequest", "EmptyLegSubscription"]
  
- path: "app/services/positioning_service.py"
  purpose: "Core empty leg discovery and matching service with aircraft positioning logic"
  lines: 500
  methods: ["find_empty_legs", "list_empty_legs", "create_empty_leg_from_movement"]
  
- path: "app/api/v1/endpoints/bookings/empty_legs.py"
  purpose: "Empty legs API endpoints with booking optimization and sharing workflows"
  lines: 1275
  endpoints: ["/empty-legs/{booking_id}/optimize", "/empty-legs/{booking_id}/request-sharing"]
  
- path: "app/services/empty_leg_processor_service.py"
  purpose: "Automated empty leg processing with profitability optimization"
  lines: 200
  methods: ["process_pending_bookings", "optimize_booking_for_confirmation"]
  
- path: "app/db/manager/repositories/empty_leg_repository.py"
  purpose: "Empty legs data access layer with route matching and availability queries"
  lines: 900
  operations: ["find_empty_legs", "get_empty_legs_for_route", "find_bitcoin_accepting_operator"]
  
- path: "app/services/legacy_services/empty_leg_processor.py"
  purpose: "Legacy empty leg processing with Bitcoin settlement optimization"
  lines: 906
  methods: ["process_pending_bookings", "_create_shared_flight_for_booking"]
  
- path: "app/services/empty_leg_solicitation_service.py"
  purpose: "Operator solicitation service for proactive empty leg inventory management"
  lines: 100
  methods: ["start_weekly_solicitation", "track_operator_responses"]
  
- path: "app/api/v1/endpoints/aircraft/positioning.py"
  purpose: "Public empty legs discovery API for marketing and customer access"
  lines: 160
  endpoints: ["/aircraft/positioning/empty-legs"]

database_models:
- model: "EmptyLeg"
  purpose: "Primary empty leg entity with comprehensive positioning and pricing data"
  fields: [
    "id", "operator_id", "aircraft_id", "movement_id", "from_airport_id", "to_airport_id",
    "departure_time", "arrival_time", "price", "currency", "original_price", "available_seats",
    "is_flexible", "date_flexibility_days", "status", "source", "visibility", "expires_at",
    "booking_id", "notes", "emptyleg_metadata", "departure_distance_nm", "arrival_distance_nm",
    "time_difference_hours", "flight_time_hours", "distance_nm"
  ]
  indexes: ["operator_id", "aircraft_id", "from_airport_id", "to_airport_id", "departure_time", "status"]
  relationships: ["operator", "aircraft", "booking", "quotes", "shared_flight", "movement", "from_airport", "to_airport"]

- model: "EmptyLegRequest"
  purpose: "Operator solicitation tracking for proactive empty leg inventory management"
  fields: [
    "id", "operator_id", "request_date", "response_date", "has_responded", "empty_legs_count",
    "email_sent", "email_sent_date", "email_opened", "email_opened_date", "notes", "request_metadata"
  ]
  tracking: "Email delivery and operator response monitoring"
  
- model: "EmptyLegSubscription"
  purpose: "User subscription management for empty leg notifications and alerts"
  fields: [
    "id", "user_id", "from_airport_id", "to_airport_id", "preferred_aircraft_categories",
    "min_seats", "date_range_start", "date_range_end", "price_max", "notify_by_email",
    "notify_by_sms", "notify_by_app", "is_active", "last_notified"
  ]
  notifications: "Multi-channel notification preferences and delivery tracking"

services:
- service: "PositioningService"
  path: "app/services/positioning_service.py"
  purpose: "Core empty leg discovery and aircraft repositioning logic"
  methods: ["find_empty_legs", "list_empty_legs", "create_empty_leg_from_movement"]
  integration: "Aircraft movement tracking and empty leg opportunity identification"
  
- service: "EmptyLegProcessorService"
  path: "app/services/empty_leg_processor_service.py"
  purpose: "Automated empty leg processing with booking optimization"
  methods: ["process_pending_bookings", "optimize_booking_for_confirmation", "process_sharing_requests"]
  automation: "Scheduler-driven empty leg processing and profitability optimization"
  
- service: "EmptyLegSolicitationService"
  path: "app/services/empty_leg_solicitation_service.py"
  purpose: "Operator solicitation automation for proactive inventory management"
  methods: ["start_weekly_solicitation", "track_operator_responses", "process_operator_opt_status"]
  outreach: "Automated operator communication and response tracking"

repositories:
- repository: "EmptyLegRepository"
  purpose: "Empty legs data access with sophisticated route matching and availability queries"
  methods: [
    "find_empty_legs", "get_empty_legs_for_route", "find_bitcoin_accepting_operator",
    "get_empty_legs_for_aircraft", "find_matching_empty_legs_by_route"
  ]
  queries: "Geographic proximity matching, time window filtering, aircraft category matching"

behavior:
  empty_leg_discovery:
    aircraft_movement_tracking:
      - "Monitor aircraft movements and positioning schedules for repositioning opportunities"
      - "Identify gaps between charter bookings that require aircraft repositioning"
      - "Calculate optimal routes and timing for repositioning flights"
      - "Generate empty leg opportunities from scheduled aircraft movements"
    
    operator_solicitation:
      - "Weekly automated solicitation emails to operators requesting empty leg inventory"
      - "Track operator response rates and empty leg submission patterns"
      - "Manage operator opt-in/opt-out preferences for solicitation campaigns"
      - "Monitor email delivery, open rates, and response tracking metrics"
    
    dynamic_pricing:
      - "Calculate empty leg pricing at 40-60% discount from regular charter rates"
      - "Apply dynamic pricing based on route demand, timing, and market conditions"
      - "Optimize pricing for maximum revenue while maintaining competitive advantage"
      - "Factor in aircraft positioning costs and operator profit margins"

  empty_leg_marketplace:
    availability_management:
      - "Real-time empty leg availability tracking with automatic expiration handling"
      - "Geographic proximity matching for flexible departure/arrival locations"
      - "Time window flexibility management with date range preferences"
      - "Aircraft category matching based on passenger count and route requirements"
    
    booking_conversion:
      - "Seamless empty leg to booking conversion with quote generation"
      - "Automated booking creation from empty leg selection and payment processing"
      - "Integration with booking lifecycle for confirmed flight management"
      - "Empty leg status updates upon booking to prevent double-booking"
    
    shared_flight_integration:
      - "Empty leg to shared flight conversion for revenue optimization"
      - "Individual seat booking creation from empty leg opportunities"
      - "Seat availability management with real-time updates and confirmations"
      - "Commission calculation and revenue sharing for seat sales"

  automated_processing:
    scheduler_integration:
      - "Background processing every 15 minutes for pending booking optimization"
      - "Empty leg matching and booking confirmation automation"
      - "Shared flight creation from empty leg opportunities"
      - "Bitcoin settlement incentive processing for operator payments"
    
    profitability_optimization:
      - "Multi-strategy booking optimization including empty leg matching"
      - "Alternative aircraft identification for cost-effective empty leg options"
      - "Route optimization and fuel efficiency calculations"
      - "Operator negotiation automation for better empty leg rates"

  subscription_management:
    user_notifications:
      - "Multi-channel notification system (email, SMS, app) for empty leg alerts"
      - "Personalized empty leg matching based on user route preferences"
      - "Flexible date range and pricing threshold management"
      - "Notification frequency control and delivery optimization"
    
    preference_management:
      - "Aircraft category preferences with seat count requirements"
      - "Geographic flexibility settings for departure/arrival airports"
      - "Price threshold management with automatic alert triggers"
      - "Notification channel preferences and delivery timing"

primary_flows:
  empty_leg_discovery_flow:
    steps:
      - "Aircraft Movement Monitoring: Track scheduled flights and positioning requirements"
      - "Opportunity Identification: Identify repositioning flights and scheduling gaps"
      - "Empty Leg Creation: Generate empty leg records with pricing and availability"
      - "Marketplace Publication: Publish empty legs to customer-facing marketplace"
      - "Availability Management: Monitor expiration and booking status updates"
    
    automation:
      - "Continuous aircraft movement monitoring with real-time opportunity detection"
      - "Automated empty leg creation from positioning requirements"
      - "Dynamic pricing updates based on market conditions and demand"

  booking_conversion_flow:
    steps:
      - "Empty Leg Selection: Customer selects empty leg from marketplace"
      - "Quote Generation: Convert empty leg to bookable quote with pricing"
      - "Payment Processing: Process payment with empty leg discount pricing"
      - "Booking Creation: Create confirmed booking from empty leg conversion"
      - "Status Updates: Update empty leg status to prevent double-booking"
    
    integration:
      - "Seamless integration with booking domain for quote and payment processing"
      - "Real-time availability validation to prevent booking conflicts"
      - "Automatic empty leg status updates upon successful booking"

  operator_solicitation_flow:
    steps:
      - "Weekly Solicitation: Send automated emails to operators requesting empty legs"
      - "Response Tracking: Monitor operator responses and empty leg submissions"
      - "Inventory Processing: Process submitted empty legs and validate data"
      - "Marketplace Integration: Publish validated empty legs to customer marketplace"
      - "Performance Analytics: Track operator response rates and inventory quality"
    
    automation:
      - "Automated email campaigns with personalized operator messaging"
      - "Response tracking with email open rates and submission monitoring"
      - "Opt-in/opt-out management for operator communication preferences"

invariants:
- "Empty leg pricing must be 40-60% below regular charter rates for the same route"
- "Empty leg availability must be validated in real-time to prevent double-booking"
- "Aircraft positioning requirements must be verified before empty leg creation"
- "Empty leg expiration must be enforced to maintain marketplace accuracy"
- "Operator solicitation must respect opt-out preferences and communication limits"
- "Empty leg to booking conversion must update status atomically"
- "Shared flight creation from empty legs must maintain seat availability constraints"
- "Geographic proximity matching must use accurate distance calculations"
- "Time window flexibility must respect aircraft scheduling constraints"
- "Subscription notifications must honor user preferences and delivery settings"

forbidden_states:
- "Empty legs published without verified aircraft availability"
- "Double-booking of empty legs without status synchronization"
- "Empty leg pricing above regular charter rates"
- "Expired empty legs remaining in active marketplace"
- "Operator solicitation without opt-in consent"
- "Empty leg conversion without payment validation"
- "Shared flight creation exceeding aircraft capacity"
- "Subscription notifications without user consent"
- "Empty leg data without proper operator attribution"
- "Geographic matching without distance validation"

depends_on:
- "aircraft - Aircraft positioning data and movement tracking"
- "booking - Booking conversion and payment processing integration"
- "operator - Operator management and communication preferences"
- "airport - Airport data for route matching and geographic calculations"
- "communication - Email and notification services for operator solicitation"

provides:
- "empty_leg_marketplace - Complete empty leg discovery and booking platform"
- "aircraft_positioning_optimization - Repositioning flight opportunity identification"
- "discounted_charter_access - 40-60% discounted private jet charter opportunities"
- "operator_inventory_management - Proactive empty leg solicitation and tracking"
- "shared_flight_creation - Empty leg to shared flight conversion capabilities"
- "subscription_notification_system - Personalized empty leg alert and matching service"

enforcement_hooks:
- "validate_empty_leg_pricing_discounts"
- "ensure_aircraft_availability_verification"
- "confirm_operator_solicitation_consent"
- "verify_booking_conversion_atomicity"
- "validate_geographic_proximity_calculations"

scheduler_integration:
  empty_leg_automation:
    - task: "Empty Leg Processing"
      schedule: "every 15 minutes"
      purpose: "Process pending bookings and optimize with empty leg matching"
      handler: "empty_leg_processor_service.py:process_pending_bookings"
      optimization: "Multi-strategy booking optimization including empty leg alternatives"
    
    - task: "Sharing Request Processing"
      schedule: "every 15 minutes"
      purpose: "Convert empty legs to shared flights for revenue optimization"
      handler: "empty_leg_processor_service.py:process_sharing_requests_from_clients"
      conversion: "Empty leg to shared flight conversion with seat availability management"
    
    - task: "Bitcoin Settlement Incentives"
      schedule: "daily 3:00 AM"
      purpose: "Process Bitcoin payment incentives for operators with empty legs"
      handler: "empty_leg_processor_service.py:process_bitcoin_settlement_incentives"
      payments: "Operator payment optimization with Bitcoin settlement bonuses"
    
    - task: "Weekly Operator Solicitation"
      schedule: "weekly Monday 9:00 AM"
      purpose: "Send automated empty leg solicitation emails to operators"
      handler: "empty_leg_solicitation_service.py:start_weekly_solicitation"
      outreach: "Proactive operator communication for empty leg inventory management"

endpoints:
  empty_leg_marketplace:
    - path: "/api/v1/aircraft/positioning/empty-legs"
      methods: ["GET"]
      description: "Public empty leg search and discovery"
      response_time: "<500ms"
      handler: "positioning.py:find_empty_legs"
      access: "Public access for marketing and customer discovery"
    
    - path: "/api/v1/bookings/empty-legs"
      methods: ["GET"]
      description: "List available empty legs with filtering"
      response_time: "<500ms"
      handler: "tracking.py:list_empty_legs"
      access: "User access with optional authentication"

  booking_optimization:
    - path: "/api/v1/empty-legs/{booking_id}/optimize"
      methods: ["POST"]
      description: "Optimize booking with empty leg alternatives and strategies"
      response_time: "<3000ms"
      handler: "empty_legs.py:optimize_booking_for_confirmation"
      access: "Admin-only for booking optimization workflows"
    
    - path: "/api/v1/empty-legs/{booking_id}/request-sharing"
      methods: ["POST"]
      description: "Request seat sharing from confirmed booking"
      response_time: "<1000ms"
      handler: "empty_legs.py:request_seat_sharing"
      access: "User access for own bookings"

  shared_flight_conversion:
    - path: "/api/v1/shared-flights/empty-leg/{empty_leg_id}"
      methods: ["POST"]
      description: "Create shared flight from empty leg opportunity"
      response_time: "<1000ms"
      handler: "flight_sharing.py:create_shared_flight_from_empty_leg"
      access: "Admin and operator access for shared flight creation"

  operator_solicitation:
    - path: "/api/v1/empty-legs/solicitation/start"
      methods: ["POST"]
      description: "Start weekly operator solicitation campaign"
      response_time: "<1000ms"
      handler: "empty_legs.py:start_weekly_solicitation"
      access: "Admin-only for solicitation management"
    
    - path: "/api/v1/empty-legs/solicitation/operators"
      methods: ["GET"]
      description: "Get operator solicitation status and metrics"
      response_time: "<500ms"
      handler: "empty_legs.py:get_solicitation_operators"
      access: "Admin-only for solicitation monitoring"

error_handling:
  empty_leg_errors:
    - error_type: "EmptyLegNotAvailable"
      description: "Empty leg no longer available for booking"
      response_code: 409
      recovery: "Suggest alternative empty legs or regular booking options"
    
    - error_type: "InvalidPricingDiscount"
      description: "Empty leg pricing validation failed"
      response_code: 400
      recovery: "Recalculate pricing with valid discount parameters"
    
    - error_type: "AircraftPositioningConflict"
      description: "Aircraft positioning conflict prevents empty leg creation"
      response_code: 409
      recovery: "Validate aircraft availability and positioning requirements"

monitoring:
  empty_leg_metrics:
    - metric: "empty_leg_conversion_rate"
      description: "Percentage of empty legs converted to bookings"
      target: ">25%"
    
    - metric: "average_empty_leg_discount"
      description: "Average discount percentage for empty leg pricing"
      target: "40-60%"
    
    - metric: "operator_response_rate"
      description: "Percentage of operators responding to solicitation"
      target: ">40%"
    
    - metric: "empty_leg_marketplace_accuracy"
      description: "Percentage of empty legs with accurate availability"
      target: ">95%"

testing:
  empty_leg_testing:
    - test_type: "Unit Tests"
      coverage: "Empty leg discovery, pricing, and conversion logic"
      location: "tests/unit/services/test_empty_leg_processor.py"
    
    - test_type: "Integration Tests"
      coverage: "Complete empty leg workflow from discovery to booking"
      location: "tests/integration/endpoints/bookings/test_empty_legs_endpoints.py"
    
    - test_type: "API Tests"
      coverage: "Empty leg API endpoints and response validation"
      location: "tests/integration/endpoints/bookings/test_empty_legs_endpoints.py"

security:
  empty_leg_security:
    - "Operator solicitation requires explicit opt-in consent"
    - "Empty leg data access controlled by visibility settings (public/private/vip)"
    - "Booking conversion requires proper user authentication and payment validation"
    - "Admin-only access for empty leg optimization and solicitation management"
    - "Subscription management requires user ownership validation"

performance:
  empty_leg_performance_targets:
    - "Empty leg search queries: <500ms response time"
    - "Booking optimization processing: <3000ms per booking"
    - "Shared flight conversion: <1000ms per empty leg"
    - "Operator solicitation processing: <5000ms per campaign"
    - "Real-time availability validation: <100ms per check"

consolidation_notes:
  empty_leg_ownership:
    - "Complete empty leg lifecycle management consolidated in PositioningService"
    - "Booking optimization centralized in EmptyLegProcessorService"
    - "Operator solicitation unified in EmptyLegSolicitationService"
    - "Marketplace access standardized across multiple API endpoints"
    - "Shared flight integration consolidated in FlightSharingService"

implementation_gaps:
  enhanced_matching_algorithms:
    status: "enhancement_opportunity"
    description: "Machine learning-based empty leg matching could improve conversion rates"
    current_state: "Geographic proximity and time window matching"
    enhancement: "AI-powered matching with user behavior and preference learning"
    priority: "medium"
    estimated_effort: "3-4 weeks"
  
  real_time_pricing_optimization:
    status: "enhancement_opportunity"
    description: "Dynamic pricing based on real-time demand and market conditions"
    current_state: "Static discount percentages (40-60%)"
    enhancement: "Real-time pricing optimization with demand-based adjustments"
    priority: "low"
    estimated_effort: "2-3 weeks"

files:
- "app/db/models/empty_leg.py - Complete empty leg data models and relationships"
- "app/services/positioning_service.py - Core empty leg discovery and positioning logic"
- "app/api/v1/endpoints/bookings/empty_legs.py - Empty legs API endpoints and workflows"
- "app/services/empty_leg_processor_service.py - Automated empty leg processing service"
- "app/db/manager/repositories/empty_leg_repository.py - Empty legs data access layer"
- "app/services/legacy_services/empty_leg_processor.py - Legacy empty leg processing"
- "app/services/empty_leg_solicitation_service.py - Operator solicitation automation"
- "app/api/v1/endpoints/aircraft/positioning.py - Public empty legs discovery API"
- "app/db/schemas/empty_leg.py - Empty leg database schemas"
- "app/schemas/empty_legs.py - Empty legs API response schemas"
- "tests/integration/endpoints/bookings/test_empty_legs_endpoints.py - Empty legs integration tests" 