system: villiers_booking_lifecycle
description: "Villiers.ai Booking Lifecycle Management Subdomain - World-Class State-Driven Booking Orchestration with Automated Transitions, Real-Time Status Tracking, and Complete Audit Trail Management. This subdomain owns all booking state management, lifecycle transitions, flight operations tracking, and post-completion processing workflows."

intent_assertions:
- "Complete booking state management with enforced transition rules (SOURCING → PENDING → CONFIRMED → COMPLETED)"
- "Automated booking lifecycle orchestration with intelligent status progression and validation"
- "Real-time flight operations tracking from departure through arrival to completion"
- "Comprehensive audit trail maintenance for all booking state changes and transitions"
- "Automated post-flight processing with operator performance metrics and customer feedback collection"
- "Error-resistant state management with rollback capabilities and transaction safety"
- "Integration with payment processing for status-dependent booking confirmations"
- "Scheduler-driven automated booking processing with profitability optimization"

technical_assertions:
- path: "app/db/models/enums.py"
  purpose: "Booking status enumeration with complete lifecycle state definitions"
  lines: 107
  states: ["SOURCING", "PENDING", "CONFIRMED", "CANCELED", "COMPLETED", "OPERATOR_DECLINED", "OPERATOR_AOG"]
  
- path: "app/services/booking_service.py"
  purpose: "Core booking lifecycle business logic with state transition management"
  lines: 586
  methods: ["confirm_booking", "cancel_booking", "update_booking", "get_booking_by_id"]
  
- path: "app/api/v1/endpoints/bookings/bookings.py"
  purpose: "Booking lifecycle API endpoints with state management operations"
  lines: 1129
  endpoints: ["/bookings/{id}/depart", "/bookings/{id}/arrive", "/bookings/{id}/complete"]
  
- path: "app/db/schemas/booking.py"
  purpose: "Booking lifecycle database schemas with metadata and status tracking"
  lines: 210
  schemas: ["BookingBase", "BookingCreate", "BookingUpdate", "BookingWithRelations"]
  
- path: "app/tasks/booking_processor.py"
  purpose: "Automated booking lifecycle processing with scheduler integration"
  lines: 84
  tasks: ["process_bookings_task", "process_sharing_requests_from_clients"]
  
- path: "app/services/trips_service.py"
  purpose: "Booking-to-trip status mapping with lifecycle state translation"
  lines: 500
  mappings: ["_map_booking_status_to_trip_status", "_map_trip_status_to_booking_status"]
  
- path: "app/db/manager/repositories/booking_repository.py"
  purpose: "Booking lifecycle data access with status-based queries and updates"
  lines: 847
  operations: ["get_pending_bookings_for_processing", "confirm_booking", "update_booking_status"]

database_models:
- model: "Booking"
  purpose: "Primary booking entity with comprehensive lifecycle state management"
  fields: ["id", "status", "payment_status", "booking_metadata", "completed_at", "confirmed_at"]
  status_field: "status (BookingStatusEnum)"
  metadata_field: "booking_metadata (JSONType)"
  audit_fields: ["created_at", "updated_at", "completed_at", "confirmed_at"]
  relationships: ["user", "quote", "operator", "aircraft", "trip"]

- model: "BookingStatusEnum"
  purpose: "Enumeration defining all valid booking lifecycle states"
  values: ["SOURCING", "PENDING", "CONFIRMED", "CANCELED", "COMPLETED", "OPERATOR_DECLINED", "OPERATOR_AOG"]
  transitions: "Enforced workflow progression with validation rules"

services:
- service: "BookingService"
  path: "app/services/booking_service.py"
  purpose: "Core booking lifecycle orchestration with state management"
  methods: ["confirm_booking", "cancel_booking", "update_booking", "get_booking_by_id"]
  state_management: "Enforces valid state transitions with business rule validation"
  
- service: "TripsService"
  path: "app/services/trips_service.py"
  purpose: "Booking lifecycle to trip status mapping and translation"
  methods: ["_map_booking_status_to_trip_status", "_map_trip_status_to_booking_status"]
  integration: "Provides UI-friendly status representation from booking states"

repositories:
- repository: "BookingRepository"
  purpose: "Booking lifecycle data access with status-based operations"
  methods: ["get_pending_bookings_for_processing", "confirm_booking", "update_booking_status"]
  queries: "Status-based filtering and lifecycle state management"

behavior:
  booking_state_transitions:
    sourcing_to_pending:
      - "Initial booking creation starts in SOURCING status"
      - "Quote selection and validation triggers transition to PENDING"
      - "Payment initiation required for PENDING status confirmation"
      - "User quote selection metadata recorded in booking_metadata"
    
    pending_to_confirmed:
      - "Payment completion triggers automatic transition to CONFIRMED"
      - "Platform fee payment validation ensures payment status alignment"
      - "Operator notification sent upon confirmation"
      - "Quote status updated to ACCEPTED, declined quotes marked DECLINED"
      - "Booking metadata updated with confirmation details and timestamps"
    
    confirmed_to_completed:
      - "Flight departure tracking initiated upon confirmation"
      - "Passenger arrival confirmation triggers completion preparation"
      - "Post-flight processing includes operator performance metrics"
      - "Customer feedback collection and rating system activation"
      - "Financial reconciliation and payment finalization"
  
  flight_operations_tracking:
    departure_processing:
      - "Flight departure confirmation with timestamp recording in booking_metadata"
      - "Booking status remains CONFIRMED but flight_status metadata updated to 'in_progress'"
      - "Passenger notification and real-time tracking activation"
      - "Emergency contact notification protocols initiated"
      - "API endpoint: PATCH /bookings/{id}/depart validates CONFIRMED status"
    
    arrival_processing:
      - "Passenger arrival confirmation at destination with location verification"
      - "Arrival timestamp and location recorded in booking_metadata"
      - "Flight_status metadata updated to 'arrived' with arrival_confirmed flag"
      - "Ground transportation coordination notifications triggered"
      - "Return flight reminders for round-trip bookings"
      - "API endpoint: PATCH /bookings/{id}/arrive updates flight tracking"
    
    completion_workflow:
      - "Post-flight processing triggered by POST /bookings/{id}/complete endpoint"
      - "Booking status transition from CONFIRMED to COMPLETED with completed_at timestamp"
      - "Operator performance metrics calculation and database updates"
      - "Customer feedback collection system activation with email notifications"
      - "Financial reconciliation and payment finalization processing"
      - "Marketing follow-up and future booking incentives scheduling"

  automated_lifecycle_processing:
    scheduler_integration:
      - "Booking processor task runs every 15 minutes via app/tasks/booking_processor.py"
      - "Pending bookings processed for profitability and automatic confirmation"
      - "Sharing requests from clients processed for shared flight creation"
      - "Bitcoin settlement incentives processed daily at 3:00 AM"
    
    state_validation:
      - "All state transitions validated against business rules and payment status"
      - "Invalid state transitions blocked with appropriate error responses"
      - "Audit trail maintained for all state changes with user and timestamp tracking"
      - "Rollback capabilities for failed transitions with transaction safety"

  error_handling_and_recovery:
    invalid_transitions:
      - "Departure marking blocked for non-CONFIRMED bookings with 400 error response"
      - "Completion processing blocked without arrival confirmation"
      - "Cancellation blocked for COMPLETED bookings with appropriate error messaging"
    
    transaction_safety:
      - "All state transitions wrapped in database transactions"
      - "Rollback capabilities for failed multi-step operations"
      - "Consistent state maintenance across related entities (quotes, payments)"

primary_flows:
  complete_booking_lifecycle:
    steps:
      - "Booking Creation: User selects quote → Booking created in SOURCING status"
      - "Payment Processing: Payment initiated → Status transitions to PENDING"
      - "Confirmation: Payment completed → Status transitions to CONFIRMED"
      - "Flight Operations: Departure tracking → Flight_status metadata updated"
      - "Arrival Processing: Arrival confirmation → Arrival metadata recorded"
      - "Completion: Post-flight processing → Status transitions to COMPLETED"
    
    state_persistence:
      - "All state changes recorded in booking_metadata with timestamps"
      - "Audit trail maintained for compliance and debugging"
      - "Status history preserved for analytics and reporting"

  cancellation_flow:
    steps:
      - "Cancellation Request: User/operator initiates cancellation"
      - "Validation: Booking status checked for cancellation eligibility"
      - "Metadata Update: Cancellation reason and feedback recorded"
      - "Status Transition: Booking status updated to CANCELED"
      - "Notifications: Operator and user notifications sent"
    
    restrictions:
      - "COMPLETED bookings cannot be cancelled"
      - "Cancellation metadata must include reason and user_id"
      - "Refund processing triggered based on cancellation timing"

invariants:
- "Booking status transitions must follow defined workflow: SOURCING → PENDING → CONFIRMED → COMPLETED"
- "Flight departure tracking can only be activated for CONFIRMED bookings"
- "Passenger arrival confirmation requires prior departure confirmation"
- "Booking completion requires both departure and arrival confirmation"
- "All state transitions must be recorded in booking_metadata with timestamps"
- "Payment status must align with booking status for confirmation transitions"
- "Cancelled bookings cannot transition to any other status"
- "Completed bookings cannot be cancelled or modified"
- "Booking metadata must maintain complete audit trail for all changes"
- "Scheduler processing must respect booking state transition rules"

forbidden_states:
- "Booking confirmation without completed payment processing"
- "Flight departure marking for non-CONFIRMED bookings"
- "Arrival confirmation without prior departure confirmation"
- "Booking completion without both departure and arrival metadata"
- "State transitions without proper audit trail recording"
- "Direct status updates bypassing business rule validation"
- "Booking cancellation after completion"
- "Payment status inconsistency with booking status"
- "Metadata updates without timestamp recording"
- "Scheduler processing overriding manual state management"

depends_on:
- "authentication - User authentication for booking lifecycle operations"
- "payment - Payment processing integration for status-dependent confirmations"
- "communication - Notification services for status change communications"
- "analytics - Booking lifecycle metrics and performance tracking"

provides:
- "booking_state_management - Complete booking lifecycle state orchestration"
- "flight_operations_tracking - Real-time flight status and passenger tracking"
- "automated_lifecycle_processing - Scheduler-driven booking automation"
- "booking_audit_trail - Comprehensive state change tracking and compliance"
- "operator_performance_metrics - Booking-based operator reliability scoring"
- "customer_lifecycle_data - Booking progression analytics and insights"

enforcement_hooks:
- "validate_booking_state_transitions"
- "ensure_payment_status_alignment"
- "confirm_audit_trail_completeness"
- "verify_flight_operations_sequence"
- "validate_scheduler_processing_rules"

scheduler_integration:
  booking_automation:
    - task: "Booking Processing"
      schedule: "every 15 minutes"
      purpose: "Process pending bookings and confirm profitable flights"
      handler: "booking_processor.py:process_bookings_task"
      state_impact: "Transitions PENDING bookings to CONFIRMED based on profitability"
    
    - task: "Sharing Request Processing"
      schedule: "every 15 minutes"
      purpose: "Process client sharing requests and create shared flights"
      handler: "booking_processor.py:process_sharing_requests_from_clients"
      state_impact: "Creates new bookings in SOURCING status for shared flights"
    
    - task: "Bitcoin Settlement Incentives"
      schedule: "daily 3:00 AM"
      purpose: "Process Bitcoin payment incentives for operators"
      handler: "booking_processor.py:process_bitcoin_settlement_incentives"
      state_impact: "No direct booking state changes, payment processing optimization"

error_handling:
  state_transition_errors:
    - error_type: "InvalidStateTransition"
      description: "Booking state transition not allowed by business rules"
      response_code: 400
      recovery: "Return current booking state with allowed transitions"
    
    - error_type: "PaymentStatusMismatch"
      description: "Payment status inconsistent with requested booking state"
      response_code: 400
      recovery: "Validate payment status before allowing state transition"
    
    - error_type: "MissingFlightOperations"
      description: "Flight operations sequence not properly followed"
      response_code: 400
      recovery: "Enforce departure confirmation before arrival processing"

monitoring:
  booking_lifecycle_metrics:
    - metric: "state_transition_success_rate"
      description: "Percentage of successful booking state transitions"
      target: ">99.5%"
    
    - metric: "average_lifecycle_duration"
      description: "Average time from SOURCING to COMPLETED"
      target: "<7 days"
    
    - metric: "automated_processing_efficiency"
      description: "Percentage of bookings processed automatically by scheduler"
      target: ">80%"
    
    - metric: "state_transition_error_rate"
      description: "Rate of failed state transitions"
      target: "<0.5%"

testing:
  state_transition_testing:
    - test_type: "Unit Tests"
      coverage: "All state transition methods and validation rules"
      location: "tests/unit/services/test_booking_service.py"
    
    - test_type: "Integration Tests"
      coverage: "Complete booking lifecycle workflows"
      location: "tests/integration/test_booking_flow.py"
    
    - test_type: "State Machine Tests"
      coverage: "Invalid state transition prevention"
      location: "tests/integration/test_booking_flow.py:test_booking_state_transitions"

security:
  state_management_security:
    - "All state transitions require proper user authentication and authorization"
    - "Booking ownership validation enforced for all lifecycle operations"
    - "Audit trail immutability with tamper-proof timestamp recording"
    - "Payment status validation prevents unauthorized booking confirmations"
    - "Operator notification security prevents unauthorized status updates"

performance:
  lifecycle_performance_targets:
    - "State transition processing: <100ms per operation"
    - "Booking status queries: <50ms response time"
    - "Scheduler processing: <5 minutes per batch"
    - "Audit trail queries: <200ms for full booking history"
    - "Flight operations tracking: <100ms per status update"

consolidation_notes:
  booking_lifecycle_ownership:
    - "Complete booking state management consolidated in BookingService"
    - "Flight operations tracking centralized in booking lifecycle endpoints"
    - "Automated processing unified in booking_processor.py scheduler task"
    - "Audit trail management standardized across all state transitions"
    - "Status mapping consolidated in TripsService for UI consistency"

implementation_gaps:
  enhanced_state_validation:
    status: "enhancement_opportunity"
    description: "State machine validation could be more robust with formal state machine implementation"
    current_state: "Business rule validation in service methods"
    enhancement: "Formal state machine with transition guards and automated validation"
    priority: "medium"
    estimated_effort: "1-2 weeks"
  
  real_time_status_updates:
    status: "enhancement_opportunity"
    description: "Real-time status updates for frontend could be improved with WebSocket integration"
    current_state: "Polling-based status updates via REST API"
    enhancement: "WebSocket-based real-time status broadcasting"
    priority: "low"
    estimated_effort: "2-3 weeks"

files:
- "app/db/models/enums.py - Booking status enumeration definitions"
- "app/services/booking_service.py - Core booking lifecycle business logic"
- "app/api/v1/endpoints/bookings/bookings.py - Booking lifecycle API endpoints"
- "app/db/schemas/booking.py - Booking lifecycle database schemas"
- "app/tasks/booking_processor.py - Automated booking lifecycle processing"
- "app/services/trips_service.py - Booking-to-trip status mapping"
- "app/db/manager/repositories/booking_repository.py - Booking lifecycle data access"
- "tests/integration/test_booking_flow.py - Booking lifecycle integration tests"
- "tests/integration/test_booking_flow_proof.py - Booking workflow proof tests" 