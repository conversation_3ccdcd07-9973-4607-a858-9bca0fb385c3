system: booking
description: "Villiers.ai Comprehensive Booking Domain - World-Class Private Jet Booking Ecosystem with Complete Lifecycle Management, Multi-Leg Trip Planning, Shared Flight Capabilities, Empty Leg Optimization, Quote Orchestration, and Advanced Payment Processing. This unified domain encompasses all booking-related operations from initial trip planning through post-flight completion, including sophisticated revenue optimization through shared flights and empty leg marketplace."

intent_assertions:
- "Complete booking ecosystem management spanning trip planning, quote orchestration, booking lifecycle, shared flights, and empty leg optimization"
- "Multi-leg trip planning with intelligent route optimization, aircraft positioning, and seamless passenger experience coordination"
- "Advanced shared flight marketplace enabling seat-level booking, revenue sharing, and community-driven travel optimization"
- "Comprehensive empty leg management with automated operator solicitation, marketplace integration, and booking optimization strategies"
- "Sophisticated quote orchestration with AI-powered request processing, operator coordination, and dynamic pricing optimization"
- "Complete booking lifecycle management with multi-payment support, real-time tracking, and automated post-flight processing"
- "Revenue optimization through intelligent empty leg conversion, shared flight creation, and dynamic pricing strategies"
- "Zero booking failures through automated recovery workflows, alternative route suggestions, and proactive optimization"
- "Advanced payment processing with multi-method support, cryptocurrency integration, and operator settlement automation"
- "Comprehensive audit trails, performance analytics, and operator reliability scoring across all booking operations"

technical_assertions:
  # Trip Planning Subdomain
  - path: "app/api/v1/endpoints/bookings/trips.py"
    purpose: "Multi-leg trip planning and itinerary management API"
    lines: 454
    subdomain: "trip_planning"
    endpoints: [
      "GET /api/v1/bookings/trips/ - List user trips with filtering",
      "POST /api/v1/bookings/trips/ - Create trip with multi-leg support",
      "GET /api/v1/bookings/trips/{trip_id} - Get trip details",
      "PUT /api/v1/bookings/trips/{trip_id} - Update trip itinerary",
      "DELETE /api/v1/bookings/trips/{trip_id} - Delete trip",
      "GET /api/v1/bookings/trips/user/{user_id} - Get user trip history"
    ]

  # Quote Orchestration Subdomain
  - path: "app/api/v1/endpoints/bookings/quotes.py"
    purpose: "AI-powered quote request processing and operator coordination"
    lines: 48
    subdomain: "quotes"
    endpoints: [
      "POST /api/v1/quotes/request - AI-powered quote request processing",
      "GET /api/v1/quotes/{quote_id} - Get quote details for booking"
    ]

  # Core Booking Lifecycle Subdomain
  - path: "app/api/v1/endpoints/bookings/bookings.py"
    purpose: "Primary booking lifecycle management with comprehensive status tracking"
    lines: 1129
    subdomain: "booking_lifecycle"
    endpoints: [
      "POST /api/v1/bookings/ - Create booking with quote confirmation",
      "POST /api/v1/bookings/bitcoin - Create booking with Bitcoin payment",
      "GET /api/v1/bookings/{booking_id} - Get detailed booking information",
      "GET /api/v1/bookings/user/{user_id} - Get user booking history",
      "PATCH /api/v1/bookings/{booking_id}/depart - Mark flight departed",
      "PATCH /api/v1/bookings/{booking_id}/arrive - Mark passenger arrival",
      "POST /api/v1/bookings/{booking_id}/complete - Complete post-flight processing",
      "GET /api/v1/bookings/payment/{invoice_id}/status - Check payment status",
      "POST /api/v1/bookings/{booking_id}/refund - Process booking refund"
    ]

  # Shared Flight Subdomain
  - path: "app/api/v1/endpoints/bookings/flight_sharing.py"
    purpose: "Shared flight marketplace and seat-level booking management"
    lines: 390
    subdomain: "shared_flights"
    endpoints: [
      "POST /api/v1/shared-flights/empty-leg/{empty_leg_id} - Create shared flight",
      "POST /api/v1/shared-flights/{shared_flight_id}/book - Book seats on shared flight",
      "POST /api/v1/bookings/{booking_id}/cancel - Cancel shared flight booking"
    ]

  # Empty Leg Subdomain
  - path: "app/api/v1/endpoints/bookings/empty_legs.py"
    purpose: "Empty leg marketplace and booking optimization platform"
    lines: 750
    subdomain: "empty_legs"
    endpoints: [
      "GET /api/v1/bookings/empty-legs - List available empty legs",
      "POST /api/v1/empty-legs/{booking_id}/optimize - Optimize booking for confirmation",
      "POST /api/v1/empty-legs/{booking_id}/request-sharing - Request seat sharing",
      "GET /api/v1/empty-legs/shared-from-booking/{booking_id} - Get shared flight details"
    ]

  # Trip Planning Services
  - path: "app/services/trips_service.py"
    purpose: "Multi-leg trip planning and itinerary management service"
    lines: 1087
    subdomain: "trip_planning"
    operations: [
      "get_trips_for_user - Retrieve user trips with filtering",
      "create_trip - Create multi-leg trip with route optimization",
      "_booking_to_trip_response - Convert booking to trip format",
      "_map_booking_status_to_trip_status - Status mapping for UI",
      "_determine_payment_status - Payment status determination"
    ]

  - path: "app/utils/routing.py"
    purpose: "Advanced route calculation and flight time estimation"
    lines: 194
    subdomain: "trip_planning"
    operations: [
      "calculate_distance_nm_by_icao - Great circle distance calculation",
      "estimate_flight_time - Multi-factor flight time estimation",
      "get_airport_coordinates - Airport coordinate resolution"
    ]

  # Quote Orchestration Services
  - path: "app/services/orchestration_service.py"
    purpose: "AI-powered quote orchestration with intelligent operator coordination"
    lines: 457
    subdomain: "quotes"
    operations: [
      "handle_quote_request - End-to-end AI-powered quote request processing",
      "confirm_booking - Complete booking confirmation workflow",
      "complete_post_flight - Post-flight processing and metrics"
    ]

  - path: "app/services/legacy_services/orchestration.py"
    purpose: "Legacy booking orchestration service (deprecated)"
    lines: 932
    subdomain: "quotes"
    status: "deprecated"

  # Core Booking Lifecycle Services
  - path: "app/services/booking_service.py"
    purpose: "Primary booking lifecycle management with database manager integration"
    lines: 548
    subdomain: "booking_lifecycle"
    operations: [
      "confirm_booking - Booking confirmation with quote selection",
      "cancel_booking - Booking cancellation with refund processing",
      "get_booking_quotes - Retrieve available quotes for booking",
      "update_booking - Booking status and metadata updates"
    ]

  - path: "app/services/payment_service.py"
    purpose: "Multi-payment method processing with Stripe and Bitcoin support"
    lines: 855
    subdomain: "booking_lifecycle"
    operations: [
      "process_stripe_payment - Credit card payment processing",
      "generate_btcpay_invoice - Bitcoin payment with 15% discount",
      "verify_payment_status - Real-time payment status checking",
      "refund_payment - Automated refund processing"
    ]

  # Shared Flight Services
  - path: "app/services/flight_sharing_service.py"
    purpose: "Shared flight marketplace and seat-level booking management"
    lines: 45
    subdomain: "shared_flights"
    operations: [
      "create_shared_flight - Create shared flights from empty legs",
      "book_shared_flight_seats - Individual seat booking with payment processing",
      "cancel_seat_booking - Cancel shared flight booking with seat release"
    ]

  - path: "app/services/legacy_services/flight_sharing.py"
    purpose: "Advanced shared flight service with profitability validation"
    lines: 700
    subdomain: "shared_flights"
    operations: [
      "create_shared_flight - Create shared flights with dynamic pricing",
      "book_shared_flight_seats - Seat booking with availability validation",
      "add_passenger_to_shared_flight - Add passengers with profitability checks"
    ]

  # Empty Leg Services
  - path: "app/services/empty_leg_processor_service.py"
    purpose: "Automated empty leg optimization and shared flight conversion"
    lines: 224
    subdomain: "empty_legs"
    operations: [
      "process_pending_bookings - Optimize bookings for confirmation",
      "optimize_booking_for_confirmation - Multi-strategy booking optimization",
      "_can_create_shared_flight - Shared flight eligibility checking",
      "_create_shared_flight_for_booking - Convert booking to shared flight"
    ]

  - path: "app/services/legacy_services/empty_leg_processor.py"
    purpose: "Advanced empty leg processing with marketplace integration"
    lines: 900
    subdomain: "empty_legs"
    operations: [
      "optimize_booking_for_confirmation - Multi-strategy optimization",
      "process_sharing_requests_from_clients - Client seat sharing requests",
      "_create_shared_flight_for_client - Marketplace listing creation",
             "_calculate_dynamic_commission - Commission rate optimization"
     ]

subdomains:
  trip_planning:
    description: "Multi-leg charter journey orchestration and itinerary management"
    purpose: "Complex trip planning with route optimization, aircraft positioning, and passenger experience coordination"
    system_definition: "villiers_system_definitions/booking/trip_planning/trip_planning.yaml"
    api_files:
      - "app/api/v1/endpoints/bookings/trips.py"
    service_files:
      - "app/services/trips_service.py"
      - "app/utils/routing.py"
    key_features:
      - "Multi-leg trip planning with FlightLeg support"
      - "Advanced route optimization with Haversine algorithms"
      - "Intelligent aircraft positioning and efficiency optimization"
      - "Comprehensive trip modification and rebooking capabilities"

  quotes:
    description: "AI-powered quote request processing and operator coordination"
    purpose: "Intelligent quote orchestration with automated operator communication and dynamic pricing"
    system_definition: "villiers_system_definitions/booking/quotes/quotes.yaml"
    api_files:
      - "app/api/v1/endpoints/bookings/quotes.py"
    service_files:
      - "app/services/orchestration_service.py"
      - "app/services/legacy_services/orchestration.py"
    key_features:
      - "AI-powered natural language quote request processing"
      - "Automated operator email communication and coordination"
      - "Dynamic pricing optimization and competitiveness assessment"
      - "Intelligent quote aggregation and presentation"

  booking_lifecycle:
    description: "Complete booking lifecycle management with multi-payment support"
    purpose: "End-to-end booking management from creation through post-flight completion"
    system_definition: "villiers_system_definitions/booking/booking_lifecycle/booking_lifecycle.yaml"
    api_files:
      - "app/api/v1/endpoints/bookings/bookings.py"
    service_files:
      - "app/services/booking_service.py"
      - "app/services/payment_service.py"
    key_features:
      - "Complete booking status workflow (SOURCING → PENDING → CONFIRMED → COMPLETED)"
      - "Multi-payment method support (Stripe, Bitcoin with 15% discount)"
      - "Real-time flight tracking and passenger notifications"
      - "Comprehensive refund processing and audit trails"

  shared_flights:
    description: "Shared flight marketplace and seat-level booking management"
    purpose: "Revenue optimization through seat sharing and community-driven travel"
    system_definition: "villiers_system_definitions/booking/shared_flights/shared_flights.yaml"
    api_files:
      - "app/api/v1/endpoints/bookings/flight_sharing.py"
    service_files:
      - "app/services/flight_sharing_service.py"
      - "app/services/legacy_services/flight_sharing.py"
    key_features:
      - "Empty leg conversion to shared flights with dynamic pricing"
      - "Individual seat booking with real-time availability management"
      - "Commission-based revenue sharing for seat providers"
      - "Advanced profitability validation and confirmation thresholds"

  empty_legs:
    description: "Empty leg marketplace and booking optimization platform"
    purpose: "Automated empty leg management with operator solicitation and booking optimization"
    system_definition: "villiers_system_definitions/booking/empty_legs/empty_legs.yaml"
    api_files:
      - "app/api/v1/endpoints/bookings/empty_legs.py"
    service_files:
      - "app/services/empty_leg_processor_service.py"
      - "app/services/legacy_services/empty_leg_processor.py"
    key_features:
      - "Automated operator empty leg solicitation campaigns"
      - "Multi-strategy booking optimization for confirmation"
      - "Intelligent shared flight conversion from empty legs"
      - "Client seat sharing requests with marketplace integration"

schemas:
  database_schemas:
    - path: "app/db/schemas/booking.py"
      purpose: "Booking API schemas with validation and relationships"
      lines: 51
      schemas: [
        "BookingBase/Create/Update - Core booking data transfer objects",
        "BookingWithRelations - Booking with full relationship data",
        "BookingInList - Simplified booking list format"
      ]

  service_schemas:
    - path: "app/schemas/booking.py"
      purpose: "Service-layer booking schemas for API responses"
      schemas: [
        "BookingResponse - Comprehensive booking API response",
        "BookingListItem - Booking list item for user interfaces",
        "PaymentStatusResponse - Payment status API response",
        "RefundResponse - Refund processing response",
        "BookingCompletionResponse - Post-flight completion response"
      ]



scheduler_tasks:
  - task: "Booking Processing"
    path: "app/tasks/booking_processor.py"
    purpose: "Automated booking processing and optimization tasks"
    lines: 84
    schedule: "every 15 minutes"
    operations: [
      "process_bookings_task - Pending booking confirmation automation",
      "process_sharing_requests_from_clients - Shared flight creation",
      "process_bitcoin_settlement_incentives - Bitcoin payment incentives"
    ]

implementation_status:
  fully_implemented:
    booking_lifecycle:
      description: "Complete booking lifecycle from creation to completion"
      components:
        - "Booking creation with quote confirmation and payment processing"
        - "Multi-payment support (Stripe credit cards, Bitcoin with 15% discount)"
        - "Real-time status tracking (SOURCING → PENDING → CONFIRMED → COMPLETED)"
        - "Flight departure and arrival tracking with notifications"
        - "Post-flight completion with operator performance metrics"
        - "Comprehensive refund processing with automated workflows"
      
    orchestration_engine:
      description: "Automated booking orchestration with intelligent optimization"
      components:
        - "Quote competitiveness assessment with dynamic pricing optimization"
        - "Payment processing integration with multiple providers"
        - "Contract generation and document management"
        - "Operator notification and communication workflows"
        - "Conversion tracking and analytics integration"
        - "Adaptive user behavior and operator performance updates"

    shared_flight_system:
      description: "Advanced shared flight and empty leg booking capabilities"
      components:
        - "Empty leg conversion to shared flights for revenue optimization"
        - "Individual seat booking on shared flights with dynamic pricing"
        - "Seat availability management with real-time updates"
        - "Shared flight confirmation based on minimum seat requirements"
        - "Commission tracking and revenue sharing calculations"

    repository_layer:
      description: "Comprehensive data access layer with advanced query capabilities"
      components:
        - "Booking CRUD operations with relationship management"
        - "User booking history with pagination and filtering"
        - "Operator booking statistics and performance metrics"
        - "Profitability analysis and financial reporting"
        - "Shared flight and seat management operations"

  partially_implemented:
    contract_management:
      description: "Contract generation and document workflow"
      current_state:
        - "Contract service integration in orchestration workflow"
        - "Contract file path storage in booking metadata"
        - "Document management structure defined"
      missing_components:
        - "Complete contract template system"
        - "Digital signature integration"
        - "Document version control and audit trail"
      implementation_priority: "medium"
      estimated_effort: "2-3 weeks"

    advanced_analytics:
      description: "Comprehensive booking analytics and reporting"
      current_state:
        - "Basic conversion tracking and operator performance metrics"
        - "Booking status and payment analytics"
        - "Integration with analytics domain"
      missing_components:
        - "Advanced booking funnel analysis"
        - "Predictive booking success modeling"
        - "Revenue optimization recommendations"
      implementation_priority: "medium"
      estimated_effort: "3-4 weeks"

  planned_future:
    mobile_booking_api:
      description: "Mobile-optimized booking API endpoints"
      scope: "Simplified booking workflows for mobile applications"
      estimated_effort: "4-6 weeks"
      
    international_payments:
      description: "International payment method support"
      scope: "SEPA, wire transfers, and regional payment processors"
      estimated_effort: "6-8 weeks"

behavior:
  booking_creation_workflow:
    quote_selection:
      - "User selects preferred quote from available options"
      - "System validates quote availability and pricing"
      - "Quote competitiveness assessment with dynamic pricing optimization"
      - "Platform fee calculation with potential discounts applied"
    
    payment_processing:
      - "Multi-payment method support (Stripe, Bitcoin with 15% discount)"
      - "Real-time payment verification and confirmation"
      - "Payment failure handling with retry mechanisms"
      - "Secure payment data storage and PCI compliance"
    
    booking_confirmation:
      - "Atomic booking creation with quote validation"
      - "Booking status transition (SOURCING → PENDING → CONFIRMED)"
      - "Operator notification and communication workflows"
      - "Contract generation and document management initiation"
      - "User confirmation notifications with next steps"

  flight_lifecycle_tracking:
    departure_processing:
      - "Flight departure confirmation with timestamp recording"
      - "Passenger notification and tracking activation"
      - "Real-time flight status updates and communication"
      - "Emergency contact notification protocols"
    
    arrival_processing:
      - "Passenger arrival confirmation at destination"
      - "Arrival timestamp and location verification"
      - "Ground transportation coordination notifications"
      - "Return flight reminders for round-trip bookings"
    
    completion_workflow:
      - "Post-flight processing with operator performance metrics"
      - "Customer feedback collection and rating systems"
      - "Financial reconciliation and payment finalization"
      - "Marketing follow-up and future booking incentives"

  shared_flight_management:
    empty_leg_conversion:
      - "Automated empty leg identification and conversion eligibility"
      - "Shared flight creation with optimal seat pricing"
      - "Seat inventory management and availability tracking"
      - "Minimum seat requirement enforcement for flight confirmation"
    
    seat_booking_process:
      - "Individual seat booking with real-time availability"
      - "Dynamic pricing based on demand and flight proximity"
      - "Seat selection and passenger detail collection"
      - "Booking confirmation and flight status updates"

  payment_and_refund_processing:
    payment_workflows:
      - "Stripe credit card processing with secure tokenization"
      - "Bitcoin payment processing with 15% automatic discount"
      - "Payment status tracking and webhook integration"
      - "Failed payment recovery and retry mechanisms"
    
    refund_processing:
      - "Automated refund policy enforcement (24+ hours full refund)"
      - "Partial refund calculation for time-based policies"
      - "Multi-payment method refund processing"
      - "Refund status tracking and customer communication"

endpoints:
  booking_lifecycle:
    - path: "/api/v1/bookings/"
      methods: ["POST"]
      description: "Create booking with quote confirmation and payment"
      response_time: "<2000ms"
      handler: "bookings.py:create_booking"
    
    - path: "/api/v1/bookings/bitcoin"
      methods: ["POST"] 
      description: "Create booking with Bitcoin payment (15% discount)"
      response_time: "<3000ms"
      handler: "bookings.py:create_bitcoin_booking"
    
    - path: "/api/v1/bookings/{booking_id}"
      methods: ["GET"]
      description: "Get comprehensive booking details"
      response_time: "<500ms"
      handler: "bookings.py:get_booking"
    
    - path: "/api/v1/bookings/user/{user_id}"
      methods: ["GET"]
      description: "Get user booking history with pagination"
      response_time: "<1000ms"
      handler: "bookings.py:get_user_bookings"

  flight_operations:
    - path: "/api/v1/bookings/{booking_id}/depart"
      methods: ["PATCH"]
      description: "Mark flight as departed with tracking activation"
      response_time: "<200ms"
      handler: "bookings.py:mark_flight_departed"
    
    - path: "/api/v1/bookings/{booking_id}/arrive"
      methods: ["PATCH"]
      description: "Mark passenger arrival at destination"
      response_time: "<200ms"
      handler: "bookings.py:mark_passenger_arrival"
    
    - path: "/api/v1/bookings/{booking_id}/complete"
      methods: ["POST"]
      description: "Complete post-flight processing and metrics"
      response_time: "<1000ms"
      handler: "bookings.py:complete_booking"

  payment_management:
    - path: "/api/v1/bookings/payment/{invoice_id}/status"
      methods: ["GET"]
      description: "Real-time payment status verification"
      response_time: "<300ms"
      handler: "bookings.py:check_payment_status"
    
    - path: "/api/v1/bookings/{booking_id}/refund"
      methods: ["POST"]
      description: "Process booking refund with policy enforcement"
      response_time: "<2000ms"
      handler: "bookings.py:refund_booking"

  shared_flights:
    - path: "/api/v1/shared-flights/empty-leg/{empty_leg_id}"
      methods: ["POST"]
      description: "Create shared flight from empty leg"
      response_time: "<1000ms"
      handler: "flight_sharing.py:create_shared_flight_from_empty_leg"
    
    - path: "/api/v1/bookings/{booking_id}/cancel"
      methods: ["POST"]
      description: "Cancel shared flight booking with seat release"
      response_time: "<500ms"
      handler: "flight_sharing.py:cancel_shared_flight_booking"

database_models:
  - model: "Booking"
    purpose: "Primary booking entity with comprehensive payment and status tracking"
    fields: [
      "id", "quote_id", "user_id", "aircraft_id", "operator_id", "trip_id",
      "status", "payment_status", "platform_fee_amount", "platform_fee_payment_id",
      "platform_fee_payment_method", "platform_fee_discount", "operator_payment_amount",
      "operator_payment_reference", "operator_payment_instructions", "contract_file",
      "invoice_file", "signed_contract_file", "passenger_details", "notes",
      "booking_metadata", "completed_at"
    ]
    indexes: ["user_id", "quote_id", "operator_id", "status", "payment_status"]
    relationships: [
      "quote", "user", "aircraft", "operator", "trip", "conversion_event",
      "aircraft_status", "notifications", "payments", "feedback", "booking_notes",
      "flights", "conversations", "messages", "empty_legs", "seat_tickets"
    ]

  - model: "BookingNote"
    purpose: "Booking communication and administrative notes tracking"
    fields: [
      "id", "booking_id", "content", "note_type", "is_internal",
      "is_operator_visible", "is_user_visible", "created_by_id", "created_by_name"
    ]
    indexes: ["booking_id", "note_type", "created_by_id"]
    relationships: ["booking", "created_by"]

services:
  - service: "BookingService"
    path: "app/services/booking_service.py"
    purpose: "Primary booking business logic with database manager integration"
    methods: [
      "confirm_booking", "cancel_booking", "get_booking_quotes",
      "update_booking", "get_booking_by_id", "_booking_to_dict"
    ]
    dependencies: ["db_manager", "notification_service"]

  - service: "OrchestrationService"
    path: "app/services/orchestration_service.py"
    purpose: "End-to-end booking lifecycle orchestration"
    methods: [
      "handle_quote_request", "confirm_booking", "complete_post_flight"
    ]
    dependencies: [
      "db_manager", "ai_engine", "email_service", "payment_service",
      "contract_service", "adaptive_service", "pricing_optimizer", "conversion_optimizer"
    ]

  - service: "PaymentService"
    path: "app/services/payment_service.py"
    purpose: "Multi-payment method processing with security compliance"
    methods: [
      "process_stripe_payment", "generate_btcpay_invoice", "verify_payment_status",
      "refund_payment"
    ]
    security: ["PCI_compliance", "cryptocurrency_support"]

  - service: "FlightSharingService"
    path: "app/services/flight_sharing_service.py"
    purpose: "Shared flight and empty leg booking management"
    methods: ["create_shared_flight", "book_seats", "cancel_seat_booking"]
    dependencies: ["db_manager"]

  - service: "EmptyLegProcessorService"
    path: "app/services/empty_leg_processor_service.py"
    purpose: "Automated empty leg and shared flight optimization"
    methods: [
      "_can_create_shared_flight", "_create_shared_flight_for_booking"
    ]
    dependencies: ["db_manager"]

repositories:
  - repository: "BookingRepository"
    path: "app/db/manager/repositories/booking_repository.py"
    purpose: "Comprehensive booking data access with advanced analytics"
    methods: [
      "get_booking_by_id", "get_active_bookings_for_user", "get_user_bookings",
      "update_booking_status", "validate_flight_profitability", "confirm_booking",
      "get_operator_booking_stats", "get_pending_bookings_for_processing"
    ]
    validation: ["booking_ownership", "status_transitions", "payment_validation"]

  - repository: "OrchestrationRepository"
    path: "app/db/manager/repositories/orchestration_repository.py"
    purpose: "Orchestration workflow data access and atomic operations"
    methods: [
      "create_booking", "complete_booking", "update_operator_reliability"
    ]
    validation: ["quote_ownership", "atomic_transactions"]

  - repository: "SharedFlightRepository"
    path: "app/db/manager/repositories/shared_flight_repository.py"
    purpose: "Shared flight and seat booking data management"
    methods: [
      "create_shared_flight_from_empty_leg", "book_shared_flight_seats"
    ]
    validation: ["seat_availability", "flight_timing", "pricing_validation"]

scheduler_integration:
  booking_automation:
    - task: "Booking Processing"
      schedule: "every 15 minutes"
      purpose: "Process pending bookings and confirm profitable flights"
      handler: "booking_processor.py:process_bookings_task"
    
    - task: "Sharing Request Processing"
      schedule: "every 15 minutes"
      purpose: "Process client sharing requests and create shared flights"
      handler: "booking_processor.py:process_sharing_requests_from_clients"
    
    - task: "Bitcoin Settlement Incentives"
      schedule: "daily 3:00 AM"
      purpose: "Process Bitcoin payment incentives for operators"
      handler: "booking_processor.py:process_bitcoin_settlement_incentives"

invariants:
- "Booking status transitions must follow defined workflow (SOURCING → PENDING → CONFIRMED → COMPLETED)"
- "Payment processing must be completed before booking confirmation"
- "Quote ownership must be validated before booking creation"
- "Refund amounts cannot exceed original payment amounts"
- "Shared flight seat bookings cannot exceed aircraft capacity"
- "Empty leg conversions must maintain flight timing and operator constraints"
- "Booking metadata must maintain audit trail for all status changes"
- "Payment sensitive data must be encrypted and PCI compliant"
- "Operator notifications must be sent for all booking confirmations"
- "Post-flight completion must update operator performance metrics"

forbidden_states:
- "Bookings confirmed without valid payment processing"
- "Quote selection without user ownership validation"
- "Refunds processed without proper authorization"
- "Shared flight creation exceeding aircraft seat capacity"
- "Payment data transmitted without encryption"
- "Booking status changes without audit trail logging"
- "Operator payments processed without booking confirmation"
- "Flight tracking without proper booking status validation"
- "Contract generation without booking confirmation"
- "Post-flight completion without arrival confirmation"

depends_on:
- "authentication - User authentication and authorization"
- "aircraft - Aircraft data and availability management"
- "airport - Airport data and routing information"
- "analytics - Booking metrics and performance tracking"

provides:
- "booking_lifecycle_management - Complete booking workflow orchestration"
- "multi_payment_processing - Stripe and Bitcoin payment integration"
- "shared_flight_capabilities - Empty leg and seat sharing functionality"
- "booking_analytics_data - Performance metrics and conversion tracking"
- "operator_performance_metrics - Booking-based operator reliability scoring"
- "customer_booking_history - User booking management and tracking"

enforcement_hooks:
- "validate_booking_creation_requirements"
- "verify_payment_processing_completion"
- "ensure_operator_notification_delivery"
- "confirm_audit_trail_completeness"
- "validate_shared_flight_seat_availability"

error_handling:
  booking_creation_errors:
    - "Quote not found or expired - Return 404 with quote refresh instructions"
    - "Payment processing failure - Return 400 with payment retry options"
    - "Insufficient aircraft availability - Return 409 with alternative suggestions"
    - "User authorization failure - Return 403 with authentication requirements"

  payment_processing_errors:
    - "Stripe payment declined - Provide alternative payment methods"
    - "Bitcoin payment timeout - Extend payment window with notification"
    - "Refund processing failure - Queue for manual review and processing"
    - "Payment verification timeout - Implement retry with exponential backoff"

  operational_errors:
    - "Flight departure tracking failure - Continue with manual operator notification"
    - "Operator notification delivery failure - Queue for retry with escalation"
    - "Contract generation failure - Allow manual contract upload workflow"
    - "Analytics tracking failure - Log error and continue booking processing"

monitoring:
  booking_metrics:
    - "Booking creation success rate (target: >99%)"
    - "Payment processing completion rate (target: >98%)"
    - "Average booking confirmation time (target: <30 seconds)"
    - "Refund processing time (target: <24 hours)"
    - "Shared flight conversion rate (target: >15%)"

  performance_monitoring:
    - "API endpoint response times with P95 tracking"
    - "Database query performance for booking operations"
    - "Payment processor integration latency monitoring"
    - "Operator notification delivery success rates"

  business_metrics:
    - "Booking conversion rates from quote requests"
    - "Revenue per booking with payment method breakdown"
    - "Customer satisfaction scores from post-flight feedback"
    - "Operator performance metrics and reliability scores"

security:
  payment_security:
    - "PCI DSS compliance for credit card processing"
    - "Cryptocurrency transaction security and validation"
    - "Payment data encryption at rest and in transit"
    - "Secure payment tokenization and storage"

  booking_security:
    - "User authorization validation for all booking operations"
    - "Booking ownership verification for modifications"
    - "Operator data access control and permissions"
    - "Audit trail logging for all booking status changes"

  api_security:
    - "Rate limiting on booking creation endpoints"
    - "Input validation and sanitization for all booking data"
    - "Authentication required for all booking operations"
    - "Authorization checks for administrative functions"

performance:
  response_time_targets:
    - "Booking creation: <2000ms (95th percentile)"
    - "Booking retrieval: <500ms (95th percentile)"
    - "Payment status check: <300ms (95th percentile)"
    - "Status updates: <200ms (95th percentile)"

  throughput_targets:
    - "Support 1000+ concurrent booking operations"
    - "Handle 10000+ booking status queries per minute"
    - "Process 500+ payment transactions per hour"
    - "Manage 100+ shared flight bookings simultaneously"

  availability_targets:
    - "99.9% uptime for booking API endpoints"
    - "99.95% uptime for payment processing systems"
    - "Real-time booking status synchronization"
    - "Automated failover for critical booking operations"

testing:
  integration_testing:
    - "End-to-end booking workflow validation"
    - "Multi-payment method processing verification"
    - "Shared flight booking and cancellation testing"
    - "Operator notification and communication testing"

  performance_testing:
    - "Load testing for concurrent booking operations"
    - "Payment processing performance under load"
    - "Database performance with large booking datasets"
    - "API response time validation under stress"

  security_testing:
    - "Payment security and PCI compliance validation"
    - "User authorization and access control testing"
    - "Data encryption and secure transmission verification"
    - "Audit trail completeness and integrity testing"

consolidation_notes:
  booking_ownership:
    - "Booking domain owns complete lifecycle from quote selection to completion"
    - "Integration with authentication for user validation and authorization"
    - "Coordination with aircraft domain for availability and specifications"
    - "Integration with analytics domain for performance tracking and metrics"

  service_integration:
    - "Primary booking service with database manager pattern"
    - "Orchestration service for complex workflow coordination"
    - "Payment service integration for multi-method processing"
    - "Shared flight service for revenue optimization capabilities"

files:
  - "app/api/v1/endpoints/bookings/trips.py - Multi-leg trip planning API"
  - "app/api/v1/endpoints/bookings/quotes.py - Quote orchestration API"
  - "app/api/v1/endpoints/bookings/bookings.py - Core booking lifecycle API"
  - "app/api/v1/endpoints/bookings/flight_sharing.py - Shared flight marketplace API"
  - "app/api/v1/endpoints/bookings/empty_legs.py - Empty leg optimization API"
  - "app/services/trips_service.py - Trip planning and itinerary management"
  - "app/services/orchestration_service.py - Quote orchestration and workflow"
  - "app/services/booking_service.py - Core booking lifecycle management"
  - "app/services/payment_service.py - Multi-payment processing"
  - "app/services/flight_sharing_service.py - Shared flight management"
  - "app/services/empty_leg_processor_service.py - Empty leg optimization"
  - "app/services/legacy_services/orchestration.py - Legacy orchestration (deprecated)"
  - "app/services/legacy_services/flight_sharing.py - Advanced shared flight service"
  - "app/services/legacy_services/empty_leg_processor.py - Advanced empty leg processing"
  - "app/utils/routing.py - Route calculation and flight time estimation"
  - "app/db/models/booking.py - Booking and BookingNote models"
  - "app/db/schemas/booking.py - Database booking schemas"
  - "app/schemas/booking.py - Service booking schemas"
  - "app/db/manager/repositories/booking_repository.py - Booking data access"
  - "app/db/manager/repositories/orchestration_repository.py - Orchestration data access"
  - "app/db/manager/repositories/shared_flight_repository.py - Shared flight data access"
  - "app/tasks/booking_processor.py - Automated booking processing and optimization"

 