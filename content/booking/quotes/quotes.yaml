system: villiers_quotes
description: "Villiers.ai Quotes Subdomain - World-Class Charter Aviation Price Estimation with AI-Powered Quote Generation, Multi-Factor Pricing Engines, and Conversion Optimization. This subdomain owns all quote creation, pricing calculation, delivery, lifecycle management, and booking conversion workflows for private jet charter opportunities."

intent_assertions:
- "Complete quote lifecycle management from flight request capture to booking conversion"
- "AI-powered quote generation with natural language processing and intent recognition"
- "Multi-factor pricing engines with aircraft, operator, route, and seasonal optimization"
- "Real-time quote calculation with distance, flight time, and cost factor analysis"
- "Intelligent quote ranking and conversion optimization based on historical performance"
- "Automated quote expiration management with configurable validity periods"
- "Comprehensive quote validation and admin approval workflows"
- "Seamless quote-to-booking conversion with payment processing integration"

technical_assertions:
- path: "app/db/models/quote.py"
  purpose: "Complete quote data models with pricing, validation, and operator integration"
  lines: 196
  models: ["Quote", "QuoteOperatorData"]
  
- path: "app/services/pricing_service.py"
  purpose: "Core pricing engine with multi-factor cost calculation and optimization"
  lines: 900
  methods: ["calculate_quote_price", "estimate_cost", "get_pricing_breakdown"]
  
- path: "app/services/price_estimation_service.py"
  purpose: "Advanced price estimation with tunable adjustment factors and market analysis"
  lines: 300
  methods: ["estimate_price", "estimate_price_range", "apply_adjustment_factors"]
  
- path: "app/api/v1/endpoints/bookings/quotes.py"
  purpose: "Quote API endpoints with creation, ranking, and conversion workflows"
  lines: 115
  endpoints: ["/quotes/", "/quotes/{quote_id}/validate", "/quotes/{quote_id}/convert"]
  
- path: "app/db/manager/repositories/quote_repository.py"
  purpose: "Quote data access layer with conversion analytics and similarity matching"
  lines: 671
  operations: ["get_quote_by_id", "get_similar_quotes", "update_quote_platform_fee"]
  
- path: "app/services/orchestration_service.py"
  purpose: "Quote orchestration with AI parsing and ranking optimization"
  lines: 200
  methods: ["handle_quote_request", "rank_quotes_by_conversion"]
  
- path: "app/services/conversion_service.py"
  purpose: "Quote conversion optimization with historical performance analysis"
  lines: 150
  methods: ["rank_quotes_by_conversion", "calculate_conversion_scores"]
  
- path: "app/services/ai_service.py"
  purpose: "AI-powered quote generation with natural language intent parsing"
  lines: 400
  methods: ["generate_quote_estimates", "parse_flight_intent"]

database_models:
- model: "Quote"
  purpose: "Primary quote entity with comprehensive pricing, validation, and lifecycle data"
  fields: [
    "id", "user_id", "aircraft_id", "aircraft_type_id", "operator_id", "empty_leg_id",
    "departure_airport_id", "arrival_airport_id", "departure_time", "arrival_time",
    "return_departure_time", "return_arrival_time", "status", "is_request", "departure_date",
    "return_date", "is_one_way", "passengers", "luggage", "aircraft_type", "aircraft_category",
    "aircraft_preferences", "price", "currency", "price_breakdown", "price_expiry",
    "platform_fee", "platform_fee_percent", "is_validated", "validated_by", "validated_at",
    "notes", "route_details", "quote_metadata", "decline_reason", "decline_feedback"
  ]
  indexes: ["user_id", "status", "departure_airport_id", "arrival_airport_id", "departure_time"]
  relationships: [
    "user", "validated_by_user", "booking", "operator", "aircraft", "aircraft_type_obj",
    "conversations", "messages", "conversion_events", "operator_data", "notifications",
    "feedback", "departure_airport", "arrival_airport", "empty_leg"
  ]

- model: "QuoteOperatorData"
  purpose: "Operator-specific quote data with pricing, terms, and extraction metadata"
  fields: [
    "id", "quote_id", "operator_id", "aircraft_id", "price", "currency", "estimated_flight_time",
    "notes", "terms", "expiration_time", "is_confirmed", "source_message_id", "extracted_at",
    "extraction_confidence"
  ]
  tracking: "Email extraction and operator response processing"

services:
- service: "PricingService"
  path: "app/services/pricing_service.py"
  purpose: "Core pricing engine with multi-factor cost calculation and breakdown"
  methods: ["calculate_quote_price", "estimate_cost", "get_pricing_breakdown", "_calculate_flight_time"]
  integration: "Aircraft data, operator pricing rules, seasonal multipliers, demand factors"
  
- service: "PriceEstimationService"
  path: "app/services/price_estimation_service.py"
  purpose: "Advanced price estimation with tunable adjustment factors and market optimization"
  methods: ["estimate_price", "estimate_price_range", "apply_adjustment_factors", "_get_base_hourly_rate"]
  automation: "Market-based pricing adjustments and historical performance optimization"
  
- service: "OrchestrationService"
  path: "app/services/orchestration_service.py"
  purpose: "Quote orchestration with AI parsing, generation, and ranking"
  methods: ["handle_quote_request", "rank_quotes_by_conversion", "calculate_flight_metrics"]
  orchestration: "End-to-end quote workflow from request to ranked results"
  
- service: "ConversionService"
  path: "app/services/conversion_service.py"
  purpose: "Quote conversion optimization with historical performance analysis"
  methods: ["rank_quotes_by_conversion", "calculate_conversion_scores", "track_conversion_events"]
  analytics: "Conversion likelihood prediction and performance tracking"

repositories:
- repository: "QuoteRepository"
  purpose: "Quote data access with conversion analytics and similarity matching"
  methods: [
    "get_quote_by_id", "get_quote_with_aircraft", "get_quote_with_operator", "get_similar_quotes",
    "update_quote_platform_fee", "update_quote", "get_quotes_for_user", "get_quotes_paginated",
    "count_quotes_in_period"
  ]
  analytics: "Historical performance analysis, conversion tracking, similarity matching"

behavior:
  quote_generation:
    ai_powered_parsing:
      - "Natural language processing of flight requests with intent recognition"
      - "Automatic extraction of departure, arrival, dates, passengers, and preferences"
      - "Aircraft category inference based on route, passenger count, and requirements"
      - "Flight time and distance calculation using route estimation algorithms"
    
    pricing_calculation:
      - "Multi-factor pricing engine with base hourly rates and adjustment factors"
      - "Route-specific pricing with distance, flight time, and positioning costs"
      - "Seasonal demand multipliers based on region and time of year"
      - "Operator-specific pricing rules and negotiated rate structures"
      - "Empty leg integration for discounted pricing opportunities"
    
    quote_ranking:
      - "Conversion likelihood scoring based on historical performance data"
      - "Price competitiveness analysis with market positioning"
      - "Operator reliability and performance metrics integration"
      - "Customer preference matching with aircraft and service requirements"

  quote_lifecycle:
    creation_workflow:
      - "AI-powered quote generation from natural language flight requests"
      - "Multiple quote creation with aircraft category diversity and pricing options"
      - "Automatic flight metrics calculation (distance, time, routing)"
      - "Platform fee calculation with configurable percentage and discount rules"
    
    validation_process:
      - "Admin validation workflow for quote accuracy and availability verification"
      - "Operator confirmation tracking with response monitoring"
      - "Aircraft availability validation with scheduling conflict detection"
      - "Price verification against market rates and operator agreements"
    
    expiration_management:
      - "Configurable quote validity periods with automatic expiration handling"
      - "Quote status updates and customer notifications for expiring quotes"
      - "Operator-specific expiration rules based on terms and agreements"
      - "Grace period handling for near-expired quotes with conversion potential"

  conversion_optimization:
    performance_tracking:
      - "Quote conversion rate analysis with customer and operator segmentation"
      - "Historical performance data collection for pricing optimization"
      - "A/B testing support for quote presentation and pricing strategies"
      - "Customer behavior analysis for quote acceptance patterns"
    
    ranking_algorithms:
      - "Machine learning-based conversion likelihood prediction"
      - "Historical success rate weighting for quote prioritization"
      - "Customer preference matching with past booking patterns"
      - "Real-time market demand integration for dynamic ranking"

  operator_integration:
    quote_extraction:
      - "Automated quote extraction from operator email responses"
      - "Natural language processing for pricing, terms, and availability parsing"
      - "Confidence scoring for extracted quote data accuracy"
      - "Manual validation workflow for low-confidence extractions"
    
    response_tracking:
      - "Operator response time monitoring and performance analytics"
      - "Quote confirmation workflow with operator approval tracking"
      - "Terms and conditions parsing with automated compliance checking"
      - "Operator preference learning for improved quote targeting"

primary_flows:
  quote_request_flow:
    steps:
      - "Flight Request Capture: Customer submits flight requirements via natural language"
      - "AI Intent Parsing: Extract flight details, preferences, and requirements"
      - "Quote Generation: Create multiple quotes with diverse aircraft and pricing options"
      - "Pricing Calculation: Apply multi-factor pricing with all cost components"
      - "Quote Ranking: Rank quotes by conversion likelihood and customer fit"
      - "Quote Delivery: Present ranked quotes to customer with detailed breakdowns"
    
    automation:
      - "End-to-end automation from request to ranked quote delivery"
      - "Real-time pricing calculation with market-based adjustments"
      - "Automatic aircraft selection based on route and passenger requirements"

  quote_validation_flow:
    steps:
      - "Quote Review: Admin reviews quote accuracy and operator availability"
      - "Aircraft Verification: Confirm aircraft availability for requested dates"
      - "Price Validation: Verify pricing against market rates and agreements"
      - "Operator Confirmation: Track operator response and quote confirmation"
      - "Customer Notification: Notify customer of validated quote availability"
    
    quality_assurance:
      - "Multi-level validation with automated checks and manual review"
      - "Operator reliability verification with performance history"
      - "Price accuracy validation against historical and market data"

  conversion_tracking_flow:
    steps:
      - "Quote Presentation: Track customer quote viewing and engagement"
      - "Selection Monitoring: Monitor quote selection and comparison behavior"
      - "Conversion Events: Track quote acceptance and booking conversion"
      - "Performance Analysis: Analyze conversion rates and optimization opportunities"
      - "Feedback Integration: Incorporate customer feedback for quote improvement"
    
    analytics:
      - "Real-time conversion tracking with detailed funnel analysis"
      - "Customer behavior insights for quote optimization"
      - "Operator performance metrics for partnership optimization"

invariants:
- "Quote pricing must include all cost factors (base price, fees, positioning, fuel)"
- "Quote expiration must be enforced with automatic status updates"
- "Platform fee calculations must be consistent and auditable"
- "Quote validation must be performed by authorized admin users only"
- "Conversion tracking must maintain complete audit trail"
- "Quote-to-booking conversion must validate quote ownership and validity"
- "Operator quote data must include confidence scores for extraction accuracy"
- "Similar quote analysis must use consistent route and timing criteria"
- "Quote ranking must be based on measurable conversion likelihood factors"
- "Price breakdown must account for all cost components transparently"

forbidden_states:
- "Quotes created without proper flight requirement validation"
- "Pricing calculations missing mandatory cost factors"
- "Quote expiration without customer notification"
- "Conversion tracking without proper quote ownership validation"
- "Quote validation by unauthorized users"
- "Booking conversion from expired or invalid quotes"
- "Operator quote extraction without confidence scoring"
- "Platform fee application without proper calculation audit"
- "Quote ranking without historical performance data"
- "Price adjustments without admin authorization"

depends_on:
- "aircraft - Aircraft availability, specifications, and performance data"
- "operator - Operator pricing rules, availability, and performance metrics"
- "airport - Airport data for route calculation and fee estimation"
- "authentication - User authentication and admin authorization"
- "communication - Email processing and customer notification services"

provides:
- "quote_generation_engine - AI-powered quote creation and pricing calculation"
- "conversion_optimization_platform - Quote ranking and performance analytics"
- "pricing_calculation_service - Multi-factor pricing with market optimization"
- "operator_integration_system - Quote extraction and response tracking"
- "quote_lifecycle_management - Complete quote workflow from creation to conversion"
- "customer_quote_experience - Ranked quote delivery with detailed breakdowns"

enforcement_hooks:
- "validate_quote_pricing_accuracy"
- "ensure_quote_expiration_compliance"
- "confirm_admin_validation_authorization"
- "verify_conversion_tracking_completeness"
- "validate_operator_quote_confidence_scores"

scheduler_integration:
  quote_automation:
    - task: "Quote Expiration Processing"
      schedule: "every 30 minutes"
      purpose: "Process expired quotes and update status with customer notifications"
      handler: "quote_processor.py:process_expired_quotes"
      notifications: "Customer notifications for expiring and expired quotes"
    
    - task: "Conversion Analytics Processing"
      schedule: "daily 2:00 AM"
      purpose: "Process quote conversion analytics and update performance metrics"
      handler: "conversion_service.py:process_conversion_analytics"
      analytics: "Quote performance analysis and ranking algorithm optimization"
    
    - task: "Operator Response Monitoring"
      schedule: "every 15 minutes"
      purpose: "Monitor operator email responses and extract quote data"
      handler: "email_processor.py:process_operator_responses"
      extraction: "Automated quote extraction from operator communications"

endpoints:
  quote_creation:
    - path: "/api/v1/bookings/quotes/"
      methods: ["POST"]
      description: "Create quote request and get ranked quotes from operators"
      response_time: "<2000ms"
      handler: "quotes.py:create_quote"
      access: "User authentication required"
    
    - path: "/api/v1/admin/manual-quotes"
      methods: ["POST"]
      description: "Create manual quote with admin privileges"
      response_time: "<1000ms"
      handler: "admin.py:create_manual_quote"
      access: "Admin-only for manual quote creation"

  quote_management:
    - path: "/api/v1/admin-ui/quotes"
      methods: ["GET"]
      description: "Get paginated list of quotes for admin management"
      response_time: "<1000ms"
      handler: "admin_ui.py:get_admin_quotes"
      access: "Admin-only with filtering and pagination"
    
    - path: "/api/v1/admin-ui/quotes/{quote_id}/validate"
      methods: ["POST"]
      description: "Validate quote for accuracy and availability"
      response_time: "<500ms"
      handler: "admin_ui.py:validate_quote"
      access: "Admin-only for quote validation"

  quote_analytics:
    - path: "/api/v1/quotes/{quote_id}/conversion-score"
      methods: ["GET"]
      description: "Get conversion likelihood score for quote"
      response_time: "<300ms"
      handler: "quotes.py:get_conversion_score"
      access: "Admin access for conversion analytics"

error_handling:
  quote_errors:
    - error_type: "QuoteExpiredError"
      description: "Quote has expired and is no longer valid for booking"
      response_code: 410
      recovery: "Generate new quote or extend existing quote validity"
    
    - error_type: "InvalidPricingParametersError"
      description: "Quote pricing calculation failed due to invalid parameters"
      response_code: 400
      recovery: "Validate flight parameters and retry pricing calculation"
    
    - error_type: "QuoteValidationError"
      description: "Quote validation failed due to availability or accuracy issues"
      response_code: 422
      recovery: "Review quote details and operator availability"
    
    - error_type: "ConversionTrackingError"
      description: "Quote conversion tracking failed"
      response_code: 500
      recovery: "Retry conversion tracking with manual fallback"

monitoring:
  quote_metrics:
    - metric: "quote_conversion_rate"
      description: "Percentage of quotes converted to bookings"
      target: ">15%"
    
    - metric: "average_quote_response_time"
      description: "Average time from request to quote delivery"
      target: "<30 seconds"
    
    - metric: "quote_pricing_accuracy"
      description: "Accuracy of quote pricing vs final booking price"
      target: ">90%"
    
    - metric: "operator_quote_confirmation_rate"
      description: "Percentage of quotes confirmed by operators"
      target: ">70%"
    
    - metric: "quote_expiration_rate"
      description: "Percentage of quotes expiring without conversion"
      target: "<60%"

testing:
  quote_testing:
    - test_type: "Unit Tests"
      coverage: "Quote pricing calculation, validation, and conversion logic"
      location: "tests/unit/services/test_pricing_service.py"
    
    - test_type: "Integration Tests"
      coverage: "Complete quote workflow from creation to conversion"
      location: "tests/integration/endpoints/bookings/test_quotes_endpoints.py"
    
    - test_type: "API Tests"
      coverage: "Quote API endpoints and response validation"
      location: "tests/integration/endpoints/admin/test_admin_quotes.py"

security:
  quote_security:
    - "Quote validation requires admin authorization with audit trail"
    - "Customer quote access restricted to quote owner and admin users"
    - "Pricing calculations include audit trail for all cost factors"
    - "Operator quote data includes confidence scoring for accuracy verification"
    - "Quote conversion tracking maintains complete customer privacy"

performance:
  quote_performance_targets:
    - "Quote generation: <2000ms from request to ranked results"
    - "Pricing calculation: <500ms per quote with full breakdown"
    - "Quote validation: <500ms per quote review"
    - "Conversion analytics: <300ms per score calculation"
    - "Quote search and filtering: <1000ms for paginated results"

consolidation_notes:
  quote_ownership:
    - "Complete quote lifecycle management consolidated in OrchestrationService"
    - "Pricing calculations centralized in PricingService with multiple engines"
    - "Conversion optimization unified in ConversionService"
    - "Quote validation standardized across admin interfaces"
    - "Operator integration consolidated in email processing workflows"

implementation_gaps:
  real_time_pricing_updates:
    status: "enhancement_opportunity"
    description: "Real-time pricing updates based on market demand and availability"
    current_state: "Static pricing with periodic recalculation"
    enhancement: "Dynamic pricing with real-time market data integration"
    priority: "medium"
    estimated_effort: "4-5 weeks"
  
  advanced_conversion_prediction:
    status: "enhancement_opportunity"
    description: "Machine learning-based conversion prediction with customer behavior analysis"
    current_state: "Rule-based conversion scoring with historical data"
    enhancement: "ML-powered prediction with behavioral pattern recognition"
    priority: "low"
    estimated_effort: "6-8 weeks"
  
  automated_quote_negotiation:
    status: "planned_future"
    description: "Automated quote negotiation with operators for better pricing"
    current_state: "Manual operator communication and negotiation"
    enhancement: "AI-powered negotiation with operator preference learning"
    priority: "low"
    estimated_effort: "8-10 weeks"

files:
- "app/db/models/quote.py - Complete quote data models and operator integration"
- "app/services/pricing_service.py - Core pricing engine with multi-factor calculation"
- "app/services/price_estimation_service.py - Advanced price estimation with market optimization"
- "app/api/v1/endpoints/bookings/quotes.py - Quote API endpoints and workflows"
- "app/db/manager/repositories/quote_repository.py - Quote data access and analytics"
- "app/services/orchestration_service.py - Quote orchestration and AI integration"
- "app/services/conversion_service.py - Quote conversion optimization and tracking"
- "app/services/ai_service.py - AI-powered quote generation and intent parsing"
- "app/db/schemas/quote.py - Quote database schemas and validation"
- "app/schemas/quotes.py - Quote API response schemas"
- "app/api/v1/endpoints/admin/admin_ui.py - Admin quote management interface"
- "tests/integration/endpoints/bookings/test_quotes_endpoints.py - Quote integration tests" 