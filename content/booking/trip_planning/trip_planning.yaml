# Trip Planning Subdomain System Definition
# Multi-Leg Charter Journey Orchestration and Itinerary Management

system: "trip_planning"
description: "Comprehensive trip planning subdomain for multi-leg charter aviation journey orchestration, route optimization, and itinerary management within the villiers.ai booking ecosystem"

intent_assertions:
- "Orchestrate complex multi-leg charter journeys with seamless passenger experiences across multiple destinations"
- "Optimize flight routing and aircraft positioning for maximum efficiency and cost-effectiveness"
- "Manage comprehensive trip itineraries with real-time coordination and passenger communication"
- "Enable dynamic trip modifications and rebooking with minimal disruption to travel plans"
- "Provide intelligent route alternatives and empty leg integration for cost optimization"
- "Ensure synchronized multi-leg timing with buffer management and contingency planning"

technical_assertions:
  api_endpoints:
    - path: "/api/v1/bookings/trips/"
      file: "app/api/v1/endpoints/bookings/trips.py"
      methods: ["GET", "POST", "PUT", "DELETE"]
      lines: 454
      description: "Complete trip lifecycle management API with multi-leg journey support"
      
    - path: "/api/v1/bookings/trips/{trip_id}"
      file: "app/api/v1/endpoints/bookings/trips.py"
      methods: ["GET", "PUT", "DELETE"]
      description: "Individual trip operations including detailed itinerary management"
      
    - path: "/api/v1/bookings/trips/user/{user_id}"
      file: "app/api/v1/endpoints/bookings/trips.py"
      methods: ["GET"]
      description: "User trip history and status filtering with pagination support"

  database_models:
    - model: "Trip"
      file: "app/db/models/trips.py"
      lines: 59
      description: "Core trip entity with multi-leg support, airport relationships, and journey coordination"
      key_fields: ["origin_airport_id", "destination_airport_id", "departure_date", "return_date", "trip_type", "requirements"]
      
    - model: "TripStatusEnum"
      file: "app/db/models/enums.py"
      lines: 354
      description: "Comprehensive trip lifecycle status management"
      values: ["DRAFT", "PENDING", "CONFIRMED", "SCHEDULED", "IN_PROGRESS", "COMPLETED", "CANCELLED", "DELAYED"]

  schemas:
    - schema: "FlightLeg"
      file: "app/schemas/trip.py"
      lines: 44
      description: "Multi-leg flight segment definition with tracking and timing coordination"
      
    - schema: "TripCreate"
      file: "app/schemas/trip.py"
      lines: 136
      description: "Trip creation schema with multi-leg journey support and passenger coordination"
      
    - schema: "TripUpdate"
      file: "app/schemas/trip.py"
      lines: 150
      description: "Trip modification schema supporting itinerary changes and rebooking"
      
    - schema: "TripResponse"
      file: "app/schemas/trip.py"
      lines: 186
      description: "Comprehensive trip response with complete journey details and status tracking"

  services:
    - service: "TripsService"
      file: "app/services/trips_service.py"
      lines: 1087
      description: "Core trip planning service with multi-leg journey orchestration and itinerary management"
      key_methods: ["get_trips_for_user", "create_trip", "_booking_to_trip_response", "_convert_trip_to_ui_response"]
      
    - service: "FlightRouteEstimator"
      file: "app/utils/routing.py"
      lines: 194
      description: "Advanced route calculation and flight time estimation for journey optimization"
      key_methods: ["calculate_distance_nm_by_icao", "estimate_flight_time", "calculate_distance_nm"]

  route_optimization:
    - utility: "FlightRouteEstimator"
      file: "app/utils/routing.py"
      capabilities: ["Great circle distance calculation", "Flight time estimation", "Cruise speed optimization"]
      algorithms: ["Haversine formula", "Multi-factor time estimation", "Speed-based route calculation"]
      
    - integration: "PricingService route optimization"
      file: "app/services/pricing_service.py"
      lines: 1335
      description: "Multi-leg itinerary cost calculation with route factor optimization"

  templates:
    - template: "passenger/itinerary.html"
      file: "app/templates/passenger/itinerary.html"
      lines: 527
      description: "Comprehensive itinerary presentation with multi-leg flight details"
      
    - template: "passenger/flight_update.html"
      file: "app/templates/passenger/flight_update.html"
      lines: 400
      description: "Trip modification and itinerary change notification template"

implementation_status:
  fully_implemented:
    basic_trip_management:
      description: "Core trip planning functionality with multi-leg journey support"
      components:
        - "Trip creation with origin/destination airport selection and date management"
        - "User trip history with status filtering and pagination support"
        - "Trip modification capabilities with itinerary updates and rebooking"
        - "Trip-to-booking conversion with quote integration and payment processing"
        - "Multi-leg flight segment definition with FlightLeg schema support"
        - "Comprehensive trip response formatting with journey details"
      
    route_calculation:
      description: "Advanced route optimization and flight time estimation"
      components:
        - "Great circle distance calculation using Haversine formula for accurate routing"
        - "Flight time estimation with multi-factor analysis including cruise speeds"
        - "Airport coordinate resolution and ICAO code validation"
        - "Route optimization considering aircraft positioning and operational costs"
        - "Distance calculation supporting both nautical miles and kilometers"

    ui_integration:
      description: "Comprehensive user interface integration for trip planning"
      components:
        - "Detailed itinerary presentation with multi-leg flight information display"
        - "Trip modification notification system with passenger communication"
        - "Status mapping between booking and trip systems for consistent UI"
        - "Payment status determination and display for trip transactions"
        - "Trip response conversion for optimized UI consumption"

  partially_implemented:
    advanced_optimization:
      description: "Advanced multi-leg route optimization with AI-driven insights"
      current_state:
        - "Basic route calculation with distance and time estimation"
        - "Integration with pricing service for multi-leg cost calculations"
        - "Aircraft positioning awareness for route efficiency"
      missing_components:
        - "Machine learning-based route optimization algorithms"
        - "Dynamic weather-based routing adjustments and contingency planning"
        - "Real-time aircraft repositioning for optimal trip efficiency"
        - "Intelligent layover time management with buffer optimization"
      implementation_priority: "high"
      estimated_effort: "4-6 weeks"

    operator_coordination:
      description: "Multi-operator coordination for complex journey execution"
      current_state:
        - "Basic operator communication through existing booking workflows"
        - "Trip status tracking with booking system integration"
        - "Operator notification for trip-related bookings"
      missing_components:
        - "Real-time multi-operator coordination platform"
        - "Automated operator scheduling for multi-leg journeys"
        - "Operator performance tracking specific to trip coordination"
        - "Conflict resolution system for operator scheduling conflicts"
      implementation_priority: "medium"
      estimated_effort: "3-4 weeks"

    analytics_reporting:
      description: "Comprehensive trip planning analytics and performance insights"
      current_state:
        - "Basic trip creation and completion tracking"
        - "Integration with existing booking analytics system"
        - "Trip status progression monitoring"
      missing_components:
        - "Multi-leg trip performance analytics with optimization insights"
        - "Route efficiency metrics and cost-benefit analysis reporting"
        - "Passenger experience tracking across journey segments"
        - "Operator coordination effectiveness and timing accuracy metrics"
      implementation_priority: "medium"
      estimated_effort: "2-3 weeks"

  planned_future:
    ai_driven_optimization:
      description: "Machine learning-powered trip planning and optimization"
      scope: "Predictive route optimization, demand forecasting, and intelligent rebooking"
      estimated_effort: "8-12 weeks"
      
    integrated_travel_services:
      description: "Complete travel ecosystem integration"
      scope: "Ground transportation, accommodation, and concierge service coordination"
      estimated_effort: "6-10 weeks"
      
    carbon_optimization:
      description: "Environmental impact optimization for charter journeys"
      scope: "Carbon footprint calculation, offset integration, and eco-friendly routing"
      estimated_effort: "4-6 weeks"

implementation_gaps:
  critical_gaps:
    - gap: "Real-time multi-operator coordination platform"
      impact: "Limited ability to coordinate complex multi-leg journeys across operators"
      priority: "high"
      estimated_effort: "4 weeks"
      
    - gap: "Advanced route optimization algorithms"
      impact: "Suboptimal routing leading to higher costs and longer journey times"
      priority: "high"
      estimated_effort: "6 weeks"
      
    - gap: "Intelligent layover management system"
      impact: "Manual buffer time management leading to passenger inconvenience"
      priority: "medium"
      estimated_effort: "3 weeks"

  technical_debt:
    - debt: "Trip service method consolidation"
      description: "Multiple similar methods for trip response conversion need refactoring"
      priority: "low"
      estimated_effort: "1 week"
      
    - debt: "Route calculation caching"
      description: "Frequent route calculations could benefit from intelligent caching"
      priority: "medium"
      estimated_effort: "2 weeks"

behavior:
  trip_planning_lifecycle:
    1: "Multi-destination request processing and initial route analysis"
    2: "Route optimization with aircraft positioning and operator coordination"
    3: "Itinerary assembly with timing coordination and buffer management"
    4: "Quote generation for optimized multi-leg journey with cost breakdown"
    5: "Trip confirmation with operator coordination and passenger communication"
    6: "Real-time journey monitoring with modification capability"
    7: "Post-trip completion with performance analysis and experience tracking"

  journey_orchestration:
    - "Seamless multi-leg coordination with timing optimization"
    - "Dynamic route adjustments based on aircraft availability and weather"
    - "Integrated empty leg opportunities for cost optimization"
    - "Passenger communication throughout journey with proactive updates"
    - "Operator coordination for complex multi-leg trip execution"

  modification_handling:
    - "Dynamic itinerary changes with minimal passenger disruption"
    - "Alternative route calculation for unexpected modifications"
    - "Real-time rebooking with operator coordination and cost impact analysis"
    - "Change history tracking with audit trail and communication log"

invariants:
- "Multi-leg trips must maintain logical timing sequence with adequate transfer buffers"
- "Route optimization must consider aircraft positioning costs and efficiency"
- "Trip modifications must preserve passenger preferences and minimize cost impact"
- "Journey coordination must ensure operator availability for all flight segments"
- "Itinerary changes must maintain audit trail for booking integrity"
- "Multi-destination pricing must reflect route optimization and positioning costs"
- "Trip status transitions must follow logical progression with proper validation"
- "Passenger communication must be synchronized across all journey segments"

forbidden_states:
- "Multi-leg trips with overlapping or conflicting flight times"
- "Route optimization that creates impossible aircraft positioning scenarios"
- "Trip modifications without proper operator confirmation and availability"
- "Itinerary changes without passenger notification and consent"
- "Multi-leg journeys without adequate transfer time buffers"
- "Trip creation without validated airport and route feasibility"
- "Journey execution without proper coordination between multiple operators"
- "Trip completion without performance tracking and experience validation"

primary_flows:
  multi_leg_trip_creation:
    - trigger: "Customer multi-destination journey request"
    - steps: ["Route analysis", "Optimization calculation", "Itinerary assembly", "Quote generation", "Confirmation"]
    - result: "Optimized multi-leg trip with coordinated itinerary"
    
  journey_modification:
    - trigger: "Trip change request or operational necessity"
    - steps: ["Impact analysis", "Alternative calculation", "Operator coordination", "Passenger communication", "Rebooking execution"]
    - result: "Updated trip with minimal disruption and optimized routing"
    
  real_time_coordination:
    - trigger: "Journey in progress monitoring"
    - steps: ["Status tracking", "Timing coordination", "Proactive communication", "Contingency activation"]
    - result: "Seamless journey execution with real-time optimization"

core_principles:
- "Journey efficiency through intelligent routing and aircraft positioning optimization"
- "Passenger experience excellence with seamless multi-leg coordination and communication"
- "Route optimization that balances cost-effectiveness with travel time and convenience"
- "Coordination excellence ensuring smooth transitions between journey segments"
- "Dynamic adaptability enabling real-time trip modifications and rebooking"
- "Performance optimization using data-driven insights for continuous improvement"

depends_on:
- "aircraft_positioning - Real-time aircraft location and availability tracking"
- "operator_communication - Multi-operator coordination for complex journeys"
- "quote_generation - Multi-leg pricing calculation and optimization"
- "booking_lifecycle - Trip conversion to confirmed bookings with payment processing"
- "airport_data - Airport information and operational constraints for route planning"
- "pricing_optimization - Dynamic pricing for multi-leg journeys with route factors"

provides:
- "multi_leg_journey_orchestration - Complete journey planning from origin to final destination"
- "route_optimization_platform - Intelligent routing with cost and efficiency optimization"
- "itinerary_management_system - Comprehensive trip coordination and modification capabilities"
- "journey_performance_analytics - Trip efficiency metrics and optimization insights"
- "passenger_experience_coordination - Seamless communication and journey management"
- "trip_planning_intelligence - AI-driven route optimization and journey recommendations"

integration_points:
  booking_system:
    - "Trip to booking conversion with multi-leg coordination"
    - "Quote integration for complex journey pricing"
    - "Payment processing for multi-segment trips"
    
  aircraft_management:
    - "Aircraft positioning optimization for route efficiency"
    - "Fleet availability coordination for multi-leg journeys"
    - "Empty leg integration for cost optimization"
    
  communication_platform:
    - "Passenger itinerary delivery and journey updates"
    - "Operator coordination for multi-leg trip execution"
    - "Real-time modification notifications and confirmations"

scheduler_integration:
  trip_optimization:
    - task: "Trip Route Optimization"
      schedule: "every 30 minutes"
      purpose: "Optimize existing trip routes based on aircraft positioning and weather updates"
      handler: "trip_optimizer.py:optimize_trip_routes"
      optimization: "Continuous route improvement with positioning cost analysis"
    
    - task: "Journey Coordination Processing"
      schedule: "every 15 minutes"
      purpose: "Coordinate multi-leg journey timing and operator communication"
      handler: "journey_coordinator.py:process_journey_coordination"
      coordination: "Real-time journey synchronization and timing optimization"
    
    - task: "Trip Modification Processing"
      schedule: "every 10 minutes"
      purpose: "Process pending trip modifications and rebooking requests"
      handler: "trip_modifier.py:process_trip_modifications"
      modifications: "Dynamic trip changes with minimal passenger disruption"

monitoring:
  trip_planning_metrics:
    - metric: "multi_leg_coordination_success_rate"
      description: "Percentage of successful multi-leg trip coordination without timing conflicts"
      target: ">99.2%"
      
    - metric: "route_optimization_efficiency"
      description: "Average cost savings achieved through intelligent route optimization"
      target: ">15%"
      
    - metric: "trip_modification_speed"
      description: "Average time to process and confirm trip modifications"
      target: "<30 minutes"
      
    - metric: "passenger_journey_satisfaction"
      description: "Passenger satisfaction score for multi-leg journey coordination"
      target: ">4.7/5"

security:
- "Trip planning data protection with passenger privacy and journey confidentiality"
- "Itinerary access control ensuring only authorized users can view journey details"
- "Route optimization security preventing competitive intelligence exposure"
- "Journey coordination security with encrypted operator communications"
- "Trip modification authorization ensuring only valid users can change bookings"
- "Performance data anonymization for analytics while protecting passenger identity"

error_handling:
  trip_planning_errors:
    - error_type: "InvalidRouteConfiguration"
      description: "Route optimization failed due to impossible aircraft positioning"
      response_code: 400
      recovery: "Provide alternative route suggestions with feasible positioning"
      
    - error_type: "MultiLegTimingConflict"
      description: "Multi-leg journey has timing conflicts or insufficient transfer time"
      response_code: 400
      recovery: "Recalculate timing with adequate buffers and operator coordination"
      
    - error_type: "JourneyModificationFailure"
      description: "Trip modification could not be processed due to operator constraints"
      response_code: 422
      recovery: "Provide alternative modification options with impact analysis"

enforcement_hooks:
- "validate_multi_leg_timing_consistency"
- "ensure_route_optimization_feasibility"
- "confirm_operator_coordination_capacity"
- "verify_trip_modification_authorization"
- "validate_journey_performance_tracking"

endpoints:
  trip_management:
    - path: "/api/v1/bookings/trips/"
      methods: ["GET", "POST"]
      description: "Create trip and list user trips with filtering"
      response_time: "<1000ms"
      handler: "trips.py:create_trip, get_user_trips"
    
    - path: "/api/v1/bookings/trips/{trip_id}"
      methods: ["GET", "PUT", "DELETE"]
      description: "Individual trip operations with detailed itinerary management"
      response_time: "<500ms"
      handler: "trips.py:get_trip, update_trip, delete_trip"
    
    - path: "/api/v1/bookings/trips/user/{user_id}"
      methods: ["GET"]
      description: "User trip history with status filtering and pagination"
      response_time: "<800ms"
      handler: "trips.py:get_user_trips_by_id"

database_models:
  - model: "Trip"
    purpose: "Core trip entity with multi-leg journey support and airport relationships"
    fields: [
      "id", "user_id", "origin_airport_id", "destination_airport_id", 
      "departure_date", "return_date", "trip_type", "requirements",
      "status", "created_at", "updated_at"
    ]
    indexes: ["user_id", "status", "departure_date", "origin_airport_id", "destination_airport_id"]
    relationships: ["user", "origin_airport", "destination_airport", "bookings", "quotes"]

services:
  - service: "TripsService"
    path: "app/services/trips_service.py"
    purpose: "Core trip planning service with multi-leg journey orchestration"
    methods: [
      "get_trips_for_user", "create_trip", "_booking_to_trip_response",
      "_convert_trip_to_ui_response", "_map_booking_status_to_trip_status",
      "_determine_payment_status"
    ]
    dependencies: ["db_manager", "aircraft_service", "pricing_service"]

  - service: "FlightRouteEstimator"
    path: "app/utils/routing.py"
    purpose: "Advanced route calculation and flight time estimation"
    methods: [
      "calculate_distance_nm_by_icao", "estimate_flight_time",
      "calculate_distance_nm", "get_airport_coordinates"
    ]
    dependencies: ["airport_data"]

repositories:
  - repository: "TripRepository"
    path: "app/db/manager/repositories/trip_repository.py"
    purpose: "Trip data access layer with multi-leg journey support"
    methods: [
      "get_trip_by_id", "get_user_trips", "create_trip",
      "update_trip", "delete_trip", "get_trips_by_status"
    ]
    validation: ["trip_ownership", "journey_timing", "route_feasibility"]

performance:
  response_time_targets:
    - "Trip creation: <1000ms (95th percentile)"
    - "Trip retrieval: <500ms (95th percentile)"
    - "Route optimization: <2000ms (95th percentile)"
    - "Trip modification: <800ms (95th percentile)"

  throughput_targets:
    - "Support 500+ concurrent trip planning operations"
    - "Handle 2000+ trip status queries per minute"
    - "Process 100+ route optimizations per hour"
    - "Manage 50+ simultaneous multi-leg journey modifications"

  availability_targets:
    - "99.8% uptime for trip planning API endpoints"
    - "99.9% uptime for route optimization services"
    - "Real-time trip status synchronization"
    - "Automated failover for critical trip operations"

testing:
  integration_testing:
    - "End-to-end multi-leg trip planning workflow validation"
    - "Route optimization algorithm accuracy testing"
    - "Trip modification and rebooking process verification"
    - "Operator coordination and communication testing"

  performance_testing:
    - "Load testing for concurrent trip planning operations"
    - "Route optimization performance under complex scenarios"
    - "Database performance with large trip datasets"
    - "API response time validation for trip operations"

  security_testing:
    - "Trip data privacy and access control validation"
    - "Itinerary confidentiality and secure transmission"
    - "User authorization for trip modifications"
    - "Journey data integrity and audit trail testing"

consolidation_notes:
  trip_planning_ownership:
    - "Trip planning subdomain owns multi-leg journey orchestration and itinerary management"
    - "Integration with booking domain for trip-to-booking conversion workflows"
    - "Coordination with aircraft domain for positioning optimization and availability"
    - "Integration with communication domain for passenger and operator coordination"

  service_integration:
    - "Primary trips service with database manager pattern for data access"
    - "Route optimization utility for intelligent journey planning"
    - "Integration with pricing service for multi-leg cost calculations"
    - "Coordination with aircraft positioning for route efficiency optimization"

files:
  - "app/api/v1/endpoints/bookings/trips.py - Primary trip planning API endpoints"
  - "app/services/trips_service.py - Core trip planning business logic"
  - "app/utils/routing.py - Route optimization and flight time estimation"
  - "app/db/models/trips.py - Trip data models"
  - "app/schemas/trip.py - Trip API schemas and validation"
  - "app/templates/passenger/itinerary.html - Itinerary presentation template"
  - "app/templates/passenger/flight_update.html - Trip modification notifications" 