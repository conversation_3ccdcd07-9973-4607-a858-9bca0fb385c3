system: villiers_shared_flights
description: "Villiers.ai Shared Flights Subdomain - Charter Aviation Cost-Sharing Platform with Multi-Passenger Flight Matching, Dynamic Pricing Optimization, and Collaborative Booking Management. This subdomain enables multiple passengers to share charter flights, reducing individual costs while maintaining charter aviation's convenience and flexibility through intelligent passenger matching, cost distribution, and flight coordination."

intent_assertions:
- "Complete shared flight lifecycle from empty leg conversion to multi-passenger booking coordination"
- "Cost-sharing optimization enabling charter aviation accessibility through passenger matching"
- "Dynamic pricing algorithms with demand-based optimization and profitability analysis" 
- "Intelligent passenger matching with preference compatibility and flight coordination"
- "Real-time seat availability management with booking confirmation thresholds"
- "Automated flight confirmation based on minimum seat fill requirements and profitability"
- "Comprehensive cancellation handling with seat redistribution and flight status management"
- "Revenue optimization through dynamic pricing and demand-based seat pricing adjustments"

technical_assertions:
- path: "app/db/models/shared_flight.py"
  purpose: "Complete shared flight data models with seat management and passenger coordination"
  lines: 93
  models: ["SharedFlight", "SharedSeat"]
  
- path: "app/services/flight_sharing_service.py"
  purpose: "Core flight sharing service with booking coordination and status management"
  lines: 410
  methods: ["create_shared_flight", "list_available_shared_flights", "book_seats", "cancel_seat_booking"]
  
- path: "app/services/legacy_services/flight_sharing.py"
  purpose: "Legacy flight sharing implementation with profitability optimization"
  lines: 805
  methods: ["create_shared_flight", "book_shared_flight_seats", "add_passenger_to_shared_flight"]
  
- path: "app/api/v1/endpoints/bookings/flight_sharing.py"
  purpose: "Shared flight API endpoints with search, booking, and management workflows"
  lines: 390
  endpoints: ["/shared-flights", "/shared-flights/{id}", "/shared-flights/empty-leg/{id}", "/shared-flights/{id}/book"]
  
- path: "app/db/manager/repositories/shared_flight_repository.py"
  purpose: "Shared flight data access with complex seat management and profitability analysis"
  lines: 1026
  operations: ["create_shared_flight_from_empty_leg", "book_shared_flight_seats", "validate_flight_profitability"]
  
- path: "app/schemas/flight_sharing.py"
  purpose: "Comprehensive shared flight API schemas with detailed validation and response models"
  lines: 450
  schemas: ["SharedFlightListResponse", "BookSharedFlightRequest", "CreateSharedFlightResponse"]
  
- path: "app/services/ticket_management_service.py"
  purpose: "Flight ticket management with profitability validation and pricing optimization"
  lines: 400
  methods: ["validate_flight_profitability", "optimize_flight_pricing", "generate_ticket"]

database_models:
- model: "SharedFlight"
  purpose: "Primary shared flight entity with comprehensive flight, pricing, and coordination data"
  fields: [
    "id", "empty_leg_id", "aircraft_id", "from_airport_id", "to_airport_id", 
    "from_airport_code", "to_airport_code", "departure_time", "arrival_time",
    "total_seats", "min_seats_required", "per_seat_price", "status", "is_confirmed",
    "confirmed_at", "distance_nm", "flight_time_hours", "flight_metadata", "original_booking_id"
  ]
  indexes: ["empty_leg_id", "aircraft_id", "departure_time", "status", "is_confirmed"]
  relationships: [
    "empty_leg", "aircraft", "seats", "from_airport", "to_airport", "original_booking"
  ]

- model: "SharedSeat"
  purpose: "Individual seat entity with passenger assignment and booking coordination"
  fields: [
    "id", "shared_flight_id", "booking_id", "user_id", "seat_number", 
    "status", "price", "passenger_details"
  ]
  tracking: "Individual seat status, passenger assignment, and booking association"
  relationships: ["shared_flight", "booking", "user"]

services:
- service: "FlightSharingService"
  path: "app/services/flight_sharing_service.py"
  purpose: "Core shared flight management with booking coordination and status tracking"
  methods: [
    "create_shared_flight", "list_available_shared_flights", "book_seats", 
    "cancel_seat_booking", "get_shared_flight_status", "add_passenger_to_shared_flight"
  ]
  integration: "Empty leg conversion, seat management, passenger coordination"
  
- service: "TicketManagementService"
  path: "app/services/ticket_management_service.py"
  purpose: "Flight profitability validation and dynamic pricing optimization"
  methods: [
    "validate_flight_profitability", "optimize_flight_pricing", "generate_ticket"
  ]
  optimization: "Revenue maximization through demand-based pricing and seat fill analysis"

repositories:
- repository: "SharedFlightRepository"
  purpose: "Shared flight data access with complex seat management and analytics"
  methods: [
    "get_shared_flight_by_id", "create_shared_flight_from_empty_leg", "list_available_shared_flights",
    "book_shared_flight_seats", "cancel_seat_booking", "get_shared_flight_status",
    "add_passenger_to_shared_flight", "create_shared_flight_from_booking", "validate_flight_profitability"
  ]
  analytics: "Profitability analysis, seat utilization tracking, revenue optimization"

behavior:
  cost_sharing_workflow:
    empty_leg_conversion:
      - "Convert available empty legs into shared flights with calculated per-seat pricing"
      - "Apply 20% markup over base charter cost divided by available seats"
      - "Set minimum seat requirements (typically 50% capacity) for flight confirmation"
      - "Create individual seat records with availability status and pricing"
    
    pricing_calculation:
      - "Multi-factor pricing based on empty leg cost, aircraft category, and route distance"
      - "Distance-based fallback pricing using category-specific rates per nautical mile"
      - "Dynamic pricing optimization based on demand, time to departure, and seat fill rate"
      - "Round pricing to nearest $50 for clean customer presentation"
    
    passenger_matching:
      - "Intelligent seat allocation based on booking order and passenger requirements"
      - "Passenger detail collection for regulatory compliance and flight coordination"
      - "Preference matching for compatible passenger grouping when possible"
      - "Real-time seat availability updates with booking deadline enforcement"

  booking_coordination:
    seat_reservation:
      - "Individual seat booking with quote generation and platform fee calculation"
      - "Automatic seat assignment with booking confirmation and passenger association"
      - "Platform fee calculation at 10% of total booking value"
      - "24-hour quote validity with booking deadline enforcement (6 hours before departure)"
    
    flight_confirmation:
      - "Automatic flight confirmation when minimum seat threshold is reached"
      - "Profitability validation before flight confirmation to ensure cost coverage"
      - "Dynamic pricing optimization triggered upon confirmation for revenue maximization"
      - "Passenger notification system for flight status changes and confirmations"
    
    cancellation_management:
      - "Seat release back to available inventory with immediate rebooking capability"
      - "Flight status recalculation if cancellation drops below minimum seat requirement"
      - "Cancellation policy enforcement (no refunds within 48 hours of departure)"
      - "Automatic passenger notifications for flight status changes due to cancellations"

  profitability_optimization:
    revenue_analysis:
      - "Real-time profitability calculation comparing total revenue to flight costs"
      - "Minimum profit margin enforcement (15%) before flight confirmation"
      - "Dynamic seat pricing based on demand patterns and booking velocity"
      - "Revenue optimization through time-based and demand-based pricing adjustments"
    
    pricing_optimization:
      - "Time-sensitive pricing with increased rates as departure approaches"
      - "Demand-based pricing with premium rates for high seat fill percentages"
      - "Seat fill rate analysis with pricing adjustments to optimize revenue"
      - "Cost-per-seat calculation ensuring minimum profitability thresholds"

primary_flows:
  shared_flight_creation_flow:
    steps:
      - "Empty Leg Selection: Identify available empty legs suitable for shared flight conversion"
      - "Aircraft Analysis: Validate aircraft capacity and calculate available seats for sharing"
      - "Pricing Calculation: Calculate per-seat pricing with markup and cost optimization"
      - "Seat Creation: Generate individual seat records with availability status"
      - "Flight Publication: Make shared flight available for passenger booking"
    
    automation:
      - "Automated empty leg monitoring for shared flight conversion opportunities"
      - "Dynamic pricing calculation based on multiple cost and demand factors"
      - "Real-time seat inventory management with availability tracking"

  passenger_booking_flow:
    steps:
      - "Flight Search: Customer searches available shared flights by route and date"
      - "Seat Selection: Customer selects desired number of seats with pricing display"
      - "Passenger Details: Collection of passenger information for regulatory compliance"
      - "Quote Generation: Create quote with platform fee and booking terms"
      - "Booking Confirmation: Process payment and assign seats to customer"
      - "Flight Status Update: Update flight confirmation status based on seat fill"
    
    validation:
      - "Seat availability validation before booking confirmation"
      - "Booking deadline enforcement preventing last-minute bookings"
      - "Passenger detail validation for regulatory compliance"
      - "Payment processing integration with platform fee collection"

  profitability_validation_flow:
    steps:
      - "Revenue Calculation: Sum all confirmed seat bookings and platform fees"
      - "Cost Analysis: Compare total revenue to original flight cost and operational expenses"
      - "Profit Margin Validation: Ensure minimum 15% profit margin before confirmation"
      - "Flight Confirmation: Confirm flight when profitability and seat thresholds are met"
      - "Pricing Optimization: Apply dynamic pricing adjustments for revenue maximization"
    
    optimization:
      - "Real-time profitability monitoring with automatic status updates"
      - "Dynamic pricing adjustments based on demand and time factors"
      - "Revenue maximization through intelligent seat pricing strategies"

invariants:
- "Shared flights must maintain minimum seat fill requirements for confirmation"
- "Per-seat pricing must ensure flight profitability with minimum 15% margin"
- "Seat assignments must be unique and properly associated with bookings"
- "Flight confirmation must validate both seat count and profitability thresholds"
- "Booking deadlines must be enforced (6 hours before departure)"
- "Cancellation policies must be consistently applied across all bookings"
- "Platform fee calculations must be accurate and auditable"
- "Passenger details must be collected for regulatory compliance"
- "Seat availability must be updated in real-time across all booking channels"
- "Flight status changes must trigger appropriate passenger notifications"

forbidden_states:
- "Shared flights created without proper profitability validation"
- "Seat bookings accepted after booking deadline"
- "Flight confirmation without meeting minimum seat or profitability requirements"
- "Duplicate seat assignments for the same flight"
- "Booking cancellations processed without proper seat release"
- "Pricing calculations without cost coverage validation"
- "Passenger bookings without required regulatory information"
- "Flight status changes without passenger notification"
- "Seat availability displayed without real-time inventory validation"
- "Platform fee calculations without proper audit trail"

depends_on:
- "empty_legs - Empty leg inventory and conversion opportunities"
- "aircraft - Aircraft capacity, specifications, and availability data"
- "booking - Booking creation, payment processing, and lifecycle management"
- "quotes - Quote generation, pricing calculation, and validation workflows"
- "authentication - User authentication and authorization for booking access"
- "payment - Payment processing, platform fees, and financial transaction management"
- "communication - Passenger notifications, flight updates, and coordination messaging"

provides:
- "cost_sharing_platform - Multi-passenger charter cost distribution and coordination"
- "passenger_matching_system - Intelligent passenger grouping and compatibility management"
- "dynamic_pricing_engine - Demand-based pricing optimization and revenue maximization"
- "seat_inventory_management - Real-time seat availability and booking coordination"
- "profitability_validation_service - Flight cost coverage and profit margin enforcement"
- "flight_confirmation_automation - Automated flight status management based on thresholds"
- "shared_flight_marketplace - Customer-facing shared flight search and booking platform"

enforcement_hooks:
- "validate_shared_flight_profitability"
- "ensure_minimum_seat_requirements"
- "confirm_booking_deadline_compliance"
- "verify_seat_assignment_uniqueness"
- "validate_passenger_detail_completeness"

endpoints:
  shared_flight_search:
    - path: "/api/v1/bookings/shared-flights"
      methods: ["GET"]
      description: "Search and list available shared flights with filtering and pagination"
      response_time: "<1000ms"
      handler: "flight_sharing.py:list_shared_flights"
      access: "Public access with optional user authentication"
    
    - path: "/api/v1/bookings/shared-flights/{shared_flight_id}"
      methods: ["GET"]
      description: "Get detailed shared flight information including real-time seat availability"
      response_time: "<500ms"
      handler: "flight_sharing.py:get_shared_flight"
      access: "Public access for flight details"

  shared_flight_management:
    - path: "/api/v1/bookings/shared-flights/empty-leg/{empty_leg_id}"
      methods: ["POST"]
      description: "Create shared flight from empty leg with admin authorization"
      response_time: "<2000ms"
      handler: "flight_sharing.py:create_shared_flight_from_empty_leg"
      access: "Admin-only for shared flight creation"
    
    - path: "/api/v1/bookings/shared-flights/{shared_flight_id}/book"
      methods: ["POST"]
      description: "Book seats on shared flight with passenger details and payment processing"
      response_time: "<3000ms"
      handler: "flight_sharing.py:book_shared_flight_seats"
      access: "User authentication required for booking"

  booking_management:
    - path: "/api/v1/bookings/bookings/{booking_id}/cancel"
      methods: ["POST"]
      description: "Cancel shared flight booking with seat release and status updates"
      response_time: "<1000ms"
      handler: "flight_sharing.py:cancel_shared_flight_booking"
      access: "User can only cancel own bookings"

error_handling:
  shared_flight_errors:
    - error_type: "InsufficientSeatsError"
      description: "Requested number of seats not available on shared flight"
      response_code: 409
      recovery: "Display available seat count and suggest alternative booking"
    
    - error_type: "BookingDeadlineExceededError"
      description: "Booking attempted within 6 hours of departure"
      response_code: 410
      recovery: "Inform customer of booking deadline and suggest alternative flights"
    
    - error_type: "FlightNotProfitableError"
      description: "Flight cannot be confirmed due to insufficient profitability"
      response_code: 422
      recovery: "Continue accepting bookings until profitability threshold is met"
    
    - error_type: "SharedFlightCreationError"
      description: "Failed to create shared flight from empty leg"
      response_code: 422
      recovery: "Validate empty leg availability and aircraft capacity"

monitoring:
  shared_flight_metrics:
    - metric: "shared_flight_fill_rate"
      description: "Average percentage of seats filled per shared flight"
      target: ">60%"
    
    - metric: "shared_flight_confirmation_rate"
      description: "Percentage of shared flights that reach confirmation threshold"
      target: ">70%"
    
    - metric: "average_booking_response_time"
      description: "Average time from seat selection to booking confirmation"
      target: "<30 seconds"
    
    - metric: "shared_flight_profitability_margin"
      description: "Average profit margin for confirmed shared flights"
      target: ">20%"
    
    - metric: "passenger_satisfaction_score"
      description: "Customer satisfaction rating for shared flight experience"
      target: ">4.2/5.0"

testing:
  shared_flight_testing:
    - test_type: "Unit Tests"
      coverage: "Shared flight creation, seat booking, pricing calculation, and profitability validation"
      location: "tests/unit/services/test_flight_sharing_service.py"
    
    - test_type: "Integration Tests"
      coverage: "Complete shared flight workflow from creation to booking confirmation"
      location: "tests/integration/endpoints/bookings/test_flight_sharing_endpoints.py"
    
    - test_type: "Validation Tests"
      coverage: "API response schema validation and business rule enforcement"
      location: "tests/integration/endpoints/bookings/test_flight_sharing_validation.py"

security:
  shared_flight_security:
    - "Shared flight creation restricted to admin users with proper authorization"
    - "Passenger booking restricted to authenticated users with booking ownership validation"
    - "Seat assignment uniqueness enforced to prevent double-booking"
    - "Payment processing integration with secure platform fee collection"
    - "Passenger personal information protected with privacy compliance"

performance:
  shared_flight_performance_targets:
    - "Shared flight search: <1000ms for filtered results with pagination"
    - "Seat booking: <3000ms including payment processing and confirmation"
    - "Flight status updates: <500ms for real-time availability changes"
    - "Profitability calculation: <200ms for real-time validation"
    - "Pricing optimization: <1000ms for dynamic pricing adjustments"

consolidation_notes:
  shared_flight_architecture:
    - "Complete shared flight lifecycle managed through FlightSharingService"
    - "Profitability validation centralized in TicketManagementService"
    - "Seat inventory management handled by SharedFlightRepository"
    - "Dynamic pricing optimization integrated with booking confirmation workflow"
    - "Passenger coordination unified across booking and communication systems"

implementation_gaps:
  advanced_passenger_matching:
    status: "enhancement_opportunity"
    description: "AI-powered passenger compatibility matching based on preferences and travel patterns"
    current_state: "Basic seat assignment based on booking order"
    enhancement: "Intelligent passenger grouping with preference analysis and compatibility scoring"
    priority: "medium"
    estimated_effort: "6-8 weeks"
  
  dynamic_pricing_optimization:
    status: "partially_implemented"
    description: "Real-time pricing adjustments based on market demand and booking velocity"
    current_state: "Basic demand-based pricing with time factors"
    enhancement: "Machine learning-powered pricing with market analysis and competitive intelligence"
    priority: "high"
    estimated_effort: "8-10 weeks"
  
  group_booking_coordination:
    status: "planned_future"
    description: "Coordinated group bookings with split payment and passenger management"
    current_state: "Individual seat bookings only"
    enhancement: "Group booking workflows with coordinator roles and split payment options"
    priority: "low"
    estimated_effort: "4-6 weeks"

files:
- "app/db/models/shared_flight.py - Shared flight and seat data models"
- "app/services/flight_sharing_service.py - Core shared flight service implementation"
- "app/services/legacy_services/flight_sharing.py - Legacy implementation with profitability features"
- "app/api/v1/endpoints/bookings/flight_sharing.py - Shared flight API endpoints"
- "app/db/manager/repositories/shared_flight_repository.py - Shared flight data access layer"
- "app/schemas/flight_sharing.py - API request/response schemas with validation"
- "app/services/ticket_management_service.py - Profitability validation and pricing optimization"
- "app/db/schemas/shared_flight.py - Database schema definitions"
- "tests/unit/services/test_flight_sharing_service.py - Unit test coverage"
- "tests/integration/endpoints/bookings/test_flight_sharing_endpoints.py - Integration tests"
- "tests/integration/endpoints/bookings/test_flight_sharing_validation.py - Schema validation tests"
- "tests/unit/repositories/test_shared_flight_repository.py - Repository test coverage" 