system: villiers_analytics_admin_dashboards
description: "Admin Dashboard Analytics - Comprehensive business intelligence dashboards for administrative oversight and decision making including executive dashboards, operational metrics, financial reporting, and system health monitoring"

intent_assertions:
  - "Consolidated administrative dashboards with key business metrics"
  - "Real-time operational oversight and system health monitoring"
  - "Financial performance tracking and revenue analytics"
  - "User engagement and growth metrics visualization"
  - "System performance and reliability monitoring"
  - "Customizable time ranges and metric breakdowns"

technical_assertions:
  dashboard_endpoints:
    - path: "/api/v1/admin-ui/dashboard"
      method: "GET"
      purpose: "Consolidated admin dashboard data"
      response: "AdminDashboardResponse"
    - path: "/api/v1/admin-ui/analytics"
      method: "GET"
      purpose: "Comprehensive analytics data for admin dashboards"
      response: "AdminAnalyticsResponse"

  service_integration:
    - path: "app/api/v1/endpoints/admin/admin_ui.py"
      methods: ["get_admin_dashboard", "get_admin_analytics"]
      purpose: "Admin dashboard endpoints with consolidated data"
    - path: "app/services/analytics_service.py"
      methods: ["get_analytics_data", "_get_user_metrics", "_get_revenue_metrics", "_get_performance_metrics", "_get_business_metrics"]
      purpose: "Analytics data aggregation for dashboards"

  schema_definitions:
    - path: "app/schemas/admin_ui.py"
      schemas: ["AdminDashboardResponse", "AdminAnalyticsResponse", "UserMetrics", "RevenueMetrics", "PerformanceMetrics", "BusinessMetrics"]
      purpose: "Structured dashboard data models"

behavior:
  dashboard_loading_workflow:
    1: "Admin user accesses dashboard endpoint"
    2: "Multiple service queries executed in parallel"
    3: "User metrics aggregated (active users, growth, engagement)"
    4: "Revenue metrics calculated (MTD revenue, booking values, conversion)"
    5: "Performance metrics gathered (API response times, system health)"
    6: "Business metrics computed (CAC, LTV, retention rates)"
    7: "Data consolidated and formatted for UI consumption"
    8: "Response cached for performance optimization"

  analytics_data_flow:
    1: "Real-time metrics collected from operational systems"
    2: "Historical data aggregated by configurable time ranges"
    3: "Multi-dimensional analysis with breakdown options"
    4: "Currency conversion for financial metrics"
    5: "Data quality validation and completeness checks"

dashboard_capabilities:
  executive_overview:
    - "Key performance indicators at-a-glance"
    - "Revenue trends and financial health"
    - "User growth and engagement metrics"
    - "System health and operational status"

  operational_metrics:
    - "Active user counts and engagement trends"
    - "Pending quotes and bookings requiring attention"
    - "System performance and error rates"
    - "Recent alerts and critical notifications"

  financial_analytics:
    - "Month-to-date and period revenue reporting"
    - "Revenue breakdown by service type and operator"
    - "Conversion rates and booking value analysis"
    - "Currency-aware financial reporting"

  user_analytics:
    - "User acquisition and retention metrics"
    - "Geographic distribution and country breakdown"
    - "Session duration and engagement analysis"
    - "User growth rates and cohort analysis"

key_metrics:
  business_health:
    - "Active users today/this week/this month"
    - "Revenue MTD with growth percentage"
    - "Pending quotes and bookings count"
    - "System uptime and availability"

  user_engagement:
    - "Daily/Weekly/Monthly Active Users"
    - "New user registrations and growth rates"
    - "Average session duration"
    - "User retention and return rates"

  financial_performance:
    - "Total revenue by time period"
    - "Average booking value and trends"
    - "Revenue growth percentage"
    - "Conversion rates through booking funnel"

  system_performance:
    - "API response times (P95)"
    - "Error rates and system availability"
    - "Database performance metrics"
    - "Recent errors and system alerts"

customization_features:
  time_range_options:
    - "Today, Yesterday, Last 24 Hours"
    - "Last 7 Days, Last 30 Days"
    - "This Month, Last Month"
    - "This Year, Custom Date Range"

  metric_filtering:
    - "Specific metric groups (users, revenue, performance, business)"
    - "Geographic breakdown by country"
    - "Service type breakdown"
    - "Time-based breakdown (daily, weekly, monthly)"

  export_capabilities:
    - "Dashboard data export to CSV/Excel"
    - "Scheduled report generation"
    - "Email report delivery"
    - "API data access for third-party tools"

performance_requirements:
  response_times:
    - "Dashboard endpoint < 2000ms"
    - "Analytics endpoint < 5000ms"
    - "Real-time metrics update < 30 seconds"

  concurrent_access:
    - "Support 100+ concurrent admin users"
    - "Dashboard auto-refresh without performance impact"
    - "Efficient caching for repeated queries"

  data_freshness:
    - "Real-time data for current day metrics"
    - "Hourly aggregation for recent trends"
    - "Daily aggregation for historical analysis"

security_considerations:
  access_control:
    - "Admin-level authentication required"
    - "Role-based dashboard access control"
    - "Audit logging for dashboard access"

  data_protection:
    - "Sensitive financial data encryption"
    - "PII anonymization in analytics views"
    - "Secure data transmission over HTTPS"

integration_points:
  - "User service for authentication and preferences"
  - "Booking service for revenue and conversion metrics"
  - "Quote service for pending quote tracking"
  - "Health service for system status monitoring"
  - "Analytics service for comprehensive metrics"
  - "Scheduler service for automated report generation"

invariants:
- "Dashboard data must be accurate and consistent across all views"
- "Real-time metrics must be updated within specified timeframes"
- "Admin authentication must be verified before dashboard access"
- "Financial data must be properly formatted and currency-aware"

forbidden_states:
- "Dashboard displaying stale or incorrect data"
- "Unauthorized access to admin dashboard functionality"
- "Performance degradation due to inefficient dashboard queries"
- "Financial data exposure without proper authorization"

depends_on:
- authentication
- booking
- communication
- database
- api

provides:
- executive_dashboard_insights
- operational_metrics_visualization
- financial_performance_reporting
- user_analytics_dashboards

files:
- app/api/v1/endpoints/admin/admin_ui.py
- app/services/analytics_service.py
- app/schemas/admin_ui.py 