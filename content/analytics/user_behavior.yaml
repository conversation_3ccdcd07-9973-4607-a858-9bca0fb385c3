system: villiers_analytics_user_behavior
description: "User Behavior Analytics - Track and analyze user interactions, sessions, and behavioral patterns for UX optimization including user activity tracking, session analysis, feature usage patterns, and behavioral insights"

intent_assertions:
  - "Comprehensive user journey tracking across all platform touchpoints"
  - "Session-based analytics with anonymous and authenticated user support"
  - "Feature usage analysis for product optimization"
  - "User engagement metrics and retention analysis"
  - "Page view tracking with performance correlation"
  - "Conversion funnel analysis from user behavior perspective"

technical_assertions:
  tracking_infrastructure:
    - path: "app/services/analytics_service.py"
      methods: ["track_user_activity", "track_page_view", "track_feature_usage", "track_booking_funnel"]
      purpose: "Core user behavior tracking methods"
    - path: "app/db/models/analytics.py"
      model: "UserActivity"
      purpose: "User interaction storage with metadata and session context"
    - path: "app/db/manager/repositories/user_activity_repository.py"
      methods: ["create_user_activity", "get_login_history", "get_active_users", "get_page_views"]
      purpose: "User behavior data access and analysis"

  api_endpoints:
    - path: "/api/v1/analytics/track"
      method: "POST"
      purpose: "Generic user activity tracking endpoint"
    - path: "/api/v1/analytics/track/page-view"
      method: "POST"
      purpose: "Dedicated page view tracking endpoint"

behavior:
  user_session_tracking:
    1: "User interaction triggers client-side tracking"
    2: "Session ID and user context captured"
    3: "Activity metadata enriched with device and location info"
    4: "Event stored with proper anonymization for privacy"
    5: "Real-time aggregation updates user engagement metrics"

  behavioral_analysis:
    1: "User activities aggregated by session and time periods"
    2: "Feature usage patterns identified and analyzed"
    3: "User journey paths mapped for optimization insights"
    4: "Engagement metrics calculated for retention analysis"

capabilities:
  - "Anonymous and authenticated user tracking"
  - "Session-based activity correlation"
  - "Feature usage heatmaps and analytics"
  - "User journey visualization and analysis"
  - "Engagement metrics and retention rates"
  - "A/B testing support through user segmentation"

key_metrics:
  - "Daily/Weekly/Monthly Active Users (DAU/WAU/MAU)"
  - "Session duration and page views per session"
  - "Feature adoption rates and usage frequency"
  - "User retention and churn analysis"
  - "Conversion rates by user segments"
  - "Time-to-value for new user onboarding"

privacy_compliance:
  - "User consent verification before tracking"
  - "Anonymous user support without PII collection"
  - "Data retention policies aligned with GDPR/CCPA"
  - "User opt-out mechanisms and right to deletion"
  - "IP address anonymization and geographic aggregation"

invariants:
- "User activity must be trackable across sessions while respecting privacy"
- "Anonymous users must be supported without compromising functionality"
- "Session data must maintain integrity and temporal ordering"
- "Feature usage tracking must not impact application performance"

forbidden_states:
- "Tracking personal data without explicit user consent"
- "Session data corruption or loss during transitions"
- "Performance degradation due to excessive tracking"
- "Privacy violations through data correlation or inference"

depends_on:
- authentication
- database
- api

provides:
- user_journey_analytics
- behavioral_insights
- engagement_metrics
- retention_analysis

files:
- app/services/analytics_service.py
- app/db/models/analytics.py
- app/db/manager/repositories/user_activity_repository.py
- app/api/v1/endpoints/analytics/analytics.py 