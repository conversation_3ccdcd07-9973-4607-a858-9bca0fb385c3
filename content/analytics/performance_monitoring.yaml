system: villiers_analytics_performance_monitoring
description: "Performance Monitoring Analytics - Monitor and analyze system performance, API metrics, and operational health for optimization including system metrics collection, API performance tracking, database monitoring, and alerting"

intent_assertions:
  - "Real-time system performance monitoring and alerting"
  - "API endpoint performance tracking and optimization"
  - "Database query performance analysis and tuning"
  - "Quote accuracy monitoring for operator performance"
  - "Resource utilization tracking and capacity planning"
  - "Error rate monitoring and system health dashboards"

technical_assertions:
  metrics_collection:
    - path: "app/services/analytics_service.py"
      methods: ["track_system_metric", "track_api_latency", "track_database_operation", "track_quote_estimate_accuracy"]
      purpose: "System performance metrics collection"
    - path: "app/db/models/analytics.py"
      model: "SystemMetric"
      purpose: "Performance metrics storage with dimensions and categories"
    - path: "app/db/manager/repositories/system_metric_repository.py"
      methods: ["create_system_metric", "get_quote_accuracy_metrics", "get_api_performance_metrics"]
      purpose: "Performance metrics data access and analysis"

  scheduler_integration:
    - path: "app/core/scheduler_metrics.py"
      component: "SchedulerMonitor"
      purpose: "Scheduler performance tracking and job execution metrics"
    - path: "app/services/scheduler_service.py"
      methods: ["get_health_status", "get_performance_metrics"]
      purpose: "Scheduler health monitoring and reporting"

  monitoring_infrastructure:
    - path: "app/templates/system/performance_alert.html"
      purpose: "Performance alert email templates with metrics visualization"
    - path: "app/templates/system/error_alert.html"
      purpose: "Error alert templates with system context"

behavior:
  metrics_collection_workflow:
    1: "System components emit performance metrics during operation"
    2: "Metrics captured with relevant dimensions and categories"
    3: "Real-time aggregation and threshold monitoring"
    4: "Alert triggering for performance degradation"
    5: "Historical trending and capacity planning analysis"

  performance_analysis:
    1: "API response times tracked per endpoint and method"
    2: "Database query performance monitored with slow query detection"
    3: "Resource utilization trends analyzed for scaling decisions"
    4: "Error rates correlated with system load and performance"

capabilities:
  system_monitoring:
    - "API response time tracking with percentile analysis"
    - "Database query performance monitoring"
    - "Memory and CPU utilization tracking"
    - "Error rate monitoring with context"
    - "Scheduler job execution metrics"

  performance_optimization:
    - "Slow query identification and optimization recommendations"
    - "API endpoint performance comparison and optimization"
    - "Resource bottleneck identification"
    - "Performance trend analysis for capacity planning"

  alerting_system:
    - "Real-time threshold-based alerting"
    - "Performance degradation detection"
    - "System health status reporting"
    - "Email and webhook notification integration"

key_metrics:
  api_performance:
    - "Response time percentiles (P50, P95, P99)"
    - "Throughput (requests per minute)"
    - "Error rates by endpoint and status code"
    - "Availability and uptime percentage"

  database_performance:
    - "Query execution time and slow query detection"
    - "Connection pool utilization"
    - "Cache hit ratios and memory usage"
    - "Lock wait times and deadlock detection"

  system_health:
    - "CPU and memory utilization trends"
    - "Disk usage and I/O performance"
    - "Network latency and throughput"
    - "Service availability and response times"

  business_metrics:
    - "Quote accuracy by operator and route"
    - "Booking conversion rates and funnel performance"
    - "Revenue per transaction and time period"
    - "Customer acquisition cost and lifetime value"

monitoring_thresholds:
  performance_alerts:
    - "API response time > 500ms (P95)"
    - "Database query time > 1000ms"
    - "Error rate > 1% over 5-minute window"
    - "Memory usage > 80% sustained"
    - "CPU usage > 85% sustained"

  critical_alerts:
    - "API response time > 2000ms (P95)"
    - "Database query time > 5000ms"
    - "Error rate > 5% over 2-minute window"
    - "Memory usage > 95%"
    - "Service unavailable for > 1 minute"

integration_points:
  - "Scheduler service for automated metrics collection"
  - "Email service for alert notifications"
  - "Dashboard service for real-time visualization"
  - "Logging service for metric correlation"
  - "External monitoring tools (Datadog, New Relic)"

invariants:
- "Performance metrics must be collected without impacting system performance"
- "Alert thresholds must be validated and tuned to minimize false positives"
- "Metrics data must maintain temporal consistency and ordering"
- "System health checks must complete within specified timeouts"

forbidden_states:
- "Performance monitoring causing performance degradation"
- "Alert fatigue due to excessive false positive alerts"
- "Metrics collection failures going undetected"
- "Critical system issues not triggering appropriate alerts"

depends_on:
- database
- api
- communication
- infrastructure

provides:
- system_performance_metrics
- operational_health_monitoring
- performance_optimization_insights
- automated_alerting_system

files:
- app/services/analytics_service.py
- app/db/models/analytics.py
- app/db/manager/repositories/system_metric_repository.py
- app/core/scheduler_metrics.py
- app/services/scheduler_service.py
- app/templates/system/performance_alert.html
- app/templates/system/error_alert.html 