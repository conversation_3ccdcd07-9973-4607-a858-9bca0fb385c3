system: villiers_analytics
description: "Villiers.ai Analytics Domain - Comprehensive analytics and business intelligence system for data-driven decision making across all Villiers.ai operations including event tracking, user behavior analysis, conversion optimization, performance monitoring, and business intelligence reporting"

intent_assertions:
  - "User activity tracking and behavioral analytics"
  - "System performance metrics and operational monitoring"
  - "Revenue optimization through detailed analytics"
  - "Business intelligence dashboards and reporting"
  - "Search analytics and feature usage tracking"
  - "Conversion funnel analysis and optimization"
  - "Quote accuracy tracking and operator performance metrics"
  - "Event-driven analytics and audit trails"
  - "Real-time dashboards and administrative reporting"
  - "Support revenue optimization through detailed analytics"
  - "Enable predictive analytics for demand forecasting"
  - "Provide actionable insights for business optimization"
  - "Support compliance and audit requirements"
  - "Improve user experience through behavioral analytics"

technical_assertions:
  # Core Analytics Service
  analytics_service:
    - path: "app/services/analytics_service.py"
      purpose: "Primary analytics service with centralized database manager pattern"
      lines: 1198
      capabilities: ["user_activity_tracking", "system_metrics", "conversion_analysis", "business_intelligence"]
    - path: "app/services/legacy_services/analytics.py"
      purpose: "Legacy analytics service (deprecated, migration in progress)"
      lines: 895
      status: "deprecated"

  # API Endpoints
  analytics_api:
    - path: "app/api/v1/endpoints/analytics/analytics.py"
      purpose: "Analytics data collection and reporting endpoints"
      lines: 424
      endpoints:
        - path: "/api/v1/analytics/track"
          method: "POST"
          purpose: "Track user activity events from client side"
        - path: "/api/v1/analytics/track/page-view"
          method: "POST"
          purpose: "Track page view events"
        - path: "/api/v1/analytics/reports/active-users"
          method: "POST"
          purpose: "Generate active users analytics reports"
        - path: "/api/v1/analytics/reports/page-views"
          method: "POST"
          purpose: "Generate page views analytics reports"
        - path: "/api/v1/analytics/reports/conversion"
          method: "POST"
          purpose: "Generate conversion funnel analytics reports"
        - path: "/api/v1/analytics/reports/feature-usage"
          method: "POST"
          purpose: "Generate feature usage analytics reports"
        - path: "/api/v1/analytics/reports/search"
          method: "POST"
          purpose: "Generate search analytics and optimization reports"
        - path: "/api/v1/analytics/quote-accuracy"
          method: "POST"
          purpose: "Get quote estimate accuracy metrics"
        - path: "/api/v1/analytics/reports/today"
          method: "GET"
          purpose: "Get today's analytics summary for quick insights"
        - path: "/api/v1/analytics/reports/last-7-days"
          method: "GET"
          purpose: "Get last 7 days analytics summary"

  # Admin UI Analytics
  admin_analytics:
    - path: "app/api/v1/endpoints/admin/admin_ui.py"
      purpose: "Admin dashboard analytics endpoints"
      lines: 294
      endpoints:
        - path: "/api/v1/admin-ui/analytics"
          method: "GET"
          purpose: "Comprehensive analytics data for admin dashboards"
        - path: "/api/v1/admin-ui/dashboard"
          method: "GET"
          purpose: "Consolidated admin dashboard with key metrics"

  # Database Models
  analytics_core:
    - path: "app/db/models/analytics.py"
      purpose: "Core analytics database models"
      lines: 158
      models:
        - "UserActivity - User interaction and behavior tracking"
        - "SystemMetric - System performance and operational metrics"
        - "ConversionFunnel - Booking conversion process tracking"
        - "SearchAnalytics - Search query pattern analysis"
        - "BookingAnalytics - Detailed booking process analytics"

  # Schemas
  analytics_schemas:
    - path: "app/db/schemas/analytics.py"
      purpose: "Analytics API schemas and validation"
      lines: 294
      schemas:
        - "UserActivityCreate/Response - User activity data transfer"
        - "SystemMetricCreate/Response - System metrics data transfer"
        - "SearchAnalyticsBase/Response - Search metrics schemas"
        - "BookingAnalyticsBase/Response - Booking analytics schemas"
        - "AnalyticsReport - Standardized report format"
        - "ConversionFunnelBase/Response - Funnel tracking schemas"
        - "QuoteAccuracyMetrics - Quote performance analysis"
        - "AdminAnalyticsResponse - Comprehensive analytics response"
        - "UserMetrics - User-related analytics"
        - "RevenueMetrics - Financial performance analytics"
        - "PerformanceMetrics - System performance indicators"
        - "BusinessMetrics - Key business performance indicators"

  # Data Access Layer
  analytics_repositories:
    - path: "app/db/manager/repositories/user_activity_repository.py"
      purpose: "User activity data access layer with comprehensive analytics queries"
      lines: 802
      operations: ["activity_tracking", "funnel_analysis", "user_metrics", "conversion_tracking"]
    - path: "app/db/manager/repositories/system_metric_repository.py"
      purpose: "System metrics data access with performance analytics"
      lines: 426
      operations: ["metric_storage", "performance_analysis", "quote_accuracy", "api_metrics"]

  # Scheduler Integration
  analytics_automation:
    - task: "Analytics Data Aggregation"
      schedule: "daily 2:00 AM"
      purpose: "Aggregate daily analytics metrics for reporting"
    - task: "Performance Metrics Collection"
      schedule: "every 5 minutes"
      purpose: "Collect system performance metrics"
    - task: "Conversion Funnel Analysis"
      schedule: "hourly"
      purpose: "Update conversion funnel metrics and alerts"

behavior:
  # Data Collection Workflows
  event_tracking:
    user_activity_capture:
      1: "Client-side events trigger tracking endpoints"
      2: "AnalyticsService.track_user_activity processes events"
      3: "Activity stored with metadata and session context"
      4: "Real-time aggregation for dashboard updates"

  system_metrics_collection:
    performance_monitoring:
      1: "System components emit performance metrics"
      2: "Metrics captured via AnalyticsService.track_system_metric"
      3: "Metrics stored with dimensions and categories"
      4: "Automated alerts triggered for anomalies"

  # Analytics Reporting Workflows
  reporting_workflows:
    real_time_dashboards:
      dashboard_loading:
        1: "Admin accesses dashboard endpoint"
        2: "Multiple analytics services queried in parallel"
        3: "Metrics aggregated and formatted for UI consumption"
        4: "Response cached for performance optimization"
        5: "Dashboard auto-refreshes with latest data"

    scheduled_reporting:
      report_generation:
        1: "Scheduler triggers report generation tasks"
        2: "Historical data aggregated by time periods"
        3: "Reports generated and stored for retrieval"
        4: "Stakeholders notified of report availability"

  # Analysis Workflows
  conversion_analysis:
    funnel_optimization:
      1: "Conversion metrics analyzed for drop-off identification"
      2: "A/B testing data correlated with conversion rates"
      3: "Optimization recommendations generated"
      4: "Implementation impact tracked and measured"

invariants:
  data_integrity:
    - "All analytics events must include timestamp and category classification"
    - "User activity must be trackable across sessions"
    - "System metrics must include timestamp and category classification"
    - "All measurement values must be validated before storage"
    - "Event ordering must be preserved in time-series data"

  performance_requirements:
    - "Analytics collection must not impact application performance"
    - "Dashboard queries must complete within 2000ms"
    - "Event tracking must complete within 100ms"
    - "Real-time metrics must be updated within 30 seconds"
    - "System metrics collection must not impact application performance"

  privacy_compliance:
    - "Analytics data retention must comply with privacy regulations"
    - "User consent must be verified before tracking personal data"
    - "Personal data must be anonymized in analytics aggregations"

forbidden_states:
  data_loss:
    - "Analytics events must never be lost due to processing failures"
    - "Metrics must never be double-counted or duplicated"
    - "Analytics data must never be lost due to system failures"

  privacy_violations:
    - "Personal data must never be exposed in analytics APIs"
    - "Tracking must never continue after user opt-out"

  performance_degradation:
    - "Analytics collection must never block primary application flows"
    - "Dashboard queries must never timeout or fail silently"
    - "Memory usage must never exceed 500MB per analytics process"
    - "Analytics queries must never impact transactional performance"

depends_on:
  data_layer:
    - database
    - caching
  business_logic:
    - booking
    - authentication
  infrastructure:
    - api
    - monitoring

provides:
  analytics_capabilities:
    - "User behavior tracking and analysis"
    - "System performance monitoring and alerting"
    - "Revenue analytics and financial performance tracking"
    - "Conversion funnel optimization insights"
    - "Business intelligence dashboards and reporting"
    - "Quote accuracy and operator performance metrics"
    - "Search analytics and feature usage insights"

enforcement_hooks:
  monitoring:
    analytics_operations:
      location: "/var/log/villiers/analytics.log"
      content: "All analytics operations with performance metrics and user context"
      retention: "90 days"

    privacy_compliance:
      location: "/var/log/villiers/analytics_privacy.log"
      content: "User consent and data processing decisions"
      retention: "3 years"

  validation:
    - "Validate all incoming analytics data for completeness"
    - "Check privacy compliance before data collection"
    - "Verify performance impact within acceptable limits"
    - "Monitor data quality and accuracy metrics"

security:
  access_control:
    - "User activity tracking available to authenticated and anonymous users"
    - "Analytics APIs require admin-level authentication"
    - "System metrics collection requires service-level authentication"

  data_protection:
    - "Anonymous data aggregation preferred for reporting"
    - "Rate limiting on all analytics endpoints"
    - "Input validation on all tracking endpoints"
    - "Secure data transmission using HTTPS"

monitoring:
  metrics:
    - "Event processing rate and queue depth"
    - "Analytics API response times and success rates"
    - "Dashboard query performance and cache hit rates"
    - "Data quality scores and validation failures"
    - "Memory usage and garbage collection in analytics processes"
    - "Real-time metrics update frequency and accuracy"

  alerts:
    - "Analytics API response time > 500ms"
    - "Event processing queue depth > 1000 items"
    - "Data validation failure rate > 5%"
    - "Dashboard queries failing > 5%"

performance:
  response_times:
    tracking: "< 100ms for event tracking endpoints"
    dashboard: "< 2000ms for admin dashboard queries"
    reports: "< 5000ms for standard analytics reports"
    metrics: "< 500ms for real-time metrics endpoints"

  throughput:
    events: "10,000+ events per minute sustained processing"
    queries: "1,000+ concurrent dashboard queries"
    concurrent_users: "500+ concurrent admin dashboard users"

  resource_limits:
    memory_usage: "< 500MB per analytics service instance"
    cpu_utilization: "< 70% during peak analytics processing"
    cache_efficiency: "> 80% cache hit rate for dashboard queries"

scalability:
  horizontal_scaling:
    - "Analytics services can be horizontally scaled independently"
    - "Event processing can be distributed across multiple workers"
    - "Dashboard queries can be load balanced across read replicas"

  data_partitioning:
    - "Time-series data partitioned by date for efficient queries"
    - "System metrics partitioned by category and time range"
    - "Archive old analytics data to cold storage automatically"

availability:
  fault_tolerance:
    - "Graceful degradation when analytics endpoints are unavailable"
    - "Event queuing for temporary service outages"
    - "Fallback to simplified metrics during system overload"

  disaster_recovery:
    reporting_failures:
      - "Automated failover to backup analytics infrastructure"
      - "Partial data reporting with clear data quality indicators"

  notifications:
    - "Email notifications for critical analytics system failures"
    - "Slack alerts for performance degradation"
    - "PagerDuty integration for system outages"

files:
- app/services/analytics_service.py
- app/services/legacy_services/analytics.py
- app/api/v1/endpoints/analytics/analytics.py
- app/api/v1/endpoints/admin/admin_ui.py
- app/db/models/analytics.py
- app/db/schemas/analytics.py
- app/db/manager/repositories/user_activity_repository.py
- app/db/manager/repositories/system_metric_repository.py 