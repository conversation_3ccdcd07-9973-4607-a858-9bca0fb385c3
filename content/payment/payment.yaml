system: "payment"
description: |
  Comprehensive payment processing domain managing the complete financial lifecycle of bookings,
  from platform fee calculations to payment processing, refunds, and financial reconciliation.
  Integrates multi-provider payment processing (Stripe, BTCPay) with dynamic fee optimization,
  secure payment data handling, and real-time payment status tracking throughout the booking workflow.

intent_assertions:
  - payment_lifecycle_management: "Complete payment processing from fee calculation to reconciliation with multi-provider support"
  - secure_payment_processing: "PCI-compliant payment processing with secure tokenization and data protection"
  - dynamic_fee_optimization: "Intelligent platform fee calculation with discount strategies and conversion optimization"
  - multi_provider_integration: "Seamless integration with Stripe, BTCPay, and other payment providers"
  - payment_status_tracking: "Real-time payment status monitoring with webhook integration and state synchronization"
  - refund_management: "Automated refund processing with policy enforcement and multi-method support"
  - invoice_generation: "Detailed invoice creation with itemized billing and payment tracking"
  - financial_reconciliation: "Complete financial audit trail with transaction matching and reporting"

technical_assertions:
  payment_processing_engine:
    - "PaymentService orchestrates multi-provider payment processing with fallback mechanisms"
    - "BTCPayService handles Bitcoin payments with 15% automatic discount application"
    - "PaymentRepository manages payment data with comprehensive CRUD operations"
    - "Stripe integration provides secure credit card processing with tokenization"
    - "Webhook processing ensures real-time payment status updates from providers"
  
  fee_calculation_system:
    - "FeeCalculatorService implements dynamic fee optimization based on multiple factors"
    - "Base platform fee: 5.0%, discounted fee: 3.5% with intelligent reduction algorithms"
    - "Fee analysis and performance tracking for conversion rate optimization"
    - "User loyalty, demand, and operator relationship factors in fee calculations"
    - "Real-time fee adjustments based on quote acceptance patterns"
  
  payment_state_management:
    - "Nine-state payment workflow: PENDING → PROCESSING → PLATFORM_FEE_PAID → OPERATOR_PAID → COMPLETED"
    - "Alternative states: FAILED, REFUNDED, PARTIALLY_REFUNDED, STORED_CREDIT"
    - "Payment status synchronization with booking lifecycle state transitions"
    - "Atomic state updates with transaction safety and rollback capabilities"
    - "Audit trail maintenance for all payment state changes with timestamps"
  
  invoice_and_billing:
    - "Comprehensive invoice generation with itemized line items and tax calculations"
    - "Invoice tracking with due dates, payment history, and overdue management"
    - "Multiple payment method support per invoice with partial payment handling"
    - "PDF generation and email delivery of invoices and payment confirmations"
    - "Financial reporting integration with detailed transaction categorization"
  
  security_and_compliance:
    - "PCI DSS compliance with secure payment data tokenization"
    - "Encrypted storage of sensitive payment method details"
    - "Webhook signature verification for payment provider notifications"
    - "Payment data masking and access control based on user permissions"
    - "Security audit logging for all payment operations and access attempts"

behavior:
  payment_processing_workflow:
    payment_initiation:
      - "User selects payment method (credit card, Bitcoin, bank transfer)"
      - "Platform fee calculation with discount application (15% for Bitcoin)"
      - "Payment intent creation with provider-specific parameters"
      - "Secure payment data collection and tokenization"
      - "Real-time availability and validation of payment methods"
    
    payment_execution:
      - "Multi-provider payment processing with automatic fallback mechanisms"
      - "Real-time payment status tracking with webhook integration"
      - "Payment confirmation with booking status synchronization"
      - "Error handling and retry logic for failed payment attempts"
      - "Payment receipt generation and customer notification"
    
    payment_completion:
      - "Payment status update to PLATFORM_FEE_PAID upon successful processing"
      - "Operator payment coordination and status tracking to OPERATOR_PAID"
      - "Final payment completion with reconciliation and audit trail"
      - "Customer and operator notifications for payment confirmations"
      - "Integration with booking lifecycle for status synchronization"
  
  dynamic_fee_optimization:
    fee_calculation_engine:
      - "Multi-factor fee calculation considering quote value, user loyalty, and demand"
      - "Real-time fee optimization based on conversion rate analysis"
      - "Intelligent fee reduction strategies for improved quote acceptance"
      - "Minimum fee thresholds (8%) and maximum reduction limits (30%)"
      - "Fee performance analytics with conversion rate tracking"
    
    discount_application:
      - "Automatic 15% discount for Bitcoin payments with real-time calculation"
      - "Loyalty-based discounts for repeat customers and high-value bookings"
      - "Seasonal and promotional discount management with expiration tracking"
      - "Corporate and group booking discount strategies"
      - "Dynamic pricing based on market demand and operator relationships"
  
  refund_processing:
    refund_policy_enforcement:
      - "Automated refund eligibility calculation based on booking timing"
      - "24+ hours before flight: Full refund minus processing fees"
      - "2-24 hours before flight: 50% refund of platform fees"
      - "Less than 2 hours: Emergency exceptions with manual approval"
      - "Post-flight refunds only for service failures with investigation"
    
    refund_execution:
      - "Multi-provider refund processing matching original payment method"
      - "Partial and full refund support with amount validation"
      - "Refund status tracking with customer notification workflows"
      - "Operator coordination for refunds involving operator payments"
      - "Financial reconciliation and reporting for all refund transactions"
  
  invoice_management:
    invoice_lifecycle:
      - "Automated invoice generation upon booking confirmation"
      - "Itemized billing with platform fees, operator charges, and additional services"
      - "Tax calculation and compliance with local regulations"
      - "Due date management with automated reminder notifications"
      - "Payment tracking with partial payment support and balance calculations"
    
    payment_tracking:
      - "Real-time payment application to invoices with balance updates"
      - "Payment history maintenance with method and timestamp tracking"
      - "Overdue invoice identification with escalation workflows"
      - "Credit and debit memo generation for adjustments and corrections"
      - "Financial reporting integration with revenue recognition standards"

invariants:
- "Payment status transitions must follow defined workflow with no invalid state jumps"
- "Platform fee calculations must respect minimum thresholds and maximum discounts"
- "All payment operations must maintain audit trails with user and timestamp tracking"
- "Refund amounts cannot exceed original payment amounts with validation enforcement"
- "Payment data encryption and tokenization must be maintained throughout processing"
- "Invoice amounts must match sum of line items plus taxes with precision validation"
- "Payment provider webhooks must be verified for signature authenticity"
- "Booking status synchronization must align with payment status transitions"

forbidden_states:
- "Payment processing without proper authentication and authorization"
- "Platform fee calculations below minimum thresholds or above maximum limits"
- "Refund processing exceeding original payment amounts or policy violations"
- "Payment data storage without encryption or proper tokenization"
- "Invoice generation without proper line item validation and tax calculations"
- "Payment status updates without corresponding audit trail entries"
- "Webhook processing without signature verification and validation"
- "Financial operations without proper transaction safety and rollback capabilities"

depends_on:
  internal_systems:
    - "Authentication system for user verification and payment authorization"
    - "Booking system for lifecycle integration and status synchronization"
    - "Notification system for payment confirmations and alerts"
    - "Audit system for comprehensive logging and compliance tracking"
    - "Database manager for transactional safety and data integrity"
  
  external_providers:
    - "Stripe API for credit card processing and payment tokenization"
    - "BTCPay Server for Bitcoin payment processing and invoice management"
    - "Banking APIs for bank transfer processing and verification"
    - "Tax calculation services for compliance and accurate billing"
    - "Email services for invoice delivery and payment notifications"

provides:
  payment_services:
    - "Payment processing capabilities for booking confirmation workflows"
    - "Fee calculation services for quote optimization and pricing strategies"
    - "Refund processing for customer service and booking cancellations"
    - "Invoice generation for detailed billing and financial documentation"
    - "Payment status tracking for real-time booking workflow integration"
  
  financial_data:
    - "Payment transaction data for financial reporting and analytics"
    - "Revenue tracking for business intelligence and performance metrics"
    - "Refund analytics for policy optimization and customer satisfaction"
    - "Fee performance data for conversion rate analysis and optimization"
    - "Financial audit trails for compliance and regulatory reporting"

enforcement_hooks:
  validation_hooks:
    - trigger: "before_payment_processing"
      action: "Validate payment method, amount, and user authorization"
    - trigger: "before_fee_calculation"
      action: "Verify quote data and apply business rule constraints"
    - trigger: "before_refund_processing"
      action: "Validate refund eligibility and amount limitations"
    - trigger: "before_invoice_generation"
      action: "Verify line items, tax calculations, and booking validity"
  
  security_hooks:
    - trigger: "payment_data_access"
      action: "Enforce encryption, tokenization, and access control policies"
    - trigger: "webhook_received"
      action: "Verify signature authenticity and prevent replay attacks"
    - trigger: "financial_operation"
      action: "Log security events and maintain audit trails"
    - trigger: "sensitive_data_exposure"
      action: "Mask payment details and enforce data protection policies"

implementation_gaps:
  security_enhancements:
    - "Multi-factor authentication for high-value payment operations"
    - "Advanced fraud detection and prevention algorithms"
    - "Payment method risk scoring and validation"
    - "Enhanced webhook security with mutual TLS verification"
  
  feature_completions:
    - "Automated operator payment distribution and reconciliation"
    - "Advanced reporting and analytics dashboard for financial operations"
    - "Integration with additional payment providers and methods"
    - "Automated tax compliance and reporting for multiple jurisdictions"
  
  optimization_opportunities:
    - "Machine learning-based fee optimization for improved conversion rates"
    - "Predictive analytics for payment failure prevention"
    - "Advanced refund policy automation with AI-driven exceptions"
    - "Real-time financial reconciliation and automated matching"

implementation_reports:
  core_functionality: "85% - Core payment processing, fee calculation, and refund management implemented"
  security_compliance: "80% - PCI compliance framework established with encryption and tokenization"
  provider_integration: "90% - Stripe and BTCPay integration complete with webhook processing"
  invoice_management: "75% - Basic invoice generation and tracking with partial payment support"
  financial_reporting: "70% - Transaction tracking and basic reporting with audit trail maintenance"

primary_flow:
  complete_payment_lifecycle:
    steps:
      - "Fee Calculation: Dynamic platform fee calculation with discount application"
      - "Payment Initiation: User selects payment method and initiates transaction"
      - "Payment Processing: Multi-provider processing with real-time status tracking"
      - "Payment Confirmation: Status update and booking lifecycle synchronization"
      - "Invoice Generation: Detailed invoice creation with itemized billing"
      - "Financial Reconciliation: Transaction matching and audit trail completion"
    
    integration_points:
      - "Booking system integration for status synchronization and workflow triggers"
      - "Notification system integration for payment confirmations and alerts"
      - "Audit system integration for comprehensive logging and compliance tracking"
      - "Reporting system integration for financial analytics and business intelligence"

restoration_method:
  payment_recovery:
    - "Payment status verification and correction through provider API reconciliation"
    - "Transaction replay capabilities for failed payments with idempotency protection"
    - "Manual payment processing for exceptional cases with proper authorization"
    - "Audit trail reconstruction for compliance and regulatory requirements"
  
  data_integrity:
    - "Payment data validation and correction through comprehensive reconciliation"
    - "Invoice and payment matching with automated discrepancy resolution"
    - "Financial data restoration from provider records and audit trails"
    - "Backup and recovery procedures for payment-related database operations"

core_principles:
  - "Security First: All payment operations prioritize data protection and compliance"
  - "Transparency: Complete audit trails and clear payment status communication"
  - "Reliability: Robust error handling and recovery mechanisms for payment operations"
  - "Optimization: Continuous improvement of fee structures and conversion rates"
  - "Integration: Seamless coordination with booking lifecycle and business workflows"

strengths:
  - "Comprehensive multi-provider payment processing with automatic fallback mechanisms"
  - "Dynamic fee optimization with intelligent discount strategies and conversion tracking"
  - "Real-time payment status tracking with webhook integration and state synchronization"
  - "Robust refund management with automated policy enforcement and multi-method support"
  - "Detailed invoice generation with itemized billing and comprehensive payment tracking"
  - "Strong security framework with PCI compliance and data protection measures"

limitations:
  - "Limited to current payment providers (Stripe, BTCPay) with manual expansion required"
  - "Fee optimization based on basic algorithms without advanced ML/AI integration"
  - "Manual tax compliance management without automated multi-jurisdiction support"
  - "Basic fraud detection without advanced machine learning prevention systems"
  - "Limited financial reporting capabilities without advanced analytics dashboard"

database_models:
  - model: "Payment"
    purpose: "Core payment transaction tracking with comprehensive status management"
    key_fields: ["user_id", "booking_id", "amount", "currency", "payment_type", "status"]
    relationships: ["user", "booking", "payment_method", "invoice_items", "invoices"]
  
  - model: "PaymentMethod"
    purpose: "User payment method storage with secure tokenization"
    key_fields: ["user_id", "type", "name", "is_default", "processor_id"]
    relationships: ["user"]
  
  - model: "Invoice"
    purpose: "Detailed billing information with itemized charges"
    key_fields: ["payment_id", "user_id", "invoice_number", "total_amount", "is_paid"]
    relationships: ["payment", "user", "items"]
  
  - model: "InvoiceItem"
    purpose: "Individual line items for detailed billing breakdown"
    key_fields: ["payment_id", "invoice_id", "description", "amount", "category"]
    relationships: ["payment", "invoice"]

services:
  - service: "PaymentService"
    path: "app/services/payment_service.py"
    purpose: "Core payment processing orchestration with multi-provider support"
    methods: ["process_stripe_payment", "generate_btcpay_invoice", "verify_payment_status", "refund_payment"]
    integration: "Orchestrates payment workflows with provider-specific implementations"
  
  - service: "BTCPayService"
    path: "app/services/btcpay_service.py"
    purpose: "Bitcoin payment processing with BTCPay Server integration"
    methods: ["create_invoice", "check_invoice_status", "process_webhook_event"]
    integration: "Handles Bitcoin payments with automatic discount application"
  
  - service: "FeeCalculatorService"
    path: "app/services/fee_calculator_service.py"
    purpose: "Dynamic platform fee calculation with optimization strategies"
    methods: ["calculate_fee", "analyze_fee_performance", "calculate_fees"]
    integration: "Provides fee optimization for improved conversion rates"

repositories:
  - repository: "PaymentRepository"
    path: "app/db/manager/repositories/payment_repository.py"
    purpose: "Payment data access with comprehensive CRUD operations"
    methods: ["create_payment", "get_payment_with_relations", "update_payment_status"]
    queries: "Payment lifecycle management with status-based filtering and analytics"

schemas:
  database_schemas:
    - schema: "Payment"
      path: "app/db/schemas/payment.py"
      purpose: "Payment entity schema with status tracking and relationship management"
    - schema: "PaymentMethod"
      path: "app/db/schemas/payment.py"
      purpose: "Payment method schema with secure tokenization support"
    - schema: "Invoice"
      path: "app/db/schemas/payment.py"
      purpose: "Invoice schema with itemized billing and payment tracking"
  
  api_schemas:
    - schema: "PaymentCreate"
      path: "app/schemas/payment.py"
      purpose: "Payment creation request validation and processing"
    - schema: "PaymentProcessResponse"
      path: "app/schemas/payment.py"
      purpose: "Payment processing response with status and metadata"
    - schema: "RefundCreate"
      path: "app/schemas/payment.py"
      purpose: "Refund request validation with amount and reason tracking"

security_modules:
  - module: "Payment Data Encryption"
    purpose: "Secure storage and transmission of sensitive payment information"
    implementation: "Field-level encryption with tokenization for payment methods"
  
  - module: "Webhook Verification"
    purpose: "Authentic verification of payment provider webhook notifications"
    implementation: "Cryptographic signature validation with replay attack prevention"
  
  - module: "PCI Compliance Framework"
    purpose: "Payment Card Industry compliance for secure card processing"
    implementation: "Tokenization, encryption, and access control for payment data"
  
  - module: "Audit Trail Security"
    purpose: "Tamper-proof logging of all payment operations and access"
    implementation: "Comprehensive logging with integrity verification and retention policies"

performance:
  response_times:
    payment_processing: "<3000ms for complete payment workflow including provider interaction"
    fee_calculation: "<500ms for dynamic fee optimization with multiple factors"
    payment_status_check: "<300ms for real-time status verification"
    refund_processing: "<2000ms for refund validation and execution"
    invoice_generation: "<1000ms for detailed invoice creation with line items"
  
  throughput:
    concurrent_payments: "100+ simultaneous payment processing operations"
    webhook_processing: "500+ webhook events per minute with verification"
    fee_calculations: "1000+ fee calculations per minute with optimization"
    status_updates: "200+ payment status updates per minute"
  
  scalability:
    payment_volume: "Designed for 10,000+ transactions per day with horizontal scaling"
    provider_integration: "Multi-provider architecture supporting additional payment methods"
    database_optimization: "Indexed payment queries with efficient relationship loading"

consolidation_notes: |
  The payment domain represents a critical financial backbone of the villiers.ai platform,
  integrating sophisticated payment processing with dynamic fee optimization and comprehensive
  financial management. The system demonstrates strong architectural patterns with clear
  separation of concerns between payment processing, fee calculation, and financial tracking.
  
  Key architectural strengths include multi-provider payment processing with automatic fallback,
  real-time status synchronization with the booking lifecycle, and comprehensive audit trails
  for compliance and regulatory requirements. The dynamic fee optimization system provides
  intelligent pricing strategies that balance conversion rates with profitability.
  
  Security considerations are paramount with PCI compliance framework, payment data encryption,
  and webhook verification ensuring secure handling of sensitive financial information.
  The system's integration with booking workflows demonstrates sophisticated state management
  and transaction safety.

restoration_key_system: |
  Payment system restoration relies on comprehensive audit trails and provider reconciliation
  capabilities. All payment operations maintain detailed logs with timestamps and user tracking,
  enabling precise reconstruction of payment states and financial transactions.
  
  Key restoration mechanisms include payment status verification through provider APIs,
  transaction replay capabilities with idempotency protection, and manual payment processing
  for exceptional cases. The system's integration with multiple payment providers enables
  cross-verification and data integrity validation.
  
  Financial reconciliation processes ensure alignment between internal records and provider
  transactions, with automated discrepancy detection and resolution workflows.

advanced_recovery_options:
  - "Multi-provider payment verification and reconciliation for data integrity validation"
  - "Automated transaction replay with idempotency protection for failed payment recovery"
  - "Comprehensive audit trail analysis for compliance and regulatory investigation"
  - "Manual payment processing capabilities for exceptional cases and customer service"
  - "Financial data restoration from provider records and backup systems"
  - "Real-time monitoring and alerting for payment anomalies and system issues"

security:
  data_protection:
    - "PCI DSS compliance framework with regular security assessments"
    - "Payment data encryption at rest and in transit with industry-standard algorithms"
    - "Payment method tokenization to minimize sensitive data exposure"
    - "Access control and permission management for payment operations"
  
  fraud_prevention:
    - "Payment method validation and risk scoring for fraud detection"
    - "Transaction monitoring and anomaly detection for suspicious activities"
    - "Rate limiting and abuse prevention for payment endpoint protection"
    - "Webhook signature verification to prevent unauthorized payment updates"
  
  compliance_monitoring:
    - "Continuous compliance monitoring with automated policy enforcement"
    - "Regular security audits and penetration testing for vulnerability assessment"
    - "Data retention and purging policies for regulatory compliance"
    - "Incident response procedures for payment security breaches"