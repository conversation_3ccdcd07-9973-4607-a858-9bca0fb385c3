# Core Domain System Definition
# Foundational Infrastructure and Cross-Cutting Concerns for Villiers.ai

system: "core"
description: "Villiers.ai Core Domain - Foundational Infrastructure Layer providing essential services, shared utilities, security infrastructure, error handling, configuration management, and cross-cutting concerns that enable consistent operation across the entire charter aviation platform"

intent_assertions:
- "Provide robust foundational infrastructure supporting all domain operations with zero single points of failure"
- "Ensure comprehensive security enforcement across authentication, authorization, data validation, and API protection"
- "Deliver consistent error handling and recovery mechanisms with centralized exception management"
- "Enable efficient database operations through connection pooling, transaction management, and query optimization"
- "Provide standardized API response patterns and middleware for consistent client experiences"
- "Support scalable configuration management with environment-aware settings and feature flags"
- "Enable comprehensive logging, monitoring, and observability across all system operations"
- "Facilitate efficient service orchestration through dependency injection and lifecycle management"
- "Ensure data integrity through comprehensive validation, sanitization, and security checks"
- "Provide shared utilities and constants for consistent business logic across domains"

technical_assertions:
  # Core Configuration and Environment Management
  - path: "app/core/config.py"
    purpose: "Centralized configuration management with environment-aware settings"
    lines: 256
    features: [
      "Environment-based configuration with fallback mechanisms",
      "Database URL management with test and migration support",
      "External API key and service configuration",
      "CORS and security settings management",
      "Email provider configuration with multiple backends",
      "Cached settings for production performance optimization"
    ]

  # Security Infrastructure
  - path: "app/core/security.py"
    purpose: "JWT token management and cryptographic security utilities"
    lines: 179
    operations: [
      "create_access_token - JWT access token generation with expiry",
      "create_refresh_token - Refresh token generation for session management",
      "verify_token - Token validation and signature verification",
      "generate_login_code - Secure random code generation for authentication"
    ]

  - path: "app/core/auth.py"
    purpose: "Authentication dependencies and user resolution middleware"
    lines: 201
    dependencies: [
      "get_current_user - Extract and validate authenticated user from token",
      "get_current_superuser - Admin user authentication and authorization",
      "get_optional_current_user - Optional authentication for mixed endpoints",
      "verify_api_key - API key validation for service-to-service communication"
    ]

  # Error Handling and Exception Management
  - path: "app/core/errors.py"
    purpose: "Centralized error handling with standardized API responses"
    lines: 282
    exception_types: [
      "APIException - Base exception for all API errors with status codes",
      "BadRequestError - Client request validation failures (400)",
      "NotFoundError - Resource not found errors (404)",
      "ValidationError - Data validation and format errors (422)",
      "AuthenticationError - Authentication failures (401)",
      "AuthorizationError - Permission and access control failures (403)",
      "DatabaseOperationError - Database operation failures (500)"
    ]

  # API Response Standardization
  - path: "app/core/api_response.py"
    purpose: "Standardized API response patterns with consistent formatting"
    lines: 357
    response_types: [
      "success_response - Standardized success responses with data payload",
      "error_response - Consistent error response format with details",
      "paginated_response - Paginated list responses with metadata",
      "created_response - Resource creation success responses (201)",
      "accepted_response - Asynchronous operation acceptance (202)"
    ]

  # Validation and Security
  - path: "app/core/validation.py"
    purpose: "Comprehensive security validation and input sanitization"
    lines: 495
    security_features: [
      "XSS pattern detection and prevention",
      "SQL injection pattern validation",
      "HTML tag sanitization and filtering",
      "Name and contact information validation",
      "UUID and email security validation",
      "Comprehensive security validator factories"
    ]

  # Database Infrastructure
  - path: "app/core/database_config.py"
    purpose: "Database configuration utilities with fallback mechanisms"
    lines: 134
    capabilities: [
      "Environment-aware database URL resolution",
      "Test database configuration with isolation",
      "Alembic migration database management",
      "Centralized configuration with fallback support"
    ]

  # Logging Infrastructure
  - path: "app/core/logger.py"
    purpose: "Structured logging with security data masking"
    lines: 116
    features: [
      "Sensitive data masking for security compliance",
      "Service-specific logger creation with context",
      "Structured logging with JSON context support",
      "Centralized log formatting and configuration"
    ]

  # Middleware and Request Processing
  - path: "app/core/middleware.py"
    purpose: "Request/response middleware for cross-cutting concerns"
    lines: 151
    middleware_types: [
      "SessionMiddleware - Anonymous session management with cookies",
      "SecurityMiddleware - Security headers and protection",
      "RequestLoggingMiddleware - Request/response logging and tracing"
    ]

  # Scheduler Integration
  - path: "app/core/scheduler_integration.py"
    purpose: "Application-wide scheduler initialization and lifecycle management"
    lines: 97
    operations: [
      "initialize_scheduler - Startup scheduler initialization with database integration",
      "shutdown_scheduler - Graceful scheduler shutdown and cleanup",
      "register_system_jobs - Automated system job registration"
    ]

  # Business Constants and Rules
  - path: "app/core/constants.py"
    purpose: "Centralized business logic constants and configuration defaults"
    lines: 65
    constant_categories: [
      "AI model configuration with fallback support",
      "Authentication defaults and security parameters",
      "Business rules for fees, timeouts, and operational limits",
      "Email workflow configuration and intervals"
    ]

  # Utility Functions
  - path: "app/core/utils.py"
    purpose: "Common utility functions and helper methods"
    lines: 8
    utilities: ["UTC timestamp generation and date utilities"]

  - path: "app/core/user_agent_utils.py"
    purpose: "User agent detection and device analytics"
    lines: 303
    capabilities: [
      "Device type detection (mobile, tablet, desktop)",
      "Browser and operating system identification",
      "User agent parsing for analytics and security",
      "Request integration utilities for FastAPI"
    ]

endpoints:
  health_monitoring:
    - path: "/health"
      methods: ["GET"]
      description: "System health check with comprehensive metrics"
      response_time: "<100ms"
      handler: "main.py:health_check"
      
    - path: "/inspector"
      methods: ["GET"]
      description: "Database inspector interface for development"
      response_time: "<500ms"
      handler: "main.py:inspector_page"

  api_root:
    - path: "/"
      methods: ["GET"]
      description: "API root endpoint with service information"
      response_time: "<50ms"
      handler: "main.py:root"

database_models:
  # Core models are distributed across domains but managed centrally
  configuration:
    - model: "SystemConfiguration"
      purpose: "System-wide configuration storage"
      fields: ["key", "value", "category", "environment", "updated_at"]
      indexes: ["key", "category", "environment"]

  audit_logging:
    - model: "AuditLog"
      purpose: "Comprehensive audit trail for security and compliance"
      fields: ["event_type", "user_id", "resource_id", "action", "details", "timestamp", "ip_address"]
      indexes: ["event_type", "user_id", "timestamp", "resource_id"]

services:
  - service: "BaseService"
    path: "app/services/base_service.py"
    purpose: "Abstract base service providing common functionality for all services"
    methods: ["__init__"]
    dependencies: ["db_manager"]
    inheritance: "Abstract base class for all domain services"

  - service: "ServiceManager"
    path: "app/service_manager.py"
    purpose: "Service orchestration and dependency injection management"
    status: "placeholder_for_future_implementation"
    design: "Centralized service lifecycle management and dependency resolution"

repositories:
  - repository: "BaseRepository"
    path: "app/db/manager/base_repository.py"
    purpose: "Abstract base repository providing common data access patterns"
    methods: ["get_by_id", "create", "update", "delete", "list_with_pagination"]
    inheritance: "Base class for all domain repositories"

  - repository: "ConfigurationRepository"
    purpose: "System configuration data access and management"
    methods: ["get_config_value", "set_config_value", "get_config_by_category"]
    validation: ["configuration_key_format", "environment_validation"]

schemas:
  core_responses:
    - schema: "ApiResponse"
      purpose: "Standardized API response wrapper with generic data support"
      fields: ["status", "code", "message", "request_id", "data"]
      validation: ["response_format", "status_code_consistency"]

    - schema: "PaginatedResponse"
      purpose: "Paginated response format with metadata"
      fields: ["status", "code", "message", "data", "meta", "request_id"]
      validation: ["pagination_metadata", "data_list_format"]

    - schema: "APIError"
      purpose: "Standardized error response format"
      fields: ["status", "code", "message", "details", "error_type", "request_id"]
      validation: ["error_format", "sensitive_data_exclusion"]

  validation_schemas:
    - schema: "ValidationPatterns"
      purpose: "Security validation patterns and rules"
      patterns: ["XSS_PATTERNS", "SQL_INJECTION_PATTERNS", "HTML_TAGS", "NAME_PATTERN"]
      security: "Input sanitization and attack prevention"

behavior:
  configuration_management:
    environment_awareness:
      - "Development environment uses fresh settings for rapid iteration"
      - "Production environment uses cached settings for performance optimization"
      - "Test environment uses isolated configuration with database separation"
      - "Configuration fallback mechanisms ensure system stability"

    setting_resolution:
      - "Priority order: environment variables > .env file > default values"
      - "Database URLs support test and migration environment separation"
      - "External API keys loaded securely with validation"
      - "CORS origins configured per environment with security defaults"

  security_enforcement:
    authentication_flow:
      - "JWT tokens generated with cryptographically secure random JTI"
      - "Token validation includes signature verification and expiry checks"
      - "Refresh tokens enable secure session extension without re-authentication"
      - "Optional authentication supports mixed public/private endpoints"

    authorization_patterns:
      - "Role-based access control with admin user validation"
      - "Resource ownership validation for user-specific data"
      - "API key authentication for service-to-service communication"
      - "Permission checking with clear error responses"

  error_handling_workflow:
    exception_processing:
      - "Centralized exception handlers registered with FastAPI application"
      - "Database exceptions transformed to appropriate API responses"
      - "Validation errors provide detailed field-level feedback"
      - "Unexpected errors logged with request context for debugging"

    response_consistency:
      - "All errors follow standardized response format with status codes"
      - "Error details include actionable information without sensitive data"
      - "Request IDs enable error tracing across distributed operations"
      - "Error types categorize issues for client-side handling"

  database_operations:
    connection_management:
      - "Connection pooling optimized for environment (test vs production)"
      - "Pool size and timeout configuration based on load requirements"
      - "Connection health monitoring with pre-ping validation"
      - "Graceful connection cleanup on application shutdown"

    transaction_handling:
      - "Automatic retry logic for transient database infrastructure issues"
      - "Application-level errors preserved without retry interference"
      - "Read-only transaction optimization for query operations"
      - "Transaction isolation levels appropriate for operation types"

invariants:
- "All API responses must follow standardized format with consistent status codes"
- "Security validation must be applied to all user inputs across domains"
- "Database connections must be properly managed with connection pooling"
- "Error handling must provide clear feedback without exposing sensitive data"
- "Configuration must be environment-aware with appropriate fallback mechanisms"
- "Logging must mask sensitive data while providing adequate debugging information"
- "Authentication tokens must be cryptographically secure with proper expiry"
- "Service dependencies must be properly injected and lifecycle managed"
- "Input validation must prevent XSS, SQL injection, and other security vulnerabilities"
- "Middleware must process requests consistently across all endpoints"

forbidden_states:
- "Sensitive data (passwords, tokens, keys) exposed in logs or error messages"
- "Database connections left open without proper cleanup"
- "Configuration secrets stored in version control or logs"
- "API responses with inconsistent format or missing error handling"
- "Authentication bypasses or security validation circumvention"
- "Unvalidated user input processed by business logic"
- "Database operations without proper transaction management"
- "Services initialized without proper dependency injection"
- "Error responses exposing internal system details or stack traces"
- "Middleware failing to process security headers or session management"

depends_on:
- system: "database"
  purpose: "Persistent storage and transaction management"
  components: ["postgresql", "connection_pooling", "migration_management"]

provides:
- foundational_infrastructure
- security_enforcement
- error_handling_framework
- api_response_standardization
- configuration_management
- logging_and_monitoring
- database_connection_management
- service_orchestration_framework
- input_validation_and_sanitization
- middleware_and_request_processing

files:
  core_infrastructure:
    - "app/core/config.py - Centralized configuration with environment awareness"
    - "app/core/security.py - JWT token management and cryptographic utilities"
    - "app/core/auth.py - Authentication dependencies and user resolution"
    - "app/core/errors.py - Centralized error handling and exception management"
    - "app/core/api_response.py - Standardized API response patterns"
    - "app/core/validation.py - Security validation and input sanitization"
    - "app/core/database_config.py - Database configuration utilities"
    - "app/core/logger.py - Structured logging with security masking"
    - "app/core/middleware.py - Request/response middleware"
    - "app/core/scheduler_integration.py - Scheduler lifecycle management"
    - "app/core/constants.py - Business logic constants"
    - "app/core/utils.py - Common utility functions"
    - "app/core/user_agent_utils.py - User agent detection and analytics"
    - "app/core/json_encoder.py - Custom JSON encoding for API responses"
    - "app/core/responses.py - HTTP response utilities"
    - "app/core/enums.py - Core enumeration types"

  service_framework:
    - "app/services/base_service.py - Abstract base service class"
    - "app/service_manager.py - Service orchestration (placeholder)"
    - "app/api/deps.py - FastAPI dependency injection utilities"

  application_setup:
    - "app/main.py - FastAPI application initialization and configuration"

implementation_status:
  fully_implemented:
    - "Comprehensive configuration management with environment awareness"
    - "JWT-based authentication and authorization infrastructure"
    - "Centralized error handling with standardized API responses"
    - "Security validation and input sanitization framework"
    - "Database connection management with pooling and transaction support"
    - "Structured logging with sensitive data masking"
    - "API response standardization with consistent formatting"
    - "Middleware framework for cross-cutting concerns"
    - "User agent detection and device analytics"
    - "Business constants and configuration defaults"

  partially_implemented:
    - "Service manager for dependency injection (placeholder exists)"
    - "Comprehensive audit logging (basic framework in place)"
    - "Advanced monitoring and observability (basic health checks implemented)"
    - "Feature flag management (configuration framework supports it)"

  implementation_gaps:
    - "Advanced service orchestration with dependency injection container"
    - "Comprehensive audit trail implementation with detailed logging"
    - "Advanced monitoring with metrics collection and alerting"
    - "Feature flag management interface and API"
    - "Configuration management UI for runtime settings"
    - "Advanced caching layer with Redis integration"
    - "Rate limiting middleware for API protection"
    - "Request/response compression middleware"

  planned_enhancements:
    - "Service mesh integration for microservices architecture"
    - "Advanced security middleware with threat detection"
    - "Performance monitoring with distributed tracing"
    - "Configuration encryption for sensitive settings"
    - "Advanced logging with structured query capabilities"
    - "API versioning framework with backward compatibility"
    - "Circuit breaker patterns for external service resilience"
    - "Advanced validation with custom rule engines"

performance_requirements:
  response_times:
    - "Health check endpoint: <100ms"
    - "Configuration retrieval: <50ms"
    - "Token validation: <25ms"
    - "Error response generation: <10ms"
    - "Validation operations: <5ms"

  scalability_targets:
    - "Database connection pool: 20 connections with 10 overflow"
    - "Concurrent request handling: 1000+ requests/second"
    - "Memory usage: <512MB for core services"
    - "CPU utilization: <30% under normal load"

security_considerations:
  authentication_security:
    - "JWT tokens with cryptographically secure generation"
    - "Token expiry enforcement with automatic refresh"
    - "Secure session management with proper cleanup"

  data_protection:
    - "Sensitive data masking in logs and error responses"
    - "Input validation preventing injection attacks"
    - "Secure configuration management with environment isolation"

  api_security:
    - "CORS configuration with environment-specific origins"
    - "Security headers enforcement through middleware"
    - "Request/response logging without sensitive data exposure"

integration_points:
  cross_domain_services:
    - "Authentication services consumed by all domains"
    - "Database management used across all data operations"
    - "Error handling framework used by all API endpoints"
    - "Validation utilities used across all input processing"
    - "Configuration management accessed by all services"

  external_systems:
    - "Database infrastructure (PostgreSQL)"
    - "Email services (Mailgun, SMTP)"
    - "AI services (OpenAI)"
    - "Payment processors (Stripe, BTCPay)"
    - "Monitoring and logging systems"

monitoring_and_observability:
  health_monitoring:
    - "System health endpoint with comprehensive metrics"
    - "Database connection health monitoring"
    - "Service dependency health checks"

  logging_strategy:
    - "Structured logging with JSON formatting"
    - "Request/response logging with correlation IDs"
    - "Error logging with context and stack traces"
    - "Security event logging for audit trails"

  performance_monitoring:
    - "Response time tracking for all endpoints"
    - "Database query performance monitoring"
    - "Memory and CPU usage tracking"

enforcement_hooks:
- "validate_configuration_consistency"
- "ensure_security_headers_present"
- "confirm_error_handling_coverage"
- "verify_input_validation_completeness"
- "validate_authentication_requirements" 