system: "operator"
description: "Comprehensive operator domain for charter aviation management encompassing operator lifecycle management, performance tracking, reliability analytics, communication coordination, and business relationship optimization with integrated fleet management and automated solicitation workflows"

intent_assertions:
  - "Enable complete operator lifecycle management from onboarding through performance optimization with comprehensive profile management and relationship tracking"
  - "Provide advanced operator reliability scoring with multi-metric performance analytics, predictive insights, and automated quality assurance"
  - "Support sophisticated communication coordination with automated solicitation campaigns, response tracking, and preference management"
  - "Deliver comprehensive fleet management integration with aircraft assignment, availability synchronization, and operational constraint tracking"
  - "Enable performance analytics and business intelligence for operator selection, relationship management, and network optimization"
  - "Support automated empty leg solicitation workflows with operator outreach, response tracking, and inventory management"
  - "Provide contact management system with multi-representative support, communication preferences, and relationship tracking"
  - "Enable pricing transparency and optimization with operator-specific rules, market analysis, and revenue optimization"

technical_assertions:
  operator_management_system:
    core_operator_management:
      - "Primary operator API endpoints for listing, filtering, and basic operations with reliability-based ranking"
      - "Admin operator management endpoints with comprehensive CRUD operations, search, and status filtering"
      - "Operator data scraping and enrichment API endpoints with external source integration"
      - "Primary operator management service with database manager integration and performance tracking"
      - "Comprehensive operator database operations with CRUD, query capabilities, and relationship management"
      - "Operator database models with comprehensive relationship mapping including fleet and performance relationships"
    
    operator_lifecycle_workflows:
      - "Comprehensive operator profile creation with fleet and contact information management"
      - "Automated data enrichment through web scraping and external API integration with quality validation"
      - "Fleet relationship establishment with aircraft assignment and availability tracking"
      - "Contact management setup with role-based access and communication preferences"
      - "Initial performance baseline establishment with historical data integration"
      - "Compliance verification and regulatory requirement validation with audit trails"
    
    data_management_infrastructure:
      - "Operator entity with extensive relationship mapping to aircraft, quotes, bookings, flights, conversations, empty legs"
      - "OperatorContact model for multi-representative contact management with preferences and notifications"
      - "OperatorPricing model for operator-specific pricing rules and dynamic adjustments"
      - "OperatorResponsePattern model for response parsing patterns and automation"
      - "OperatorPerformanceStats model for comprehensive performance metrics tracking and analytics"

  reliability_tracking_system:
    performance_analytics:
      - "Operator reliability tracking and performance metrics API with detailed analytics and trend analysis"
      - "Primary reliability tracking service with comprehensive metrics and predictive analytics capabilities"
      - "Legacy reliability tracking service with comprehensive metrics calculation and pattern analysis"
      - "Operator reliability data access layer with metrics calculation and performance analytics"
      - "Operator reliability and performance prediction schemas with trend analysis and forecasting"
    
    reliability_calculation_engine:
      - "Multi-metric reliability scoring with weighted performance calculations including response time, accuracy, punctuality"
      - "Response time tracking with statistical analysis and trend identification over configurable periods"
      - "Quote accuracy measurement with pricing variance analysis and final booking correlation"
      - "On-time performance monitoring with flight punctuality scoring and operational constraint correlation"
      - "Cancellation rate tracking with reason analysis and impact assessment on business relationships"
      - "Customer satisfaction integration with feedback correlation and improvement tracking"
    
    predictive_analytics_infrastructure:
      - "Historical performance trend analysis with seasonal and market factor integration"
      - "Machine learning-powered performance prediction with confidence scoring and uncertainty quantification"
      - "Operator selection optimization based on route, aircraft, and timing factors with risk assessment"
      - "Market benchmarking with industry standard comparison and competitive analysis"
      - "Risk assessment with cancellation probability and reliability confidence intervals"
      - "Performance improvement recommendations with actionable insights and progress tracking"

  communication_coordination_system:
    solicitation_management:
      - "Operator solicitation and response tracking for empty leg inventory management with campaign optimization"
      - "Automated operator solicitation for empty leg inventory management with weekly campaigns"
      - "Operator communication coordination and preference management with multi-channel support"
      - "Legacy operator communication service with advanced features including quote requests and relationship scoring"
    
    automated_campaign_workflows:
      - "Weekly empty leg solicitation with personalized operator messaging and intelligent targeting"
      - "Automated email delivery with tracking and open rate monitoring including delivery status validation"
      - "Response collection and processing with structured data validation and quality assessment"
      - "Follow-up campaign management with automated reminder systems and escalation procedures"
      - "Opt-out preference management with compliance and relationship preservation"
      - "Campaign performance analytics with response rate optimization and strategic insights"
    
    relationship_optimization:
      - "Communication preference tracking with channel optimization and timing analysis"
      - "Response pattern analysis with machine learning-powered insights and automation"
      - "Relationship scoring with interaction quality and business value assessment"
      - "Automated issue detection and resolution with proactive relationship management"
      - "Performance feedback delivery with constructive improvement recommendations"
      - "Partnership opportunity identification with strategic relationship development"

  administrative_and_integration_system:
    fleet_coordination:
      - "Aircraft assignment optimization with operator capability and availability matching"
      - "Fleet utilization tracking with efficiency analysis and optimization recommendations"
      - "Maintenance coordination with scheduling and availability impact management"
      - "Empty leg opportunity identification with aircraft positioning and route optimization"
      - "Capacity planning support with demand forecasting and fleet expansion analysis"
      - "Operational constraint management with regulatory compliance and safety oversight"
    
    pricing_optimization:
      - "Operator-specific pricing rule management with market factor integration and seasonal adjustments"
      - "Dynamic pricing adjustment calculation with demand forecasting and capacity optimization"
      - "Route-specific pricing factors with competitive analysis and market positioning"
      - "Volume discount rule management with loyalty program integration and partnership pricing"
      - "Real-time pricing adjustment with market condition monitoring and competitive response"
      - "Commission optimization with profitability analysis and operator incentive alignment"

behavior:
  operator_lifecycle_management:
    operator_onboarding_workflow:
      - "Comprehensive operator profile creation with fleet and contact information validation"
      - "Automated data enrichment through web scraping and external API integration with quality assessment"
      - "Fleet relationship establishment with aircraft assignment and availability tracking"
      - "Contact management setup with role-based access and communication preferences"
      - "Initial performance baseline establishment with historical data integration and validation"
      - "Compliance verification and regulatory requirement validation with approval workflows"
    
    operator_profile_management:
      - "Real-time operator profile updates with change tracking and audit trails"
      - "Fleet composition management with aircraft addition, removal, and status updates"
      - "Contact relationship management with multiple representatives and role assignments"
      - "Pricing rule configuration with seasonal adjustments and market factors"
      - "Service capability definition with specialization and operational constraint tracking"
      - "Preference management for communication, booking, and operational procedures"
    
    data_enrichment_and_validation:
      - "Automated web scraping with data quality validation and source attribution"
      - "External API integration with rate limiting and error handling"
      - "Data validation with completeness checking and accuracy verification"
      - "Source attribution with data lineage tracking and quality scoring"
      - "Regular data refresh with automated scheduling and validation procedures"

  performance_tracking_and_analytics:
    reliability_calculation_workflow:
      - "Performance data collection from multiple sources with validation and deduplication"
      - "Sample size verification with statistical significance testing and minimum threshold enforcement"
      - "Data quality assessment with completeness checking, accuracy validation, and outlier detection"
      - "Individual metric calculation with response time, response rate, quote accuracy, on-time performance"
      - "Weighted score aggregation with configurable metric weights and statistical validation"
      - "Database persistence with audit trail maintenance and change tracking"
    
    predictive_analytics_workflow:
      - "Historical data retrieval with time period specification and data quality validation"
      - "Trend identification with statistical analysis, pattern recognition, and seasonal adjustment"
      - "Performance pattern analysis with correlation detection and causal relationship assessment"
      - "Performance forecasting with machine learning models and statistical algorithms"
      - "Route-specific adjustment with geographic correlation and operational complexity assessment"
      - "Confidence interval calculation with uncertainty quantification and risk assessment"
    
    operator_ranking_workflow:
      - "Multi-dimensional scoring with reliability metrics, capability assessment, and market positioning"
      - "Category-specific weighting with aircraft type specialization and route expertise"
      - "Regional adjustment with geographic factors and local market conditions"
      - "Competitive analysis with peer comparison and market differential assessment"
      - "Dynamic ranking updates with real-time score integration and position tracking"

  communication_and_relationship_management:
    automated_solicitation_campaigns:
      - "Weekly empty leg solicitation with personalized operator messaging and intelligent targeting"
      - "Automated email delivery with tracking and open rate monitoring"
      - "Response collection and processing with structured data validation"
      - "Follow-up campaign management with automated reminder systems"
      - "Opt-out preference management with compliance and relationship preservation"
      - "Campaign performance analytics with response rate optimization"
    
    relationship_optimization_workflow:
      - "Relationship assessment with current relationship health evaluation and multi-factor analysis"
      - "Performance correlation with business value correlation and relationship quality metrics"
      - "Issue identification with proactive problem detection and automated alerting"
      - "Improvement planning with strategic relationship enhancement and actionable recommendations"
      - "Implementation tracking with progress monitoring and milestone achievement validation"
      - "Value measurement with ROI analysis and relationship impact quantification"
    
    communication_preference_management:
      - "Communication preference tracking with channel optimization and timing analysis"
      - "Opt-in/opt-out management with immediate effect and audit logging"
      - "Multi-channel communication coordination with delivery tracking and performance monitoring"
      - "Response pattern analysis with machine learning-powered insights and automation"
      - "Preference migration with proper notification and validation procedures"

invariants:
  - "Operator records must have unique identification with consistent naming conventions and validation"
  - "Fleet relationships must be validated with active aircraft and operator associations"
  - "Contact information must be verified with role-based access and communication preferences"
  - "Performance metrics must be calculated using standardized algorithms with consistent weighting"
  - "Reliability scores must be bounded between 0.0 and 1.0 with proper statistical validation"
  - "Pricing rules must be mathematically consistent with non-negative adjustments and valid ranges"
  - "Operator solicitation must respect opt-out preferences with immediate effect and compliance monitoring"
  - "Email campaigns must comply with anti-spam regulations with proper unsubscribe mechanisms"
  - "Communication tracking must maintain audit trails with privacy protection and data retention policies"
  - "Response data must be validated for completeness with structured format requirements"

forbidden_states:
  - "Operators without valid contact information or fleet relationships"
  - "Performance metrics calculated without sufficient historical data or statistical significance"
  - "Reliability scores outside valid ranges or with inconsistent calculation methods"
  - "Pricing rules with negative adjustments or mathematically impossible configurations"
  - "Solicitation emails sent to operators who have opted out or without proper consent"
  - "Communication without proper tracking and audit trail maintenance"
  - "Response data processing without validation and structured format verification"
  - "Preference management without proper versioning and change tracking"
  - "Performance calculations without proper error handling and fallback mechanisms"
  - "Bulk operations without transaction management and rollback capabilities"

depends_on:
  - core
  - authentication
  - aircraft
  - booking
  - communication
  - pricing
  - analytics

provides:
  - "Comprehensive operator database with fleet and performance information"
  - "Standardized operator reliability metrics with predictive analytics"
  - "Aircraft-operator relationship management with availability coordination"
  - "Operator-specific pricing rules with dynamic market adjustments"
  - "Automated operator outreach with response tracking and preference management"
  - "Business intelligence for operator selection and relationship optimization"
  - "Empty leg inventory management through automated operator campaigns"
  - "Comprehensive operator relationship tracking with optimization insights"

enforcement_hooks:
  pre_operator_create:
    - hook: "validate_operator_uniqueness"
      purpose: "Validate operator uniqueness across name, email, and identification fields"
      triggers: ["operator_creation", "profile_setup", "data_import"]
    
    - hook: "verify_required_fields"
      purpose: "Verify required fields completeness with business rule compliance"
      triggers: ["operator_creation", "profile_validation", "data_validation"]
    
    - hook: "check_fleet_relationship_validity"
      purpose: "Check fleet relationship validity with aircraft availability and operator capacity"
      triggers: ["fleet_assignment", "aircraft_association", "relationship_creation"]
  
  pre_operator_update:
    - hook: "ensure_update_consistency"
      purpose: "Ensure update consistency with existing relationships and dependencies"
      triggers: ["operator_update", "profile_modification", "relationship_change"]
    
    - hook: "validate_pricing_rule_changes"
      purpose: "Validate pricing rule changes with mathematical consistency and market reasonableness"
      triggers: ["pricing_update", "rule_modification", "market_adjustment"]
    
    - hook: "verify_contact_changes"
      purpose: "Verify contact changes with proper notification and preference migration"
      triggers: ["contact_update", "preference_change", "notification_setup"]
  
  pre_reliability_calculation:
    - hook: "validate_minimum_data_requirements"
      purpose: "Validate minimum data requirements for statistical significance"
      triggers: ["reliability_calculation", "performance_analysis", "metric_computation"]
    
    - hook: "check_calculation_methodology"
      purpose: "Check calculation methodology consistency with historical scoring"
      triggers: ["metric_calculation", "score_computation", "algorithm_execution"]
    
    - hook: "verify_data_sources_quality"
      purpose: "Verify data sources and quality for metric calculation inputs"
      triggers: ["data_collection", "source_validation", "quality_assessment"]
  
  pre_solicitation_send:
    - hook: "verify_operator_opt_in_status"
      purpose: "Verify operator opt-in status with current preference validation"
      triggers: ["solicitation_send", "campaign_delivery", "email_dispatch"]
    
    - hook: "check_rate_limiting_compliance"
      purpose: "Check rate limiting compliance with email provider restrictions"
      triggers: ["email_send", "campaign_execution", "bulk_delivery"]
    
    - hook: "validate_message_content"
      purpose: "Validate message content with regulatory compliance and brand standards"
      triggers: ["content_validation", "message_preparation", "campaign_setup"]
  
  post_solicitation_delivery:
    - hook: "log_delivery_status"
      purpose: "Log delivery status with success rate and error tracking"
      triggers: ["delivery_completion", "campaign_finished", "email_delivered"]
    
    - hook: "update_operator_engagement_metrics"
      purpose: "Update operator engagement metrics with response tracking"
      triggers: ["engagement_update", "response_received", "interaction_logged"]
    
    - hook: "monitor_opt_out_requests"
      purpose: "Monitor opt-out requests with immediate preference updating"
      triggers: ["opt_out_request", "preference_change", "unsubscribe_action"]
  
  post_performance_update:
    - hook: "validate_performance_metric_consistency"
      purpose: "Validate performance metric consistency with historical trends"
      triggers: ["performance_update", "metric_change", "score_modification"]
    
    - hook: "update_reliability_scores"
      purpose: "Update reliability scores with proper recalculation and validation"
      triggers: ["score_update", "reliability_change", "performance_recalculation"]
    
    - hook: "trigger_performance_alerts"
      purpose: "Trigger alerts for significant performance changes or anomalies"
      triggers: ["performance_change", "anomaly_detection", "threshold_violation"]

implementation_gaps:
  current_limitations:
    - gap: "Advanced machine learning models for operator selection optimization"
      description: "Current operator selection relies on basic scoring without advanced ML optimization"
      impact: "Suboptimal operator selection and missed optimization opportunities"
      priority: "high"
    
    - gap: "Real-time dashboard for operator relationship health monitoring"
      description: "Operator relationship monitoring requires manual analysis without real-time insights"
      impact: "Delayed response to relationship issues and manual overhead"
      priority: "medium"
    
    - gap: "Automated contract management and renewal tracking"
      description: "Contract management is manual without automated tracking and renewal notifications"
      impact: "Potential contract lapses and manual administrative overhead"
      priority: "medium"
    
    - gap: "Enhanced predictive analytics with market trend integration"
      description: "Predictive analytics limited to internal data without external market correlation"
      impact: "Reduced accuracy of performance predictions and market analysis"
      priority: "low"

implementation_reports:
  operator_service:
    status: "completed"
    completion: 85%
    features:
      - "Comprehensive operator CRUD operations with validation and error handling"
      - "Performance tracking integration with reliability metrics and analytics"
      - "Admin interface with pagination, search, and filtering capabilities"
      - "Database manager integration with transaction support and relationship management"
    gaps:
      - "Advanced operator selection algorithms with ML optimization"
      - "Real-time performance monitoring with automated alerting"
      - "Enhanced relationship scoring with predictive insights"
  
  operator_reliability_service:
    status: "completed"
    completion: 88%
    features:
      - "Multi-metric reliability calculation with weighted scoring algorithms"
      - "Trend analysis with historical performance tracking and seasonal adjustments"
      - "Operator ranking and benchmarking with competitive analysis"
      - "Performance prediction with confidence intervals and risk assessment"
      - "Business intelligence with market analysis and optimization recommendations"
    gaps:
      - "Advanced machine learning models for improved prediction accuracy"
      - "Real-time alerting for performance anomalies and relationship issues"
      - "Integration with external market data sources for comprehensive benchmarking"
  
  empty_leg_solicitation_service:
    status: "completed"
    completion: 82%
    features:
      - "Automated weekly solicitation campaigns with intelligent targeting"
      - "Response tracking with structured data validation and processing"
      - "Opt-in/opt-out preference management with compliance monitoring"
      - "Campaign performance analytics with optimization insights"
    gaps:
      - "Advanced A/B testing framework for campaign optimization"
      - "Machine learning-powered response prediction and targeting"
      - "Real-time campaign monitoring with automated adjustments"
  
  operator_repository:
    status: "completed"
    completion: 90%
    features:
      - "Comprehensive database operations with advanced querying and relationship management"
      - "Performance integration with metrics calculation and historical analysis"
      - "Pagination and search capabilities with filtering and sorting"
      - "Transaction support with error handling and rollback capabilities"
    gaps:
      - "Advanced query optimization for complex performance calculations"
      - "Real-time data synchronization with external sources"
      - "Enhanced caching strategies for performance optimization"

primary_flow:
  comprehensive_operator_management_workflow:
    steps:
      1. "Operator onboarding with comprehensive profile creation and validation"
      2. "Automated data enrichment through web scraping and external API integration"
      3. "Fleet relationship establishment with aircraft assignment and availability tracking"
      4. "Contact management setup with role-based access and communication preferences"
      5. "Initial performance baseline establishment with historical data integration"
      6. "Compliance verification and regulatory requirement validation"
      7. "Performance tracking with continuous data collection and metric calculation"
      8. "Reliability scoring with multi-metric analysis and statistical validation"
      9. "Automated solicitation campaigns with personalized messaging and tracking"
      10. "Relationship optimization with performance correlation and strategic insights"
    
    integration_points:
      - "Aircraft domain for fleet management and availability synchronization"
      - "Booking system for performance data collection and outcome tracking"
      - "Communication platform for email delivery and notification services"
      - "Analytics dashboard for business intelligence and reporting"
      - "Authentication system for admin access control and security"
    
    automation_features:
      - "Automated data enrichment with quality validation and source attribution"
      - "Scheduled performance updates with real-time metric calculation"
      - "Weekly solicitation campaigns with intelligent targeting and optimization"
      - "Dynamic ranking updates with competitive analysis and market positioning"
      - "Proactive relationship management with issue detection and resolution"

restoration_method:
  operator_data_recovery:
    triggers:
      - "Operator data corruption or integrity violations detected"
      - "Performance metric calculation errors or statistical anomalies"
      - "Fleet relationship inconsistencies or aircraft assignment conflicts"
      - "Communication preference corruption or solicitation failures"
    
    procedures:
      - "Historical data validation with integrity checking and consistency verification"
      - "Performance metric recalculation with validated algorithms and statistical verification"
      - "Fleet relationship restoration with aircraft availability and operator capacity validation"
      - "Communication preference restoration with audit trail verification and validation"
      - "System synchronization with external data sources and validation procedures"
    
    success_metrics:
      - "Operator data integrity restored with 100% consistency validation"
      - "Performance metrics accuracy within statistical confidence intervals"
      - "Fleet relationships consistency with aircraft availability and operational constraints"
      - "Communication preferences compliance with opt-in/opt-out requirements and audit trails"

core_principles:
  - "Comprehensive operator lifecycle management with automated workflows and quality assurance"
  - "Performance-driven operator selection with statistical rigor and predictive analytics"
  - "Relationship-centric communication with preference respect and compliance monitoring"
  - "Data-driven decision making with business intelligence and optimization insights"
  - "Quality assurance through validation, verification, and continuous improvement"
  - "Scalability through automation, optimization, and efficient resource utilization"
  - "Transparency through audit trails, change tracking, and accountability measures"

strengths:
  - "Comprehensive operator management with complete lifecycle support and relationship tracking"
  - "Advanced reliability scoring with multi-metric analysis and predictive analytics"
  - "Sophisticated communication coordination with automated campaigns and preference management"
  - "Robust fleet management integration with aircraft assignment and availability synchronization"
  - "Business intelligence capabilities with performance correlation and optimization insights"
  - "Automated solicitation workflows with intelligent targeting and response tracking"
  - "Strong data quality management with validation, enrichment, and integrity verification"

limitations:
  - "Machine learning models require additional training data and advanced algorithm implementation"
  - "Real-time monitoring capabilities limited by current infrastructure and alerting systems"
  - "External market data integration constrained by data source availability and API limitations"
  - "Advanced analytics features dependent on historical data quality and completeness"
  - "Relationship optimization algorithms require enhanced predictive modeling capabilities"
  - "Contract management automation limited by current workflow and tracking capabilities"
  - "Performance prediction accuracy constrained by market volatility and external factors"

database_models:
  primary_models:
    - model: "Operator"
      file: "app/db/models/operator.py"
      description: "Primary operator entity with comprehensive relationship mapping and performance tracking"
      fields: ["id", "name", "description", "website", "country", "reliability_score", "conversion_rate", "operator_metadata"]
      relationships: ["aircraft", "quotes", "bookings", "flights", "conversations", "empty_legs", "contacts", "pricing", "performance_stats"]
    
    - model: "OperatorContact"
      file: "app/db/models/operator.py"
      description: "Operator contact management with preferences and notification handling"
      fields: ["operator_id", "first_name", "last_name", "email", "phone", "is_primary", "notification_preferences"]
      relationships: ["operator"]
    
    - model: "OperatorPricing"
      file: "app/db/models/operator.py"
      description: "Operator-specific pricing rules and dynamic adjustments"
      fields: ["operator_id", "base_markup_percentage", "peak_season_multiplier", "volume_discount_rules"]
      relationships: ["operator"]
    
    - model: "OperatorResponsePattern"
      file: "app/db/models/operator.py"
      description: "Response parsing patterns for automation and machine learning"
      fields: ["operator_id", "pattern_type", "regex_pattern", "success_rate"]
      relationships: ["operator"]
    
    - model: "OperatorPerformanceStats"
      file: "app/db/models/adaptive.py"
      description: "Comprehensive performance metrics tracking and analytics"
      fields: ["operator_id", "total_bookings", "success_rate", "average_response_time", "reliability_score"]
      relationships: ["operator"]

services:
  core_services:
    operator_service:
      class: "OperatorService"
      file: "app/services/operator_service.py"
      description: "Primary operator management service with comprehensive CRUD and performance tracking"
      methods:
        - "get_operator(operator_id) -> Optional[Dict[str, Any]]"
        - "get_operator_performance(operator_id) -> Optional[Dict[str, Any]]"
        - "list_operators(min_reliability, category, region) -> List[Dict[str, Any]]"
        - "create_operator(operator_data) -> Optional[Dict[str, Any]]"
        - "update_operator(operator_id, operator_data) -> Optional[Dict[str, Any]]"
        - "delete_operator(operator_id) -> bool"
        - "get_operators_for_admin(page, per_page, search, status) -> Tuple[List[Dict], int]"
      features: ["CRUD operations", "performance tracking", "admin interface", "validation"]
    
    operator_reliability_service:
      class: "OperatorReliabilityService"
      file: "app/services/operator_reliability_service.py"
      description: "Operator reliability tracking and predictive analytics service"
      methods:
        - "calculate_operator_reliability(operator_id) -> Dict[str, Any]"
        - "get_operator_reliability_trend(operator_id, months) -> Dict[str, Any]"
        - "rank_operators(category, region, limit) -> List[Dict[str, Any]]"
        - "predict_operator_performance(operator_id, route) -> Dict[str, Any]"
        - "update_reliability_scores(limit) -> Dict[str, Any]"
      features: ["reliability scoring", "trend analysis", "operator ranking", "performance prediction"]
    
    empty_leg_solicitation_service:
      class: "EmptyLegSolicitationService"
      file: "app/services/empty_leg_solicitation_service.py"
      description: "Automated operator solicitation for empty leg inventory management"
      methods:
        - "start_weekly_solicitation() -> Dict[str, Any]"
        - "track_empty_leg_response(operator_id, response_data) -> Dict[str, Any]"
        - "get_operators_to_notify() -> List[Dict[str, Any]]"
        - "opt_out_operator(operator_id) -> bool"
        - "opt_in_operator(operator_id) -> bool"
      features: ["automated campaigns", "response tracking", "preference management"]
    
    operator_communication_service:
      class: "OperatorCommunicationService"
      file: "app/services/operator_communication_service.py"
      description: "Operator communication coordination and preference management"
      methods:
        - "send_operator_notification(operator_id, notification_data) -> Dict[str, Any]"
        - "track_communication_preferences(operator_id, preferences) -> Dict[str, Any]"
        - "coordinate_multi_operator_outreach(campaign_data) -> Dict[str, Any]"
      features: ["notification management", "preference tracking", "multi-channel coordination"]

repositories:
  data_access_layer:
    operator_repository:
      class: "OperatorRepository"
      file: "app/db/manager/repositories/operator_repository.py"
      description: "Comprehensive operator database operations with advanced querying and relationship management"
      methods:
        - "get_by_id(operator_id) -> Optional[Operator]"
        - "get_operator_performance(operator_id) -> Dict[str, Any]"
        - "list_operators(min_reliability, category, region) -> List[Operator]"
        - "get_operators_paginated(page, per_page, search, status) -> Tuple[List[Operator], int]"
        - "create_operator(operator_data) -> Operator"
        - "update_operator(operator_id, operator_data) -> Operator"
        - "delete_operator(operator_id) -> None"
        - "get_fiat_only_operators() -> List[Operator]"
      features: ["advanced querying", "relationship management", "performance integration", "pagination"]
    
    operator_reliability_repository:
      class: "OperatorReliabilityRepository"
      file: "app/db/manager/repositories/operator_reliability_repository.py"
      description: "Operator reliability data access with metrics calculation and performance analytics"
      methods:
        - "get_operator_by_id(operator_id) -> Operator"
        - "calculate_reliability_metrics(operator_id) -> Dict[str, Any]"
        - "get_performance_history(operator_id, start_date, end_date) -> List[Dict]"
        - "update_operator_reliability_score(operator_id, score) -> None"
        - "get_period_metrics(operator_id, start_date, end_date) -> Dict[str, Any]"
      features: ["metrics calculation", "historical analysis", "performance tracking", "score management"]

schemas:
  request_response_schemas:
    - schema: "OperatorCreate"
      file: "app/db/schemas/operator.py"
      description: "Operator creation schema with validation and business rule compliance"
      fields: ["name", "description", "website", "country", "operator_type", "contact_email", "fleet_size"]
    
    - schema: "OperatorUpdate"
      file: "app/db/schemas/operator.py"
      description: "Operator update schema with optional field validation and relationship consistency"
      fields: ["name", "contact_email", "website", "reliability_score", "response_time_avg", "is_active"]
    
    - schema: "Operator"
      file: "app/db/schemas/operator.py"
      description: "Complete operator schema with relationship data and performance metrics"
      fields: ["id", "name", "description", "website", "country", "reliability_score", "contacts", "reliability_stats"]
    
    - schema: "OperatorWithRelations"
      file: "app/db/schemas/operator.py"
      description: "Operator schema with comprehensive relationship data and performance analytics"
      fields: ["id", "name", "contacts", "response_patterns", "performance_stats", "aircraft_count"]
    
    - schema: "OperatorInList"
      file: "app/db/schemas/operator.py"
      description: "Simplified operator schema for list endpoints with essential information"
      fields: ["id", "name", "country", "reliability_score", "aircraft_count"]
    
    - schema: "OperatorReliabilityStats"
      file: "app/db/schemas/operator.py"
      description: "Operator reliability statistics schema with performance metrics"
      fields: ["response_time", "quote_accuracy", "on_time_performance", "customer_satisfaction"]

security_modules:
  operator_security:
    - module: "Operator Data Access Control"
      description: "Role-based authentication with admin-only management functions"
      implementation: "JWT token validation with operator access permissions and audit trail logging"
    
    - module: "Performance Metrics Security"
      description: "Restricted access to performance metrics with authorized personnel only"
      implementation: "Role-based access control with audit trail maintenance and data encryption"
    
    - module: "Solicitation Campaign Security"
      description: "Admin approval required for solicitation campaigns with compliance monitoring"
      implementation: "Multi-factor authentication for campaign operations with approval workflows"
    
    - module: "Communication Preference Security"
      description: "Secure handling of communication preferences with opt-out compliance"
      implementation: "Encrypted preference storage with immediate effect processing and audit logging"

performance:
  operator_performance_targets:
    - metric: "Operator Listing Response Time"
      target: "<500ms response time with pagination and filtering support"
      current: "380ms average response time"
      optimization: "Query optimization and caching strategies"
    
    - metric: "Reliability Calculation Processing Time"
      target: "<2000ms per operator with comprehensive metrics"
      current: "1200ms average calculation time"
      optimization: "Statistical calculation caching and incremental updates"
    
    - metric: "Solicitation Campaign Delivery Time"
      target: "<5000ms per campaign with batch processing optimization"
      current: "3200ms average delivery time"
      optimization: "Batch processing and parallel email delivery"
    
    - metric: "Performance Metric Update Time"
      target: "<1000ms per operator with real-time synchronization"
      current: "650ms average update time"
      optimization: "Incremental updates and optimized database queries"

consolidation_notes:
  - "Complete operator lifecycle management consolidated in OperatorService with database manager integration"
  - "Performance tracking centralized in OperatorReliabilityService with predictive analytics capabilities"
  - "Communication coordination unified in OperatorCommunicationService with preference management"
  - "Solicitation automation standardized in EmptyLegSolicitationService with campaign optimization"
  - "Fleet management integration consolidated through aircraft relationships with availability synchronization"
  - "Pricing optimization integrated through operator-specific rules with dynamic market adjustments"

restoration_key_system:
  operator_data_continuity:
    backup_mechanisms:
      - "Daily operator data backup with relationship integrity and performance metrics preservation"
      - "Performance calculation backup with algorithm versioning and historical accuracy tracking"
      - "Communication preference backup with opt-in/opt-out status and audit trail preservation"
    
    recovery_procedures:
      - "Operator data restoration with relationship validation and consistency verification"
      - "Performance metric recalculation with historical data validation and statistical verification"
      - "Communication preference restoration with compliance verification and audit trail validation"
    
    business_continuity:
      - "Alternative operator selection with manual scoring and expert evaluation"
      - "Backup communication channels with manual outreach and preference management"
      - "Performance data reconstruction from historical records with statistical validation"

advanced_recovery_options:
  - recovery: "Operator Profile Recovery"
    description: "Recovery of operator profiles with relationship data and performance history"
    procedure: "Profile restoration from backup with relationship validation and performance metric recalculation"
  
  - recovery: "Performance Analytics Recovery"
    description: "Restoration of performance analytics with historical data and trend analysis"
    procedure: "Analytics restoration with statistical validation and trend recalculation from historical data"
  
  - recovery: "Communication System Recovery"
    description: "Recovery of communication systems with preference data and campaign history"
    procedure: "Communication system restoration with preference validation and campaign data reconstruction"

security:
  operator_specific_security:
    - control: "Operator Data Access Authentication"
      description: "Secure access control for operator management with role-based permissions"
      implementation: "OAuth 2.0 authentication with operator access verification and session management"
    
    - control: "Performance Data Encryption"
      description: "End-to-end encryption for sensitive operator performance and business metrics"
      implementation: "AES-256 encryption for performance data with secure key management and access logging"
    
    - control: "Communication Compliance Security"
      description: "Secure handling of communication preferences with opt-out compliance monitoring"
      implementation: "Encrypted preference storage with immediate processing and compliance audit trails"
    
    - control: "Campaign Security Management"
      description: "Secure solicitation campaign management with approval workflows and monitoring"
      implementation: "Multi-factor authentication for campaigns with approval tracking and delivery monitoring"