system: "operator_management"
description: "Comprehensive operator lifecycle management system for charter aviation operators including CRUD operations, performance tracking, fleet integration, contact management, data enrichment, and administrative oversight with real-time analytics and business intelligence"

intent_assertions:
  - "Enable complete operator lifecycle management from onboarding through deactivation with comprehensive data tracking and relationship management"
  - "Provide robust operator performance monitoring with reliability scoring, trend analysis, and predictive analytics for optimal operator selection"
  - "Support scalable operator fleet integration with aircraft assignment, availability synchronization, and operational constraint management"
  - "Deliver comprehensive contact management with multi-representative support, communication preferences, and relationship tracking"
  - "Ensure automated data enrichment with web scraping, external API integration, and real-time profile completion"
  - "Maintain operator data integrity with validation, deduplication, relationship consistency, and audit trail preservation"
  - "Provide administrative oversight with comprehensive dashboard analytics, bulk operations, and compliance monitoring"
  - "Support business intelligence with operator benchmarking, market analysis, and strategic relationship optimization"

technical_assertions:
  operator_lifecycle_management:
    crud_operations:
      - "Complete operator CRUD with validation, relationship management, and cascade handling for dependent entities"
      - "Operator creation with automatic data enrichment, fleet integration, and contact setup workflows"
      - "Operator updates with field validation, relationship consistency checks, and change audit logging"
      - "Soft deletion with cascade handling, relationship preservation, and recovery mechanisms"
      - "Bulk operations with transaction management, error handling, and progress tracking"
    
    data_integrity:
      - "Operator uniqueness validation across name, email, IATA/ICAO codes with duplicate detection and merge options"
      - "Relationship consistency enforcement between operators, aircraft, contacts, and pricing configurations"
      - "Data validation with business rule compliance, format verification, and completeness checking"
      - "Audit trail maintenance with change tracking, user attribution, and version history"
      - "Automated data cleanup with orphaned record removal and relationship repair"
    
    data_enrichment:
      - "Automated web scraping for operator profile completion with data quality validation and source attribution"
      - "External API integration for fleet data, performance metrics, and regulatory information"
      - "Real-time data synchronization with external systems and third-party aviation databases"
      - "Data quality assessment with completeness scoring, accuracy validation, and improvement recommendations"
      - "Enrichment scheduling with configurable intervals, source prioritization, and failure handling"

  performance_tracking_system:
    metrics_calculation:
      - "Multi-factor reliability scoring with weighted metrics: response time (20%), response rate (15%), quote accuracy (15%), on-time performance (20%), cancellation rate (20%), customer satisfaction (10%)"
      - "Real-time performance data collection from bookings, quotes, communications, and customer feedback"
      - "Statistical analysis with confidence intervals, trend calculation, and anomaly detection"
      - "Historical performance tracking with seasonal adjustments, market comparisons, and benchmarking"
      - "Predictive analytics with machine learning models for performance forecasting and operator selection optimization"
    
    reliability_analytics:
      - "Comprehensive reliability trend analysis with monthly, quarterly, and annual performance tracking"
      - "Operator ranking and benchmarking with category-specific comparisons and market positioning"
      - "Performance prediction with route-specific adjustments and booking context analysis"
      - "Reliability score normalization with industry benchmarks and competitive analysis"
      - "Performance impact assessment with business value correlation and relationship optimization"
    
    business_intelligence:
      - "Operator portfolio analysis with performance distribution, risk assessment, and optimization opportunities"
      - "Market analysis with competitive positioning, pricing trends, and capacity utilization"
      - "Relationship value assessment with revenue contribution, booking success rates, and strategic importance"
      - "Performance forecasting with seasonal adjustments, market trends, and capacity planning"
      - "Strategic insights with actionable recommendations for operator network optimization"

  fleet_integration_management:
    aircraft_assignment:
      - "Dynamic aircraft-operator relationship management with availability synchronization and constraint tracking"
      - "Fleet capacity tracking with aircraft utilization, availability windows, and operational limitations"
      - "Operational constraint management with maintenance schedules, crew requirements, and regulatory compliance"
      - "Aircraft performance correlation with operator reliability and service quality metrics"
      - "Fleet optimization recommendations with capacity utilization analysis and strategic fleet planning"
    
    availability_synchronization:
      - "Real-time aircraft availability updates with operator fleet management system integration"
      - "Availability window tracking with maintenance schedules, booking conflicts, and operational constraints"
      - "Capacity planning with demand forecasting, fleet utilization optimization, and expansion recommendations"
      - "Aircraft assignment optimization with route efficiency, cost optimization, and customer preference matching"
      - "Fleet performance analytics with aircraft utilization rates, revenue per aircraft, and profitability analysis"

  contact_relationship_management:
    multi_representative_support:
      - "Comprehensive contact management with role assignment, hierarchy tracking, and communication preferences"
      - "Contact relationship mapping with primary/secondary designations, escalation paths, and responsibility areas"
      - "Communication preference management with channel selection, timing preferences, and language localization"
      - "Contact performance tracking with response rates, communication quality, and relationship effectiveness"
      - "Contact synchronization with external systems and operator management platforms"
    
    relationship_intelligence:
      - "Relationship health scoring with communication frequency, response quality, and business value correlation"
      - "Contact engagement analytics with response patterns, preferred channels, and optimal timing"
      - "Relationship optimization recommendations with communication strategy improvements and relationship strengthening"
      - "Contact lifecycle management with onboarding, training, and performance improvement support"
      - "Relationship portfolio analysis with contact distribution, capability coverage, and risk assessment"

  administrative_oversight:
    admin_dashboard_integration:
      - "Comprehensive operator management dashboard with real-time metrics, performance trends, and actionable insights"
      - "Paginated operator listing with advanced filtering, sorting, and search capabilities across all operator attributes"
      - "Bulk operation support with batch updates, mass communication, and administrative workflow automation"
      - "Performance monitoring with real-time alerts, threshold management, and automated escalation procedures"
      - "Compliance tracking with regulatory requirement monitoring, certification status, and audit trail maintenance"
    
    business_analytics:
      - "Operator network analysis with geographic distribution, capacity utilization, and market coverage"
      - "Performance benchmarking with industry standards, competitive analysis, and best practice identification"
      - "Business value assessment with revenue contribution, profitability analysis, and strategic importance scoring"
      - "Risk assessment with performance volatility, relationship stability, and business continuity planning"
      - "Strategic planning support with market expansion, operator acquisition, and partnership development insights"

behavior:
  operator_onboarding_workflow:
    registration_and_validation:
      - "Operator registration with comprehensive data collection, validation, and business rule compliance"
      - "Duplicate detection with smart matching algorithms and merge recommendations for existing operator records"
      - "Data completeness assessment with mandatory field validation and enrichment opportunity identification"
      - "Initial compliance verification with regulatory requirement checking and certification validation"
      - "Welcome workflow initiation with communication setup, relationship establishment, and onboarding guide delivery"
    
    automated_enrichment:
      - "Web scraping initiation for operator profile completion with data source prioritization and quality validation"
      - "External API integration for fleet data, performance history, and regulatory information retrieval"
      - "Data quality assessment with completeness scoring, accuracy validation, and improvement recommendations"
      - "Profile optimization with data correlation, conflict resolution, and standardization procedures"
      - "Enrichment status tracking with progress monitoring, success metrics, and completion notifications"
    
    fleet_and_contact_setup:
      - "Fleet integration with aircraft assignment, availability synchronization, and operational constraint configuration"
      - "Contact establishment with multi-representative setup, role assignment, and communication preference configuration"
      - "Relationship mapping with hierarchy establishment, escalation path definition, and responsibility area assignment"
      - "Performance baseline establishment with historical data integration and initial reliability scoring"
      - "Operational setup completion with system integration, workflow configuration, and go-live procedures"

  performance_monitoring_workflow:
    continuous_data_collection:
      - "Real-time performance data gathering from booking system with quote response tracking and conversion monitoring"
      - "Customer feedback integration with satisfaction scoring, issue tracking, and service quality assessment"
      - "Communication pattern analysis with response time tracking, engagement quality, and preference learning"
      - "Operational performance tracking with on-time performance, cancellation rates, and service reliability"
      - "Market data correlation with competitive analysis, pricing trends, and capacity utilization"
    
    analytics_and_scoring:
      - "Multi-metric reliability calculation with weighted scoring, normalization, and confidence interval determination"
      - "Trend analysis with historical comparison, seasonal adjustment, and performance trajectory assessment"
      - "Benchmark comparison with industry standards, peer analysis, and competitive positioning"
      - "Predictive modeling with performance forecasting, risk assessment, and recommendation generation"
      - "Score validation with statistical significance testing, outlier detection, and quality assurance"
    
    optimization_and_insights:
      - "Performance optimization recommendations with actionable insights and improvement strategy development"
      - "Relationship enhancement suggestions with communication improvements and partnership strengthening"
      - "Portfolio optimization with operator mix analysis, capacity balancing, and strategic alignment"
      - "Risk mitigation with performance decline detection, early warning systems, and contingency planning"
      - "Strategic planning with market expansion opportunities, operator development, and competitive advantage"

  administrative_management_workflow:
    operator_oversight:
      - "Dashboard monitoring with real-time performance tracking, alert management, and administrative action triggering"
      - "Compliance monitoring with regulatory requirement tracking, certification status, and audit preparation"
      - "Issue management with problem identification, escalation procedures, and resolution tracking"
      - "Performance intervention with underperforming operator support, improvement planning, and progress monitoring"
      - "Strategic review with periodic assessment, relationship evaluation, and network optimization"
    
    bulk_operations:
      - "Mass updates with batch processing, transaction management, and error handling for large-scale changes"
      - "Communication campaigns with multi-operator messaging, response tracking, and engagement analytics"
      - "Data synchronization with external system integration, consistency validation, and conflict resolution"
      - "Performance recalculation with batch processing, validation, and systematic updates across operator network"
      - "Compliance updates with regulatory change implementation, certification renewal, and audit trail maintenance"

invariants:
  - "All operator records must maintain data integrity with unique identification across name, email, and regulatory codes"
  - "Performance metrics must be calculated with sufficient data points for statistical significance (minimum 30 data points)"
  - "Operator reliability scores must be updated within 24 hours of new performance data availability"
  - "Fleet relationships must maintain consistency with aircraft availability and operational constraints"
  - "Contact information must be validated and current with communication preference synchronization"
  - "Administrative actions must maintain comprehensive audit trails with user attribution and timestamp recording"
  - "Data enrichment must preserve data source attribution and quality scoring for validation"
  - "Performance predictions must include confidence intervals and statistical significance indicators"

forbidden_states:
  - "Operator records without primary identification fields (name, contact information) or regulatory compliance data"
  - "Performance calculations without sufficient historical data or with statistically insignificant sample sizes"
  - "Fleet assignments without availability validation or operational constraint verification"
  - "Contact relationships without communication preference setup or role assignment clarity"
  - "Administrative operations without proper authentication, authorization, and audit trail maintenance"
  - "Data enrichment without source attribution, quality validation, or accuracy verification"
  - "Bulk operations without transaction consistency, error handling, and recovery mechanisms"
  - "Performance scoring without normalization, benchmarking, and statistical validation"

depends_on:
  - core
  - authentication
  - aircraft
  - communication
  - analytics
  - booking

provides:
  - "Comprehensive operator lifecycle management with CRUD operations and relationship tracking"
  - "Performance monitoring and reliability scoring with predictive analytics and benchmarking"
  - "Fleet integration management with aircraft assignment and availability synchronization"
  - "Contact and relationship management with multi-representative support and communication coordination"
  - "Administrative oversight with dashboard analytics and bulk operation capabilities"
  - "Business intelligence with market analysis and strategic optimization recommendations"
  - "Data enrichment and quality management with automated profile completion and validation"
  - "Integration hooks for booking system, communication platform, and analytics dashboard"

enforcement_hooks:
  pre_operator_creation:
    - hook: "validate_operator_uniqueness"
      purpose: "Ensure operator uniqueness across name, email, and regulatory identification fields"
      triggers: ["operator_registration", "bulk_import", "api_creation"]
    
    - hook: "validate_required_fields"
      purpose: "Verify completeness of mandatory operator information and business rule compliance"
      triggers: ["operator_creation", "data_import", "profile_setup"]
    
    - hook: "validate_fleet_relationships"
      purpose: "Ensure fleet relationship validity with aircraft availability and operational constraints"
      triggers: ["fleet_assignment", "aircraft_addition", "capacity_planning"]
  
  post_operator_creation:
    - hook: "initiate_data_enrichment"
      purpose: "Start automated data enrichment workflows with web scraping and API integration"
      triggers: ["operator_creation", "profile_completion", "data_refresh"]
    
    - hook: "setup_performance_tracking"
      purpose: "Initialize performance monitoring with baseline establishment and metric setup"
      triggers: ["operator_onboarding", "service_activation", "monitoring_setup"]
    
    - hook: "configure_communication_integration"
      purpose: "Setup communication channels and preference management for operator coordination"
      triggers: ["contact_setup", "communication_activation", "preference_configuration"]
  
  pre_performance_calculation:
    - hook: "validate_data_sufficiency"
      purpose: "Ensure sufficient data points for statistically significant performance calculations"
      triggers: ["reliability_calculation", "score_update", "analytics_generation"]
    
    - hook: "verify_calculation_methodology"
      purpose: "Validate calculation methodology consistency with historical scoring and industry standards"
      triggers: ["metric_calculation", "score_normalization", "benchmark_comparison"]
  
  post_performance_update:
    - hook: "update_operator_rankings"
      purpose: "Refresh operator rankings and competitive positioning based on updated performance metrics"
      triggers: ["score_update", "performance_recalculation", "benchmark_refresh"]
    
    - hook: "trigger_optimization_recommendations"
      purpose: "Generate optimization recommendations based on performance changes and market analysis"
      triggers: ["performance_analysis", "trend_detection", "strategic_review"]

implementation_gaps:
  current_limitations:
    - gap: "Real-time fleet synchronization with external operator systems"
      description: "Direct API integration with operator fleet management systems not implemented"
      impact: "Manual fleet data updates required with potential accuracy and timeliness issues"
      priority: "high"
    
    - gap: "Advanced predictive analytics for operator performance forecasting"
      description: "Machine learning models for performance prediction partially implemented"
      impact: "Limited predictive capabilities for operator selection and risk assessment"
      priority: "medium"
    
    - gap: "Automated compliance monitoring and regulatory requirement tracking"
      description: "Regulatory compliance tracking requires manual updates and monitoring"
      impact: "Potential compliance gaps and manual overhead for certification tracking"
      priority: "medium"
    
    - gap: "International operator support with multi-currency and regulatory frameworks"
      description: "Limited support for international operators with different regulatory and currency requirements"
      impact: "Reduced global operator network expansion capabilities"
      priority: "low"

implementation_reports:
  operator_service:
    status: "completed"
    completion: 85%
    features:
      - "Complete operator CRUD operations with validation and relationship management"
      - "Performance metrics calculation with reliability scoring and trend analysis"
      - "Admin interface integration with dashboard analytics and bulk operations"
      - "Data enrichment workflows with web scraping and external API integration"
      - "Fleet integration with aircraft assignment and availability tracking"
    gaps:
      - "Real-time external system synchronization for fleet and performance data"
      - "Advanced machine learning models for predictive analytics and optimization"
      - "Automated compliance monitoring with regulatory requirement tracking"
  
  operator_repository:
    status: "completed"
    completion: 90%
    features:
      - "Comprehensive database operations with advanced querying and filtering"
      - "Relationship management with cascade handling and integrity constraints"
      - "Performance metrics integration with statistical calculations and validation"
      - "Pagination and search capabilities with optimization for large datasets"
      - "Audit trail maintenance with change tracking and version history"
    gaps:
      - "Advanced query optimization for complex relationship queries"
      - "Bulk operation optimization for large-scale data processing"
      - "Real-time change notifications and event streaming"
  
  operator_reliability_service:
    status: "completed"
    completion: 83%
    features:
      - "Multi-metric reliability calculation with weighted scoring algorithms"
      - "Trend analysis with historical performance tracking and seasonal adjustments"
      - "Operator ranking and benchmarking with competitive analysis"
      - "Performance prediction with confidence intervals and risk assessment"
      - "Business intelligence with market analysis and optimization recommendations"
    gaps:
      - "Advanced machine learning models for performance prediction accuracy"
      - "Real-time alerting for performance anomalies and relationship issues"
      - "Integration with external market data sources for comprehensive benchmarking"

primary_flow:
  operator_lifecycle_management_workflow:
    steps:
      1. "Operator registration with comprehensive data collection and validation"
      2. "Automated data enrichment with web scraping and external API integration"
      3. "Fleet integration setup with aircraft assignment and availability synchronization"
      4. "Contact management configuration with multi-representative setup and communication preferences"
      5. "Performance baseline establishment with historical data integration and initial scoring"
      6. "Compliance verification with regulatory requirement validation and certification tracking"
      7. "Operational activation with system integration and workflow configuration"
      8. "Continuous performance monitoring with real-time data collection and analytics"
      9. "Relationship optimization with communication enhancement and strategic development"
      10. "Administrative oversight with dashboard monitoring and business intelligence reporting"
    
    integration_points:
      - "Aircraft management system for fleet integration and availability synchronization"
      - "Communication platform for contact management and relationship coordination"
      - "Booking system for performance data collection and reliability tracking"
      - "Analytics dashboard for business intelligence and strategic insights"
      - "Admin interface for operational oversight and bulk management operations"
    
    error_handling:
      - "Data validation failures with field-specific error reporting and correction guidance"
      - "Enrichment process errors with fallback mechanisms and manual override capabilities"
      - "Fleet integration issues with relationship repair and consistency validation"
      - "Performance calculation errors with statistical validation and confidence assessment"
      - "Administrative operation failures with transaction rollback and recovery procedures"

restoration_method:
  operator_data_recovery:
    triggers:
      - "Operator data corruption or integrity violations detected"
      - "Performance metric calculation errors or statistical anomalies"
      - "Fleet relationship inconsistencies or constraint violations"
      - "Contact information synchronization failures or communication disruptions"
    
    procedures:
      - "Data integrity assessment with comprehensive validation and relationship verification"
      - "Backup data restoration with incremental recovery and consistency checking"
      - "Relationship repair with constraint validation and dependency resolution"
      - "Performance metric recalculation with statistical validation and historical comparison"
      - "System synchronization with external data sources and validation procedures"
    
    success_metrics:
      - "Data integrity restoration with 100% consistency validation"
      - "Performance metric accuracy within statistical confidence intervals"
      - "Fleet relationship consistency with operational constraint validation"
      - "Contact synchronization with communication preference preservation"

core_principles:
  - "Data integrity and consistency maintained through comprehensive validation and relationship management"
  - "Performance tracking based on statistical significance and industry benchmarking standards"
  - "Administrative efficiency through bulk operations and automated workflow management"
  - "Business intelligence driven by data analytics and strategic optimization recommendations"
  - "Scalability supported through efficient database design and optimization algorithms"
  - "Reliability ensured through comprehensive error handling and recovery mechanisms"
  - "User experience optimized through intuitive interfaces and responsive performance"

strengths:
  - "Comprehensive operator lifecycle management with end-to-end workflow automation and oversight"
  - "Advanced performance tracking with multi-metric reliability scoring and predictive analytics"
  - "Robust data management with validation, enrichment, and quality assurance mechanisms"
  - "Scalable architecture with efficient database operations and bulk processing capabilities"
  - "Business intelligence integration with market analysis and strategic optimization insights"
  - "Administrative efficiency with dashboard analytics and bulk operation support"
  - "Integration readiness with comprehensive API design and external system coordination"

limitations:
  - "Real-time external system integration limited to scheduled synchronization workflows"
  - "Machine learning models for predictive analytics require additional training data and validation"
  - "International operator support limited by single-currency and regulatory framework constraints"
  - "Compliance monitoring requires manual updates for regulatory changes and certification tracking"
  - "Performance prediction accuracy dependent on historical data availability and quality"
  - "Fleet integration relies on manual configuration without automated discovery mechanisms"
  - "Advanced analytics features require additional data sources and market intelligence integration"

database_models:
  primary_models:
    - model: "Operator"
      file: "app/db/models/operator.py"
      description: "Primary operator entity with comprehensive business information and relationship mapping"
      fields: ["name", "description", "website", "country", "operator_type", "status", "reliability_score", "conversion_rate", "fleet_size", "is_active"]
      relationships: ["aircraft", "quotes", "bookings", "flights", "contacts", "pricing", "performance_stats"]
    
    - model: "OperatorContact"
      file: "app/db/models/operator.py"
      description: "Operator contact management with role assignment and communication preferences"
      fields: ["operator_id", "first_name", "last_name", "position", "department", "email", "phone", "is_primary", "notification_preferences"]
      relationships: ["operator"]
    
    - model: "OperatorPricing"
      file: "app/db/models/operator.py"
      description: "Operator-specific pricing rules and dynamic adjustment configurations"
      fields: ["operator_id", "base_markup_percentage", "peak_season_multiplier", "volume_discount_rules", "payment_terms"]
      relationships: ["operator"]
    
    - model: "OperatorResponsePattern"
      file: "app/db/models/operator.py"
      description: "Response parsing patterns for automated communication processing"
      fields: ["operator_id", "pattern_type", "pattern_name", "regex_pattern", "success_rate"]
      relationships: ["operator"]

services:
  core_services:
    operator_service:
      class: "OperatorService"
      file: "app/services/operator_service.py"
      description: "Primary operator management service with comprehensive CRUD and performance tracking"
      methods:
        - "get_operator(operator_id) -> Optional[Dict[str, Any]]"
        - "get_operator_performance(operator_id) -> Optional[Dict[str, Any]]"
        - "list_operators(min_reliability, category, region) -> List[Dict[str, Any]]"
        - "create_operator(operator_data) -> Optional[Dict[str, Any]]"
        - "update_operator(operator_id, operator_data) -> Optional[Dict[str, Any]]"
        - "delete_operator(operator_id) -> bool"
        - "get_operators_for_admin(page, per_page, search, status) -> Tuple[List[Dict], int]"
      features: ["CRUD operations", "performance tracking", "admin interface", "validation"]
    
    operator_reliability_service:
      class: "OperatorReliabilityService"
      file: "app/services/operator_reliability_service.py"
      description: "Operator reliability tracking and predictive analytics service"
      methods:
        - "calculate_operator_reliability(operator_id) -> Dict[str, Any]"
        - "get_operator_reliability_trend(operator_id, months) -> Dict[str, Any]"
        - "rank_operators(category, region, limit) -> List[Dict[str, Any]]"
        - "predict_operator_performance(operator_id, route) -> Dict[str, Any]"
        - "update_reliability_scores(limit) -> Dict[str, Any]"
      features: ["reliability scoring", "trend analysis", "operator ranking", "performance prediction"]

repositories:
  data_access_layer:
    operator_repository:
      class: "OperatorRepository"
      file: "app/db/manager/repositories/operator_repository.py"
      description: "Comprehensive operator database operations with advanced querying and relationship management"
      methods:
        - "get_by_id(operator_id) -> Optional[Operator]"
        - "get_operator_performance(operator_id) -> Dict[str, Any]"
        - "list_operators(min_reliability, category, region) -> List[Operator]"
        - "get_operators_paginated(page, per_page, search, status) -> Tuple[List[Operator], int]"
        - "create_operator(operator_data) -> Operator"
        - "update_operator(operator_id, operator_data) -> Operator"
        - "delete_operator(operator_id) -> None"
        - "get_fiat_only_operators() -> List[Operator]"
      features: ["advanced querying", "relationship management", "performance integration", "pagination"]
    
    operator_reliability_repository:
      class: "OperatorReliabilityRepository"
      file: "app/db/manager/repositories/operator_reliability_repository.py"
      description: "Operator reliability data access with metrics calculation and performance analytics"
      methods:
        - "get_operator_by_id(operator_id) -> Operator"
        - "calculate_reliability_metrics(operator_id) -> Dict[str, Any]"
        - "get_performance_history(operator_id, start_date, end_date) -> List[Dict]"
        - "update_operator_reliability_score(operator_id, score) -> None"
        - "get_period_metrics(operator_id, start_date, end_date) -> Dict[str, Any]"
      features: ["metrics calculation", "historical analysis", "performance tracking", "score management"]

schemas:
  request_response_schemas:
    - schema: "OperatorCreate"
      file: "app/db/schemas/operator.py"
      description: "Operator creation schema with validation and business rule compliance"
      fields: ["name", "description", "website", "country", "operator_type", "contact_email", "fleet_size"]
    
    - schema: "OperatorUpdate"
      file: "app/db/schemas/operator.py"
      description: "Operator update schema with optional field validation and relationship consistency"
      fields: ["name", "contact_email", "website", "reliability_score", "response_time_avg", "is_active"]
    
    - schema: "Operator"
      file: "app/db/schemas/operator.py"
      description: "Complete operator schema with relationship data and performance metrics"
      fields: ["id", "name", "description", "website", "country", "reliability_score", "contacts", "reliability_stats"]
    
    - schema: "OperatorWithRelations"
      file: "app/db/schemas/operator.py"
      description: "Operator schema with comprehensive relationship data and performance analytics"
      fields: ["id", "name", "contacts", "response_patterns", "performance_stats", "aircraft_count"]

security_modules:
  operator_management_security:
    - module: "Operator Data Access Control"
      description: "Role-based access control for operator management with admin-only modification privileges"
      implementation: "JWT token validation with operator management permissions and audit trail logging"
    
    - module: "Performance Data Protection"
      description: "Secure handling of sensitive operator performance and business metrics"
      implementation: "Data encryption at rest and in transit with access logging and retention policies"
    
    - module: "Administrative Operation Security"
      description: "Enhanced security for bulk operations and administrative functions"
      implementation: "Multi-factor authentication for admin operations with transaction logging and approval workflows"
    
    - module: "Data Enrichment Security"
      description: "Secure handling of external data sources and API integrations"
      implementation: "API key management with rate limiting and data source validation"

performance:
  operator_management_metrics:
    - metric: "Operator Query Response Time"
      target: "<500ms for standard queries, <1000ms for complex relationship queries"
      current: "350ms average for standard queries, 750ms for complex queries"
      optimization: "Database indexing optimization and query caching for frequently accessed operator data"
    
    - metric: "Performance Calculation Processing Time"
      target: "<2000ms for individual operator reliability calculation"
      current: "1200ms average reliability calculation"
      optimization: "Statistical calculation caching and incremental metric updates"
    
    - metric: "Admin Dashboard Load Time"
      target: "<1000ms for operator management dashboard with 20 operators per page"
      current: "650ms average dashboard load time"
      optimization: "Data pagination optimization and dashboard component caching"
    
    - metric: "Bulk Operation Processing Rate"
      target: ">100 operators per minute for bulk updates"
      current: "85 operators per minute"
      optimization: "Batch processing optimization and transaction management improvements"

consolidation_notes:
  - "Operator management service integrates with communication platform for relationship coordination and performance tracking"
  - "Performance tracking integrates with booking system for real-time data collection and reliability scoring"
  - "Fleet integration coordinates with aircraft management for availability synchronization and operational constraints"
  - "Administrative functions integrate with system analytics for business intelligence and strategic insights"
  - "Data enrichment workflows coordinate with external API services while maintaining data quality and security"

restoration_key_system:
  operator_data_continuity:
    backup_mechanisms:
      - "Daily operator data backup with relationship preservation and performance history archival"
      - "Performance metrics backup with statistical validation and trend data preservation"
      - "Contact relationship backup with communication preferences and hierarchy maintenance"
    
    recovery_procedures:
      - "Operator data restoration with relationship validation and consistency verification"
      - "Performance metric recalculation with historical data validation and statistical verification"
      - "Contact synchronization with communication preference restoration and hierarchy reconstruction"
    
    business_continuity:
      - "Alternative operator sourcing with capability matching and emergency assignment procedures"
      - "Performance data reconstruction from booking history with statistical validation and trend analysis"
      - "Manual operator management with essential function preservation and gradual system restoration"

advanced_recovery_options:
  - recovery: "Performance Analytics Restoration"
    description: "Recovery of reliability calculations and performance trend data with statistical validation"
    procedure: "Historical data analysis with metric recalculation and statistical confidence verification"
  
  - recovery: "Fleet Relationship Recovery"
    description: "Restoration of operator-aircraft relationships with availability and constraint validation"
    procedure: "Relationship mapping reconstruction with operational constraint validation and availability synchronization"
  
  - recovery: "Contact Network Recovery"
    description: "Restoration of operator contact relationships with communication preferences and hierarchy"
    procedure: "Contact data validation with preference restoration and communication channel verification"

security:
  operator_specific_security:
    - control: "Operator Data Access Authentication"
      description: "Secure access control for operator management with role-based permissions"
      implementation: "OAuth 2.0 authentication with operator management role verification and session management"
    
    - control: "Performance Data Encryption"
      description: "End-to-end encryption for sensitive operator performance and business metrics"
      implementation: "AES-256 encryption for performance data with secure key management and access logging"
    
    - control: "Administrative Operation Audit"
      description: "Comprehensive audit logging for all administrative operations and bulk changes"
      implementation: "Immutable audit logs with digital signatures and external log verification"
    
    - control: "External Integration Security"
      description: "Secure handling of external API integrations and data enrichment sources"
      implementation: "API authentication with rate limiting, data validation, and security monitoring"