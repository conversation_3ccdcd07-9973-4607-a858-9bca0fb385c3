#!/usr/bin/env python3
"""
Villiers.ai System Definitions Index Rebuild Script
==================================================
Rebuilds index.yaml from all domain system definition files
with full content inlining, SHA256 generation, and domain validation.

This script handles the domain-driven architecture of Villiers.ai with
both main domain files and subdomain specializations.
"""

import yaml
import os
import hashlib
from datetime import datetime, timezone
import logging
from pathlib import Path
import glob
import shutil
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def backup_index_file():
    """
    Backup existing index.yaml file before regenerating.
    Creates timestamped backup in ../backups/system_definitions/
    """
    index_file = Path('index.yaml')
    if not index_file.exists():
        logger.info("No existing index.yaml to backup")
        return None
    
    # Create backup directory if it doesn't exist
    backup_dir = Path('../backups/system_definitions')
    backup_dir.mkdir(parents=True, exist_ok=True)
    
    # Create timestamped backup filename
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_filename = f'index_backup_{timestamp}.yaml'
    backup_path = backup_dir / backup_filename
    
    try:
        # Copy the file
        shutil.copy2(index_file, backup_path)
        logger.info(f"✅ Backed up index.yaml to {backup_path}")
        
        # Also backup the SHA256 file if it exists
        sha_file = Path('index.sha256')
        if sha_file.exists():
            sha_backup_path = backup_dir / f'index_backup_{timestamp}.sha256'
            shutil.copy2(sha_file, sha_backup_path)
            logger.info(f"✅ Backed up index.sha256 to {sha_backup_path}")
        
        return backup_path
    except Exception as e:
        logger.error(f"❌ Failed to backup index.yaml: {e}")
        return None

def get_villiers_domains():
    """Get all Villiers.ai domain directories and their system files"""
    domains = {}
    
    # Get all domain directories (excluding files and special directories)
    domain_dirs = [d for d in os.listdir('.') if os.path.isdir(d) and not d.startswith('.') and d not in ['__pycache__']]
    
    for domain_dir in domain_dirs:
        domain_files = {}
        
        # Get main domain file (domain_name.yaml)
        main_file = os.path.join(domain_dir, f"{domain_dir}.yaml")
        if os.path.exists(main_file):
            domain_files['main'] = main_file
        
        # Get all other YAML files in domain directory (excluding index files)
        yaml_files = glob.glob(os.path.join(domain_dir, "*.yaml"))
        for yaml_file in yaml_files:
            filename = os.path.basename(yaml_file)
            if filename != f"{domain_dir}.yaml" and not filename.startswith('index'):
                subdomain_name = filename.replace('.yaml', '')
                domain_files[subdomain_name] = yaml_file
        
        # Get subdirectory YAML files
        subdirs = [d for d in os.listdir(domain_dir) if os.path.isdir(os.path.join(domain_dir, d))]
        for subdir in subdirs:
            subdir_path = os.path.join(domain_dir, subdir)
            subdir_yaml_files = glob.glob(os.path.join(subdir_path, "*.yaml"))
            for yaml_file in subdir_yaml_files:
                filename = os.path.basename(yaml_file)
                if not filename.startswith('index'):
                    subdomain_name = f"{subdir}_{filename.replace('.yaml', '')}"
                    domain_files[subdomain_name] = yaml_file
        
        if domain_files:
            domains[domain_dir] = domain_files
    
    return domains

def load_system_file(filepath):
    """Load and return system definition file content with error handling"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f)
        return data
    except yaml.YAMLError as e:
        logger.error(f"YAML parsing error in {filepath}: {e}")
        return None
    except Exception as e:
        logger.error(f"Failed to load {filepath}: {e}")
        return None

def validate_system_definition(system_data, filepath):
    """Validate that system definition has required fields"""
    if not isinstance(system_data, dict):
        logger.warning(f"Invalid system data structure in {filepath}")
        return False
    
    # Check for required fields
    required_fields = ['system', 'description']
    missing_fields = [field for field in required_fields if field not in system_data]
    
    if missing_fields:
        logger.warning(f"Missing required fields in {filepath}: {missing_fields}")
        return False
    
    return True

def calculate_domain_metrics(domain_data):
    """Calculate metrics for a domain"""
    metrics = {
        'systems_count': len(domain_data),
        'total_lines': 0,
        'total_size_kb': 0,
        'subdomain_count': 0,
        'has_main_system': False
    }
    
    for system_name, system_info in domain_data.items():
        if system_name == 'main':
            metrics['has_main_system'] = True
        else:
            metrics['subdomain_count'] += 1
        
        filepath = system_info.get('path', '')
        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                    metrics['total_lines'] += len(content.splitlines())
                    metrics['total_size_kb'] += len(content.encode('utf-8')) / 1024
            except Exception as e:
                logger.warning(f"Could not read file for metrics: {filepath}: {e}")
    
    metrics['total_size_kb'] = round(metrics['total_size_kb'], 2)
    return metrics

def rebuild_villiers_index():
    """Rebuild the Villiers.ai canonical index from all domain files"""
    print("🔄 REBUILDING VILLIERS.AI SYSTEM DEFINITIONS INDEX")
    print("=" * 55)
    
    domains_structure = get_villiers_domains()
    
    # Create index structure
    index = {
        'system_intent': 'Villiers.ai enables seamless private jet booking through intelligent trip planning, real-time operator integration, and personalized customer experiences. The platform handles the complete booking lifecycle from natural language trip requests to post-flight feedback, optimizing pricing, availability, and service quality.',
        'metadata': {
            'title': 'Villiers.ai System Definitions - Canonical Index',
            'description': 'Complete domain-driven system definition index with full content inlining for private jet charter platform',
            'generated': datetime.now(timezone.utc).isoformat(),
            'domains_count': len(domains_structure),
            'architecture_type': 'domain_driven_design',
            'platform_type': 'private_jet_charter_platform',
            'validation_status': 'post_analytics_format_correction'
        },
        'structure': {
            'core_domains': {}
        },
        'domains': {},
        'domain_metrics': {},
        'constraints': {
            'data_sovereignty': [
                "All aircraft data must come from database - no hardcoded data",
                "All operator information must be dynamically loaded from database", 
                "All pricing must be calculated from database rules and historical data",
                "No in-memory storage for production data - PostgreSQL only"
            ],
            'performance_requirements': [
                "Trip search responses must complete within 2000ms",
                "Quote aggregation must complete within 30000ms", 
                "Chat responses must complete within 2000ms",
                "Database queries must complete within 200ms",
                "API health checks must complete within 100ms"
            ],
            'security_constraints': [
                "All customer data must be encrypted at rest and in transit",
                "Payment processing must be PCI DSS compliant",
                "Authentication must use secure token management", 
                "All API endpoints must have proper authentication"
            ],
            'integration_requirements': [
                "Real GPT API calls only - no mocking in production",
                "All operator communications must be logged and auditable",
                "Email processing must handle structured and unstructured formats",
                "Chat interface must maintain conversation context"
            ]
        },
        'enforcement_hooks': [
            'database_consistency_validator',
            'performance_monitoring', 
            'security_compliance_check',
            'integration_health_monitor',
            'data_sovereignty_enforcer'
        ]
    }
    
    # Process each domain
    total_systems = 0
    for domain_name, domain_files in domains_structure.items():
        print(f"📋 Processing domain: {domain_name}")
        
        domain_data = {}
        domain_description = ""
        domain_includes = []
        domain_services = []
        domain_endpoints = []
        
        # Process each file in the domain
        for system_name, filepath in domain_files.items():
            print(f"  📄 Loading: {system_name} ({filepath})")
            
            if not os.path.exists(filepath):
                print(f"  ⚠️  File not found: {filepath}")
                continue
                
            system_data = load_system_file(filepath)
            if system_data:
                if not validate_system_definition(system_data, filepath):
                    print(f"  ⚠️  Invalid system definition: {filepath}")
                    continue
                
                # Add metadata
                system_data['domain'] = domain_name
                system_data['subdomain'] = system_name if system_name != 'main' else None
                system_data['path'] = filepath
                system_data['file_size_kb'] = round(os.path.getsize(filepath) / 1024, 2)
                
                # Extract domain-level information from main file
                if system_name == 'main':
                    domain_description = system_data.get('description', f"{domain_name.title()} domain")
                    
                    # Try to extract API endpoints, services, etc.
                    if 'technical_assertions' in system_data:
                        tech_assertions = system_data['technical_assertions']
                        if isinstance(tech_assertions, list):
                            # Handle list format (Villiers.ai format)
                            for item in tech_assertions:
                                if isinstance(item, dict) and 'path' in item:
                                    if item['path'].startswith('/api/'):
                                        domain_endpoints.append(item['path'])
                                    elif 'service' in item['path']:
                                        domain_services.append(os.path.basename(item['path']))
                        elif isinstance(tech_assertions, dict):
                            # Handle dictionary format (original SEQ1 format)
                            for section_name, section_data in tech_assertions.items():
                                if isinstance(section_data, list):
                                    for item in section_data:
                                        if isinstance(item, dict) and 'path' in item:
                                            if item['path'].startswith('/api/'):
                                                domain_endpoints.append(item['path'])
                                            elif 'service' in item['path']:
                                                domain_services.append(os.path.basename(item['path']))
                
                # Add to domain data
                domain_data[system_name] = system_data
                total_systems += 1
                print(f"    ✅ Added to index")
            else:
                print(f"    ❌ Failed to load")
        
        # Add domain to index structure
        if domain_data:
            # Calculate domain metrics
            domain_metrics = calculate_domain_metrics(domain_data)
            index['domain_metrics'][domain_name] = domain_metrics
            
            # Add to core domains summary
            index['structure']['core_domains'][domain_name] = {
                'description': domain_description,
                'systems_count': len(domain_data),
                'has_main_system': 'main' in domain_data,
                'subdomains': [k for k in domain_data.keys() if k != 'main'],
                'primary_services': list(set(domain_services)),
                'api_endpoints': list(set(domain_endpoints))
            }
            
            # Add full domain data
            index['domains'][domain_name] = domain_data
    
    # Update metadata with final counts
    index['metadata']['total_systems'] = total_systems
    index['metadata']['domains_processed'] = len(domains_structure)
    
    return index

def generate_sha256(content):
    """Generate SHA256 hash of content"""
    if isinstance(content, dict):
        content = yaml.dump(content, default_flow_style=False, sort_keys=True)
    return hashlib.sha256(content.encode('utf-8')).hexdigest()

def validate_index_structure(index):
    """Validate the generated index structure"""
    required_sections = ['system_intent', 'metadata', 'structure', 'domains', 'constraints']
    missing_sections = [section for section in required_sections if section not in index]
    
    if missing_sections:
        logger.error(f"Missing required sections in index: {missing_sections}")
        return False
    
    if not index['domains']:
        logger.error("No domains found in index")
        return False
    
    logger.info(f"Index validation passed: {len(index['domains'])} domains, {index['metadata']['total_systems']} systems")
    return True

def main():
    print("🔧 VILLIERS.AI SYSTEM DEFINITIONS INDEX REBUILD")
    print("=" * 50)
    print()
    
    # Change to script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Backup existing index
    print("STEP 1: INDEX BACKUP")
    print("-" * 20)
    backup_path = backup_index_file()
    if backup_path:
        print(f"📦 Index backed up to: {backup_path}")
    print()
    
    # Rebuild index
    print("STEP 2: INDEX REBUILD")
    print("-" * 25)
    index = rebuild_villiers_index()
    
    print()
    print("STEP 3: SAVING CANONICAL INDEX")
    print("=" * 35)
    
    # Validate index structure
    if not validate_index_structure(index):
        print("❌ Index validation failed - aborting")
        return False
    
    # Save index
    try:
        with open('index.yaml', 'w', encoding='utf-8') as f:
            yaml.dump(index, f, default_flow_style=False, allow_unicode=True, sort_keys=False, width=120)
        print("✅ Saved index.yaml")
    except Exception as e:
        print(f"❌ Failed to save index.yaml: {e}")
        return False
    
    # Generate SHA256
    try:
        with open('index.yaml', 'r', encoding='utf-8') as f:
            content = f.read()
        sha256_hash = generate_sha256(content)
        
        with open('index.sha256', 'w', encoding='utf-8') as f:
            f.write(f"{sha256_hash}  index.yaml\n")
        print(f"✅ Generated SHA256: {sha256_hash}")
    except Exception as e:
        print(f"❌ Failed to generate SHA256: {e}")
        return False
    
    print()
    print("📊 REBUILD SUMMARY")
    print("=" * 20)
    print(f"Domains processed: {index['metadata']['domains_processed']}")
    print(f"Total systems: {index['metadata']['total_systems']}")
    print(f"Generated at: {index['metadata']['generated']}")
    print(f"SHA256: {sha256_hash}")
    
    # Display domain breakdown
    print("\n🏗️ DOMAIN BREAKDOWN")
    print("-" * 20)
    for domain_name, metrics in index['domain_metrics'].items():
        print(f"{domain_name:15} | {metrics['systems_count']:2} systems | {metrics['total_size_kb']:6.1f} KB | {'Main+' if metrics['has_main_system'] else 'Sub-'}{metrics['subdomain_count']} sub")
    
    print()
    print("🎯 VILLIERS.AI INDEX REBUILD COMPLETED SUCCESSFULLY")
    print("Domain-driven system definitions indexed and validated")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 