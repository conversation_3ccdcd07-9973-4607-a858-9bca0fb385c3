'use client';

import { useState, useEffect, useRef } from 'react';
import { FileContent } from '@/lib/types';
import { SyntaxHighlighter } from './syntax-highlighter';
import { MarkdownRenderer } from './markdown-renderer';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Loader2, FileText, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FileViewerProps {
  filePath?: string;
  className?: string;
}

function getFileIcon(fileType: string) {
  switch (fileType) {
    case 'markdown':
      return '📝';
    case 'yaml':
      return '⚙️';
    case 'python':
      return '🐍';
    default:
      return '📄';
  }
}

function getFileTypeColor(fileType: string) {
  switch (fileType) {
    case 'markdown':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'yaml':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
    case 'python':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
}

export function FileViewer({ filePath, className }: FileViewerProps) {
  const [fileContent, setFileContent] = useState<FileContent | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasAttemptedLoad, setHasAttemptedLoad] = useState(false);
  const abortControllerRef = useRef<AbortController | null>(null);

  useEffect(() => {
    if (!filePath) {
      setFileContent(null);
      setError(null);
      setHasAttemptedLoad(false);
      return;
    }

    // Cancel any pending request
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    const loadFile = async () => {
      // Create new abort controller for this request
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      setLoading(true);
      setError(null);
      setHasAttemptedLoad(true);

      try {
        const response = await fetch(`/api/files/${encodeURIComponent(filePath)}`, {
          signal: abortController.signal
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`Failed to load file ${filePath}:`, response.status, response.statusText, errorText);
          throw new Error(`Failed to load file: ${response.statusText}`);
        }

        const data = await response.json();

        // Only update state if this request wasn't aborted
        if (!abortController.signal.aborted) {
          setFileContent(data);
        }
      } catch (err) {
        // Only update error state if this request wasn't aborted
        if (!abortController.signal.aborted) {
          console.error(`Error loading file ${filePath}:`, err);
          setError(err instanceof Error ? err.message : 'Failed to load file');
          setFileContent(null);
        }
      } finally {
        // Only update loading state if this request wasn't aborted
        if (!abortController.signal.aborted) {
          setLoading(false);
        }
      }
    };

    // Load file immediately for better UX
    loadFile();

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [filePath]);

  if (!filePath) {
    return (
      <div className={cn('flex items-center justify-center h-full', className)}>
        <div className="text-center">
          <FileText className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Welcome to Documentation Viewer</h3>
          <p className="text-muted-foreground max-w-md">
            Select a file from the sidebar to view its content. You can browse through
            Markdown, YAML, and Python files with proper formatting and syntax highlighting.
          </p>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className={cn('flex items-center justify-center h-full', className)}>
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Loading file...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('flex items-center justify-center h-full', className)}>
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Error Loading File</h3>
          <p className="text-muted-foreground max-w-md">{error}</p>
        </div>
      </div>
    );
  }

  if (!fileContent && hasAttemptedLoad && !loading) {
    return (
      <div className={cn('flex items-center justify-center h-full', className)}>
        <div className="text-center">
          <AlertCircle className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">File Not Found</h3>
          <p className="text-muted-foreground max-w-md">
            The requested file could not be found or loaded.
          </p>
        </div>
      </div>
    );
  }

  // Show loading state if we don't have content yet but are loading or haven't attempted load
  if (!fileContent) {
    return (
      <div className={cn('flex items-center justify-center h-full', className)}>
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-4" />
          <p className="text-muted-foreground">Loading file...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('h-full flex flex-col', className)}>
      {/* File Header */}
      <div className="flex-shrink-0 p-6 border-b bg-muted/30">
        <div className="flex items-center gap-3 mb-3">
          <span className="text-2xl">{getFileIcon(fileContent.type)}</span>
          <div className="flex-1 min-w-0">
            <h1 className="text-2xl font-bold truncate">{fileContent.name}</h1>
            <p className="text-sm text-muted-foreground truncate">{fileContent.path}</p>
          </div>
          <Badge
            variant="secondary"
            className={cn('text-sm px-3 py-1', getFileTypeColor(fileContent.type))}
          >
            {fileContent.type.toUpperCase()}
          </Badge>
        </div>

        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <span>Size: {formatFileSize(fileContent.size)}</span>
          <Separator orientation="vertical" className="h-4" />
          <span>Modified: {formatDate(new Date(fileContent.lastModified))}</span>
        </div>
      </div>

      {/* File Content */}
      <div className="flex-1 overflow-auto">
        {fileContent.type === 'markdown' ? (
          <div className="p-6 w-full min-w-0">
            <MarkdownRenderer content={fileContent.content} />
          </div>
        ) : (
          <div className="p-6">
            <SyntaxHighlighter
              code={fileContent.content}
              language={fileContent.type}
              filename={fileContent.name}
              showLineNumbers={true}
              showCopyButton={true}
              showDownloadButton={true}
            />
          </div>
        )}
      </div>
    </div>
  );
}
