'use client';

import { useState, useEffect } from 'react';
import { Search, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { FileNode } from '@/lib/types';

interface FileSearchProps {
  onFileSelect: (filePath: string) => void;
  className?: string;
}



function getFileIcon(fileType?: string) {
  switch (fileType) {
    case 'markdown':
      return '📝';
    case 'yaml':
      return '⚙️';
    case 'python':
      return '🐍';
    default:
      return '📄';
  }
}

function getFileTypeColor(fileType?: string) {
  switch (fileType) {
    case 'markdown':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'yaml':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
    case 'python':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
}

export function FileSearch({ onFileSelect, className }: FileSearchProps) {
  const [query, setQuery] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [searchResults, setSearchResults] = useState<FileNode[]>([]);


  // Debounced search effect
  useEffect(() => {
    const timeoutId = setTimeout(async () => {
      if (!query.trim()) {
        setSearchResults([]);
        setIsOpen(false);
        return;
      }

      try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(query.trim())}`);
        if (response.ok) {
          const data = await response.json();
          setSearchResults(data.results || []);
          setIsOpen(true);
        } else {
          setSearchResults([]);
          setIsOpen(false);
        }
      } catch (error) {
        console.error('Search failed:', error);
        setSearchResults([]);
        setIsOpen(false);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timeoutId);
  }, [query]);

  useEffect(() => {
    setIsOpen(query.trim().length > 0);
  }, [query]);

  const handleFileSelect = (filePath: string) => {
    onFileSelect(filePath);
    setQuery('');
    setIsOpen(false);
  };

  const clearSearch = () => {
    setQuery('');
    setIsOpen(false);
  };

  return (
    <div className={cn('relative', className)}>
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          type="text"
          placeholder="Search files..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="pl-10 pr-10"
        />
        {query && (
          <Button
            variant="ghost"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            onClick={clearSearch}
          >
            <X className="h-3 w-3" />
          </Button>
        )}
      </div>

      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-background border rounded-md shadow-lg max-h-80 overflow-y-auto">
          {searchResults.length > 0 ? (
            <div className="p-1">
              {searchResults.map((result) => (
                <div
                  key={result.path}
                  className="flex items-center gap-3 px-3 py-2 text-sm cursor-pointer rounded-md hover:bg-muted transition-colors"
                  onClick={() => handleFileSelect(result.path)}
                >
                  <span className="text-base">{getFileIcon(result.fileType)}</span>
                  <div className="flex-1 min-w-0">
                    <div className="font-medium truncate">{result.name}</div>
                    <div className="text-xs text-muted-foreground truncate">
                      {result.path}
                    </div>
                  </div>
                  {result.fileType && (
                    <Badge
                      variant="secondary"
                      className={cn('text-xs px-1.5 py-0.5', getFileTypeColor(result.fileType))}
                    >
                      {result.fileType}
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="p-4 text-center text-sm text-muted-foreground">
              No files found matching "{query}"
            </div>
          )}
        </div>
      )}
    </div>
  );
}
