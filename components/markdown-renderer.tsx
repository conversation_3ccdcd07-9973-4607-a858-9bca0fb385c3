'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeHighlight from 'rehype-highlight';
import rehypeRaw from 'rehype-raw';
import { SyntaxHighlighter } from './syntax-highlighter';

interface MarkdownRendererProps {
  content: string;
  className?: string;
}

export function MarkdownRenderer({ content, className = '' }: MarkdownRendererProps) {
  return (
    <div
      className={`markdown-content w-full min-w-0 ${className}`}
      style={{
        wordWrap: 'break-word',
        overflowWrap: 'anywhere',
        maxWidth: '100%',
        boxSizing: 'border-box'
      }}
    >
      <ReactMarkdown
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeHighlight, rehypeRaw]}
        components={{
          code({ node, inline, className, children, ...props }: any) {
            const match = /language-(\w+)/.exec(className || '');
            const language = match ? match[1] : '';

            if (!inline && language) {
              // Get the raw text content from the node
              let codeContent = '';

              if (node && node.children && node.children[0] && node.children[0].value) {
                // Get raw text from the AST node
                codeContent = node.children[0].value;
              } else {
                // Fallback: extract text content from React children
                const extractTextContent = (children: any): string => {
                  if (typeof children === 'string') {
                    return children;
                  }
                  if (Array.isArray(children)) {
                    return children.map(extractTextContent).join('');
                  }
                  if (children?.props?.children) {
                    return extractTextContent(children.props.children);
                  }
                  return String(children || '');
                };
                codeContent = extractTextContent(children);
              }

              return (
                <SyntaxHighlighter
                  code={codeContent.replace(/\n$/, '')}
                  language={language}
                  showLineNumbers={true}
                  showCopyButton={true}
                  showDownloadButton={true}
                  maxHeight="none"
                  className="syntax-highlighter-container"
                />
              );
            }

            return (
              <code
                className={`${className} break-all`}
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'anywhere',
                  whiteSpace: 'pre-wrap',
                  maxWidth: '100%'
                }}
                {...props}
              >
                {children}
              </code>
            );
          },
          pre({ children, ...props }) {
            // Check if this pre contains a code element with a language class (syntax highlighted code)
            const hasLanguageCode = React.Children.toArray(children).some((child: any) =>
              child?.props?.className?.includes('language-')
            );

            // If it contains syntax highlighted code, don't add styling (SyntaxHighlighter handles it)
            if (hasLanguageCode) {
              return <pre {...props}>{children}</pre>;
            }

            // For regular pre blocks, add styling
            return (
              <pre
                className="bg-muted rounded-lg p-4 my-4 text-sm font-mono w-full"
                style={{
                  whiteSpace: 'pre-wrap',
                  wordWrap: 'break-word',
                  overflowWrap: 'anywhere',
                  maxWidth: '100%',
                  overflow: 'hidden'
                }}
                {...props}
              >
                {children}
              </pre>
            );
          },
          h1({ children }) {
            return (
              <h1 className="text-3xl font-bold tracking-tight mb-6 pb-2 border-b w-full break-words" style={{ wordWrap: 'break-word', overflowWrap: 'anywhere', maxWidth: '100%' }}>
                {children}
              </h1>
            );
          },
          h2({ children }) {
            return (
              <h2 className="text-2xl font-semibold tracking-tight mb-4 mt-8 pb-2 border-b w-full break-words" style={{ wordWrap: 'break-word', overflowWrap: 'anywhere', maxWidth: '100%' }}>
                {children}
              </h2>
            );
          },
          h3({ children }) {
            return (
              <h3 className="text-xl font-semibold tracking-tight mb-3 mt-6 w-full break-words" style={{ wordWrap: 'break-word', overflowWrap: 'anywhere', maxWidth: '100%' }}>
                {children}
              </h3>
            );
          },
          h4({ children }) {
            return (
              <h4 className="text-lg font-semibold tracking-tight mb-2 mt-4 w-full break-words" style={{ wordWrap: 'break-word', overflowWrap: 'anywhere', maxWidth: '100%' }}>
                {children}
              </h4>
            );
          },
          p({ children }) {
            return <p className="mb-4 leading-7 w-full break-words" style={{ wordWrap: 'break-word', overflowWrap: 'anywhere', maxWidth: '100%' }}>{children}</p>;
          },
          ul({ children }) {
            return (
              <ul
                className="mb-4 list-disc space-y-2 w-full"
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'anywhere',
                  maxWidth: '100%',
                  paddingLeft: '1.5rem',
                  marginLeft: '0'
                }}
              >
                {children}
              </ul>
            );
          },
          ol({ children }) {
            return (
              <ol
                className="mb-4 list-decimal space-y-2 w-full"
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'anywhere',
                  maxWidth: '100%',
                  paddingLeft: '1.5rem',
                  marginLeft: '0'
                }}
              >
                {children}
              </ol>
            );
          },
          li({ children }) {
            return (
              <li
                className="leading-7 break-words w-full"
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'anywhere',
                  maxWidth: '100%',
                  whiteSpace: 'normal'
                }}
              >
                {children}
              </li>
            );
          },
          blockquote({ children }) {
            return (
              <blockquote className="border-l-4 border-muted-foreground/25 pl-4 italic my-4 break-words w-full" style={{ wordWrap: 'break-word', overflowWrap: 'anywhere', maxWidth: '100%' }}>
                {children}
              </blockquote>
            );
          },
          table({ children }) {
            return (
              <div className="my-4 w-full" style={{ overflowX: 'auto', maxWidth: '100%' }}>
                <table className="border-collapse border border-muted-foreground/25" style={{ width: '100%', tableLayout: 'auto' }}>
                  {children}
                </table>
              </div>
            );
          },
          th({ children }) {
            return (
              <th className="border border-muted-foreground/25 px-4 py-2 bg-muted font-semibold text-left break-words" style={{ wordWrap: 'break-word', overflowWrap: 'anywhere' }}>
                {children}
              </th>
            );
          },
          td({ children }) {
            return (
              <td className="border border-muted-foreground/25 px-4 py-2 break-words" style={{ wordWrap: 'break-word', overflowWrap: 'anywhere' }}>
                {children}
              </td>
            );
          },
          strong({ children }) {
            return (
              <strong
                className="font-bold break-words"
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'anywhere',
                  whiteSpace: 'normal'
                }}
              >
                {children}
              </strong>
            );
          },
          em({ children }) {
            return (
              <em
                className="italic break-words"
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'anywhere',
                  whiteSpace: 'normal'
                }}
              >
                {children}
              </em>
            );
          },
          a({ href, children }) {
            return (
              <a
                href={href}
                className="text-primary hover:underline break-words"
                style={{
                  wordWrap: 'break-word',
                  overflowWrap: 'anywhere',
                  whiteSpace: 'normal'
                }}
                target={href?.startsWith('http') ? '_blank' : undefined}
                rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
              >
                {children}
              </a>
            );
          },
          img({ src, alt }) {
            return (
              <img
                src={src}
                alt={alt}
                className="max-w-full h-auto rounded-lg shadow-sm my-4"
              />
            );
          },
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
}
