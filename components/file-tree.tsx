'use client';

import { useState } from 'react';
import { ChevronRight, ChevronDown, Folder, FolderOpen } from 'lucide-react';
import { cn } from '@/lib/utils';
import { FileNode } from '@/lib/types';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface FileTreeProps {
  nodes: FileNode[];
  selectedFile?: string;
  onFileSelect: (filePath: string) => void;
  className?: string;
}

interface FileTreeNodeProps {
  node: FileNode;
  selectedFile?: string;
  onFileSelect: (filePath: string) => void;
  level?: number;
}

function getFileIcon(fileType?: string) {
  switch (fileType) {
    case 'markdown':
      return '📝';
    case 'yaml':
      return '⚙️';
    case 'python':
      return '🐍';
    default:
      return '📄';
  }
}

function getFileTypeColor(fileType?: string) {
  switch (fileType) {
    case 'markdown':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'yaml':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300';
    case 'python':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
}

function FileTreeNode({ node, selectedFile, onFileSelect, level = 0 }: FileTreeNodeProps) {
  const [isOpen, setIsOpen] = useState(level < 2); // Auto-expand first two levels
  const isSelected = selectedFile === node.path;
  const hasChildren = node.children && node.children.length > 0;

  if (node.type === 'file') {
    return (
      <div
        className={cn(
          'flex gap-2 px-2 py-1.5 text-sm cursor-pointer rounded-md transition-colors',
          'hover:bg-muted/50',
          isSelected && 'bg-muted text-foreground font-medium',
          !isSelected && 'text-muted-foreground hover:text-foreground'
        )}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        onClick={() => onFileSelect(node.path)}
      >
        <span className="text-base flex-shrink-0">{getFileIcon(node.fileType)}</span>
        <div className="flex-1 overflow-hidden">
          <div className="flex flex-wrap items-center gap-2">
            <span className="break-all leading-tight">{node.name}</span>
            {node.fileType && (
              <Badge
                variant="secondary"
                className={cn('text-xs px-1.5 py-0.5 flex-shrink-0', getFileTypeColor(node.fileType))}
              >
                {node.fileType}
              </Badge>
            )}
          </div>
        </div>
      </div>
    );
  }

  return (
    <Collapsible open={isOpen} onOpenChange={setIsOpen}>
      <CollapsibleTrigger asChild>
        <div
          className={cn(
            'flex gap-2 px-2 py-1.5 text-sm cursor-pointer rounded-md transition-colors',
            'hover:bg-muted/50 text-foreground'
          )}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
        >
          {hasChildren ? (
            isOpen ? (
              <ChevronDown className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            ) : (
              <ChevronRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
            )
          ) : (
            <div className="h-4 w-4 flex-shrink-0" />
          )}
          {isOpen ? (
            <FolderOpen className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
          ) : (
            <Folder className="h-4 w-4 text-blue-600 dark:text-blue-400 flex-shrink-0" />
          )}
          <div className="flex-1 overflow-hidden">
            <div className="flex flex-wrap items-center gap-2">
              <span className="break-all font-medium leading-tight">{node.name}</span>
              {hasChildren && (
                <Badge variant="outline" className="text-xs px-1.5 py-0.5 flex-shrink-0">
                  {node.children!.length}
                </Badge>
              )}
            </div>
          </div>
        </div>
      </CollapsibleTrigger>

      {hasChildren && (
        <CollapsibleContent className="space-y-0.5">
          {node.children!.map((child) => (
            <FileTreeNode
              key={child.path}
              node={child}
              selectedFile={selectedFile}
              onFileSelect={onFileSelect}
              level={level + 1}
            />
          ))}
        </CollapsibleContent>
      )}
    </Collapsible>
  );
}

export function FileTree({ nodes, selectedFile, onFileSelect, className }: FileTreeProps) {
  return (
    <div className={cn('space-y-0.5', className)}>
      {nodes.map((node) => (
        <FileTreeNode
          key={node.path}
          node={node}
          selectedFile={selectedFile}
          onFileSelect={onFileSelect}
        />
      ))}
    </div>
  );
}
