'use client';

import { useState, useCallback, useMemo, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Copy,
  Check,
  WrapText,
  ScrollText,
  Download,
  AlertTriangle,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';

// Performance constants
const MAX_LINES_FOR_SYNTAX_HIGHLIGHTING = 1000;
const MAX_FILE_SIZE_FOR_SYNTAX_HIGHLIGHTING = 100 * 1024; // 100KB

interface SyntaxHighlighterProps {
  code: string;
  language: string;
  filename?: string;
  showLineNumbers?: boolean;
  showCopyButton?: boolean;
  showDownloadButton?: boolean;
  className?: string;
  maxHeight?: string | 'none';
}

// Lightweight syntax highlighter component that avoids memory leaks
export function SyntaxHighlighter({
  code,
  language,
  filename,
  showLineNumbers = true,
  showCopyButton = true,
  showDownloadButton = true,
  className,
  maxHeight = '600px',
}: SyntaxHighlighterProps) {
  const [copied, setCopied] = useState(false);
  const [wordWrap, setWordWrap] = useState(false);
  const [useSyntaxHighlighting, setUseSyntaxHighlighting] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const componentRef = useRef<HTMLDivElement>(null);

  // Memoize the code lines for performance
  const codeLines = useMemo(() => code.split('\n'), [code]);
  const totalLines = codeLines.length;
  const codeSize = useMemo(() => new Blob([code]).size, [code]);

  // Determine if we should use syntax highlighting based on performance constraints
  const shouldUseSyntaxHighlighting = useMemo(() => {
    return (
      totalLines <= MAX_LINES_FOR_SYNTAX_HIGHLIGHTING &&
      codeSize <= MAX_FILE_SIZE_FOR_SYNTAX_HIGHLIGHTING &&
      useSyntaxHighlighting
    );
  }, [totalLines, codeSize, useSyntaxHighlighting]);

  // Cleanup effect to prevent memory leaks
  useEffect(() => {
    return () => {
      // Cleanup any pending timeouts
      if (copied) {
        setCopied(false);
      }
    };
  }, [copied]);

  // Handle copy to clipboard
  const handleCopy = useCallback(async () => {
    try {
      await navigator.clipboard.writeText(code);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy code:', err);
      // Fallback for older browsers
      const textArea = document.createElement('textarea');
      textArea.value = code;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand('copy');
      document.body.removeChild(textArea);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  }, [code]);

  // Handle download
  const handleDownload = useCallback(() => {
    const blob = new Blob([code], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename || `code.${getFileExtension(language)}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [code, filename, language]);

  // Toggle word wrap
  const toggleWordWrap = useCallback(() => {
    setWordWrap(prev => !prev);
  }, []);

  // Toggle syntax highlighting
  const toggleSyntaxHighlighting = useCallback(() => {
    setUseSyntaxHighlighting(prev => !prev);
  }, []);

  return (
    <div ref={componentRef} className={cn('relative group', className)}>
      {/* Header with controls */}
      <div className="flex items-center justify-between bg-muted/50 border border-border rounded-t-lg px-4 py-2">
        <div className="flex items-center gap-2">
          {filename && (
            <span className="text-sm font-medium text-foreground">
              {filename}
            </span>
          )}
          <span className="text-xs text-muted-foreground">
            {language.toUpperCase()} • {totalLines} line{totalLines !== 1 ? 's' : ''} • {formatFileSize(codeSize)}
          </span>
          {!shouldUseSyntaxHighlighting && (
            <span className="text-xs text-amber-600 dark:text-amber-400 flex items-center gap-1">
              <AlertTriangle className="h-3 w-3" />
              Plain text mode
            </span>
          )}
        </div>

        <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
          {(totalLines > MAX_LINES_FOR_SYNTAX_HIGHLIGHTING || codeSize > MAX_FILE_SIZE_FOR_SYNTAX_HIGHLIGHTING) && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={toggleSyntaxHighlighting}
                  className="h-7 w-7 p-0!"
                >
                  {useSyntaxHighlighting ? (
                    <AlertTriangle className="h-3.5 w-3.5 text-amber-500" />
                  ) : (
                    <AlertTriangle className="h-3.5 w-3.5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {useSyntaxHighlighting ? 'Disable syntax highlighting (performance)' : 'Enable syntax highlighting'}
              </TooltipContent>
            </Tooltip>
          )}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleWordWrap}
                className="h-7 w-7 p-0!"
              >
                {wordWrap ? (
                  <ScrollText className="h-3.5 w-3.5" />
                ) : (
                  <WrapText className="h-3.5 w-3.5" />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {wordWrap ? 'Disable word wrap' : 'Enable word wrap'}
            </TooltipContent>
          </Tooltip>

          {showDownloadButton && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleDownload}
                  className="h-7 w-7 p-0!"
                >
                  <Download className="h-3.5 w-3.5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Download file</TooltipContent>
            </Tooltip>
          )}

          {showCopyButton && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleCopy}
                  className="h-7 w-7 p-0!"
                >
                  {copied ? (
                    <Check className="h-3.5 w-3.5 text-green-600" />
                  ) : (
                    <Copy className="h-3.5 w-3.5" />
                  )}
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                {copied ? 'Copied!' : 'Copy to clipboard'}
              </TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>

      {/* Code content */}
      <div className="relative border border-t-0 border-border rounded-b-lg overflow-hidden">
        <div
          className="overflow-auto"
          style={{ maxHeight: maxHeight === 'none' ? undefined : maxHeight }}
        >
          {shouldUseSyntaxHighlighting ? (
            <SyntaxHighlightedCode
              code={code}
              language={language}
              showLineNumbers={showLineNumbers}
              wordWrap={wordWrap}
              onLoadingChange={setIsLoading}
            />
          ) : (
            <PlainTextCode
              code={code}
              showLineNumbers={showLineNumbers}
              wordWrap={wordWrap}
            />
          )}

          {isLoading && (
            <div className="absolute inset-0 bg-background/80 flex items-center justify-center">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading syntax highlighting...
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// Plain text code component for large files
function PlainTextCode({
  code,
  showLineNumbers,
  wordWrap
}: {
  code: string;
  showLineNumbers: boolean;
  wordWrap: boolean;
}) {
  const lines = useMemo(() => code.split('\n'), [code]);

  return (
    <div className="flex bg-background">
      {showLineNumbers && (
        <div className="bg-muted/30 px-3 py-4 text-xs text-muted-foreground select-none font-mono border-r border-border/50 min-w-[3rem]">
          {lines.map((_, index) => (
            <div
              key={index}
              className="text-right leading-[1.4] h-[15.4px] flex items-center justify-end"
            >
              {index + 1}
            </div>
          ))}
        </div>
      )}
      <div className="flex-1 overflow-x-auto">
        <pre
          className={`p-4 text-sm font-mono leading-[1.4] ${wordWrap ? 'whitespace-pre-wrap' : 'whitespace-pre'
            }`}
          style={{
            fontFamily: 'var(--font-geist-mono), ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
          }}
        >
          {code}
        </pre>
      </div>
    </div>
  );
}

// Lazy-loaded syntax highlighted code component
function SyntaxHighlightedCode({
  code,
  language,
  showLineNumbers,
  wordWrap,
  onLoadingChange
}: {
  code: string;
  language: string;
  showLineNumbers: boolean;
  wordWrap: boolean;
  onLoadingChange: (loading: boolean) => void;
}) {
  const [highlighter, setHighlighter] = useState<any>(null);
  const [styles, setStyles] = useState<any>(null);
  const [error, setError] = useState(false);

  useEffect(() => {
    let isMounted = true;

    const loadHighlighter = async () => {
      try {
        onLoadingChange(true);

        // Dynamic import to avoid loading heavy library until needed
        const [highlighterModule, stylesModule] = await Promise.all([
          import('react-syntax-highlighter').then(m => m.Prism),
          import('react-syntax-highlighter/dist/esm/styles/prism').then(m => ({
            oneLight: m.oneLight,
            oneDark: m.oneDark
          }))
        ]);

        if (isMounted) {
          setHighlighter(() => highlighterModule);
          setStyles(stylesModule);
          setError(false);
        }
      } catch (err) {
        console.error('Failed to load syntax highlighter:', err);
        if (isMounted) {
          setError(true);
        }
      } finally {
        if (isMounted) {
          onLoadingChange(false);
        }
      }
    };

    loadHighlighter();

    return () => {
      isMounted = false;
    };
  }, [onLoadingChange]);

  // Cleanup effect
  useEffect(() => {
    return () => {
      // Force cleanup of any syntax highlighter instances
      setHighlighter(null);
      setStyles(null);
    };
  }, []);

  if (error || !highlighter || !styles) {
    return (
      <PlainTextCode
        code={code}
        showLineNumbers={showLineNumbers}
        wordWrap={wordWrap}
      />
    );
  }

  const HighlighterComponent = highlighter;

  return (
    <HighlighterComponent
      language={normalizeLanguage(language)}
      style={styles.oneLight}
      showLineNumbers={showLineNumbers}
      wrapLines={wordWrap}
      wrapLongLines={wordWrap}
      customStyle={{
        margin: 0,
        padding: '1rem',
        fontSize: '11px',
        lineHeight: '1.4',
        fontFamily: 'var(--font-geist-mono), ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace',
        background: 'transparent',
      }}
      lineNumberStyle={{
        minWidth: '3em',
        paddingRight: '1em',
        color: 'var(--muted-foreground)',
        fontSize: '11px',
        userSelect: 'none',
      }}
      codeTagProps={{
        style: {
          fontSize: '11px',
          fontFamily: 'inherit',
        }
      }}
    >
      {code}
    </HighlighterComponent>
  );
}

// Helper function to get file extension from language
function getFileExtension(language: string): string {
  const extensions: Record<string, string> = {
    javascript: 'js',
    typescript: 'ts',
    python: 'py',
    yaml: 'yml',
    json: 'json',
    html: 'html',
    css: 'css',
    scss: 'scss',
    markdown: 'md',
    bash: 'sh',
    shell: 'sh',
    sql: 'sql',
    xml: 'xml',
    php: 'php',
    java: 'java',
    csharp: 'cs',
    cpp: 'cpp',
    c: 'c',
    go: 'go',
    rust: 'rs',
    ruby: 'rb',
    swift: 'swift',
    kotlin: 'kt',
    dart: 'dart',
  };
  return extensions[language.toLowerCase()] || 'txt';
}

// Helper function to normalize language names for react-syntax-highlighter
function normalizeLanguage(language: string): string {
  const languageMap: Record<string, string> = {
    yml: 'yaml',
    js: 'javascript',
    ts: 'typescript',
    py: 'python',
    sh: 'bash',
    md: 'markdown',
    cs: 'csharp',
    kt: 'kotlin',
  };
  return languageMap[language.toLowerCase()] || language.toLowerCase();
}
